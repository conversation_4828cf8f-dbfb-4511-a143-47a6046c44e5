{"version": 3, "names": ["React", "useEffect", "Animated", "Image", "Platform", "StyleSheet", "UIManager", "View", "Freeze", "version", "TransitionProgressContext", "useTransitionProgress", "isSearchBarAvailableForCurrentPlatform", "isNewBackTitleImplementation", "executeNativeBackPress", "isPlatformSupported", "OS", "ENABLE_SCREENS", "enableScreens", "shouldEnableScreens", "arguments", "length", "undefined", "getViewManagerConfig", "console", "error", "ENABLE_FREEZE", "enableFreeze", "shouldEnableReactFreeze", "minor", "parseInt", "split", "warn", "shouldUseActivityState", "screensEnabled", "NativeScreenValue", "NativeScreenContainerValue", "NativeScreenNavigationContainerValue", "NativeScreenStack", "NativeScreenStackHeaderConfig", "NativeScreenStackHeaderSubview", "AnimatedNativeScreen", "NativeSearchBar", "NativeSearchBarCommands", "NativeFullWindowOverlay", "ScreensNativeModules", "NativeScreen", "require", "default", "NativeScreenContainer", "NativeScreenNavigationContainer", "Commands", "DelayedFreeze", "_ref", "freeze", "children", "freezeState", "setFreezeState", "useState", "id", "setImmediate", "clearImmediate", "createElement", "ScreenStack", "props", "rest", "size", "Children", "count", "childrenWithFreeze", "map", "child", "index", "_props$descriptor", "_props$descriptors", "_descriptor$options$f", "_descriptor$options", "key", "descriptor", "descriptors", "freezeEnabled", "options", "freezeOnBlur", "InnerScreen", "Component", "constructor", "_defineProperty", "Value", "ref", "_this$props$onCompone", "_this$props", "onComponentRef", "call", "setNativeProps", "_this$ref", "render", "enabled", "sheetAllowedDetents", "sheetLargestUndimmedDetent", "sheetGrabberVisible", "sheetCornerRadius", "sheetExpandsWhenScrolledToEdge", "_gestureResponseDista", "_gestureResponseDista2", "_gestureResponseDista3", "_gestureResponseDista4", "createAnimatedComponent", "active", "activityState", "isNativeStack", "gestureResponseDistance", "onGestureCancel", "handleRef", "_ref$viewConfig", "viewConfig", "validAttributes", "style", "display", "setRef", "_extends", "start", "end", "top", "bottom", "onTransitionProgress", "event", "nativeEvent", "progress", "closing", "goingForward", "useNativeDriver", "Provider", "value", "ScreenContainer", "hasTwoStates", "FullWindowOverlay", "position", "width", "height", "styles", "create", "headerSubview", "right", "flexDirection", "alignItems", "justifyContent", "ScreenStackHeaderBackButtonImage", "type", "resizeMode", "fadeDuration", "SearchBar", "nativeSearchBarRef", "createRef", "_callMethodWithRef", "method", "current", "blur", "focus", "toggleCancelButton", "flag", "clearText", "setText", "text", "ScreenStackHeaderRightView", "ScreenStackHeaderLeftView", "ScreenStackHeaderCenterView", "ScreenStackHeaderSearchBarView", "ScreenContext", "createContext", "Screen", "ScreenWrapper", "context", "module", "exports", "ScreenStackHeaderConfig", "ScreenStackHeaderSubview", "SearchBarCommands"], "sources": ["index.native.tsx"], "sourcesContent": ["/* eslint-disable @typescript-eslint/no-var-requires */\nimport React, { useEffect, PropsWithChildren, ReactNode } from 'react';\nimport {\n  Animated,\n  Image,\n  ImageProps,\n  Platform,\n  StyleProp,\n  StyleSheet,\n  UIManager,\n  View,\n  ViewProps,\n  ViewStyle,\n} from 'react-native';\nimport { Freeze } from 'react-freeze';\nimport { version } from 'react-native/package.json';\n\nimport TransitionProgressContext from './TransitionProgressContext';\nimport useTransitionProgress from './useTransitionProgress';\nimport {\n  StackPresentationTypes,\n  StackAnimationTypes,\n  BlurEffectTypes,\n  ScreenReplaceTypes,\n  ScreenOrientationTypes,\n  HeaderSubviewTypes,\n  ScreenProps,\n  ScreenContainerProps,\n  ScreenStackProps,\n  ScreenStackHeaderConfigProps,\n  SearchBarProps,\n  SearchBarCommands,\n} from './types';\nimport {\n  isSearchBarAvailableForCurrentPlatform,\n  isNewBackTitleImplementation,\n  executeNativeBackPress,\n} from './utils';\n\n// web implementation is taken from `index.tsx`\nconst isPlatformSupported =\n  Platform.OS === 'ios' ||\n  Platform.OS === 'android' ||\n  Platform.OS === 'windows';\n\nlet ENABLE_SCREENS = isPlatformSupported;\n\nfunction enableScreens(shouldEnableScreens = true): void {\n  ENABLE_SCREENS = isPlatformSupported && shouldEnableScreens;\n  if (ENABLE_SCREENS && !UIManager.getViewManagerConfig('RNSScreen')) {\n    console.error(\n      `Screen native module hasn't been linked. Please check the react-native-screens README for more details`\n    );\n  }\n}\n\nlet ENABLE_FREEZE = false;\n\nfunction enableFreeze(shouldEnableReactFreeze = true): void {\n  const minor = parseInt(version.split('.')[1]); // eg. takes 66 from '0.66.0'\n\n  // react-freeze requires react-native >=0.64, react-native from main is 0.0.0\n  if (!(minor === 0 || minor >= 64) && shouldEnableReactFreeze) {\n    console.warn(\n      'react-freeze library requires at least react-native 0.64. Please upgrade your react-native version in order to use this feature.'\n    );\n  }\n\n  ENABLE_FREEZE = shouldEnableReactFreeze;\n}\n\n// const that tells if the library should use new implementation, will be undefined for older versions\nconst shouldUseActivityState = true;\n\nfunction screensEnabled(): boolean {\n  return ENABLE_SCREENS;\n}\n\ntype SearchBarCommandsType = {\n  blur: (\n    viewRef: React.ElementRef<typeof ScreensNativeModules.NativeSearchBar>\n  ) => void;\n  focus: (\n    viewRef: React.ElementRef<typeof ScreensNativeModules.NativeSearchBar>\n  ) => void;\n  clearText: (\n    viewRef: React.ElementRef<typeof ScreensNativeModules.NativeSearchBar>\n  ) => void;\n  toggleCancelButton: (\n    viewRef: React.ElementRef<typeof ScreensNativeModules.NativeSearchBar>,\n    flag: boolean\n  ) => void;\n  setText: (\n    viewRef: React.ElementRef<typeof ScreensNativeModules.NativeSearchBar>,\n    text: string\n  ) => void;\n};\n\n// We initialize these lazily so that importing the module doesn't throw error when not linked\n// This is necessary coz libraries such as React Navigation import the library where it may not be enabled\nlet NativeScreenValue: React.ComponentType<ScreenProps>;\nlet NativeScreenContainerValue: React.ComponentType<ScreenContainerProps>;\nlet NativeScreenNavigationContainerValue: React.ComponentType<ScreenContainerProps>;\nlet NativeScreenStack: React.ComponentType<ScreenStackProps>;\nlet NativeScreenStackHeaderConfig: React.ComponentType<ScreenStackHeaderConfigProps>;\nlet NativeScreenStackHeaderSubview: React.ComponentType<\n  React.PropsWithChildren<ViewProps & { type?: HeaderSubviewTypes }>\n>;\nlet AnimatedNativeScreen: React.ComponentType<ScreenProps>;\n\nlet NativeSearchBar: React.ComponentType<SearchBarProps> &\n  typeof NativeSearchBarCommands;\nlet NativeSearchBarCommands: SearchBarCommandsType;\n\nlet NativeFullWindowOverlay: React.ComponentType<\n  PropsWithChildren<{\n    style: StyleProp<ViewStyle>;\n  }>\n>;\n\nconst ScreensNativeModules = {\n  get NativeScreen() {\n    NativeScreenValue =\n      NativeScreenValue || require('./fabric/ScreenNativeComponent').default;\n    return NativeScreenValue;\n  },\n\n  get NativeScreenContainer() {\n    NativeScreenContainerValue =\n      NativeScreenContainerValue ||\n      require('./fabric/ScreenContainerNativeComponent').default;\n    return NativeScreenContainerValue;\n  },\n\n  get NativeScreenNavigationContainer() {\n    NativeScreenNavigationContainerValue =\n      NativeScreenNavigationContainerValue ||\n      (Platform.OS === 'ios'\n        ? require('./fabric/ScreenNavigationContainerNativeComponent').default\n        : this.NativeScreenContainer);\n    return NativeScreenNavigationContainerValue;\n  },\n\n  get NativeScreenStack() {\n    NativeScreenStack =\n      NativeScreenStack ||\n      require('./fabric/ScreenStackNativeComponent').default;\n    return NativeScreenStack;\n  },\n\n  get NativeScreenStackHeaderConfig() {\n    NativeScreenStackHeaderConfig =\n      NativeScreenStackHeaderConfig ||\n      require('./fabric/ScreenStackHeaderConfigNativeComponent').default;\n    return NativeScreenStackHeaderConfig;\n  },\n\n  get NativeScreenStackHeaderSubview() {\n    NativeScreenStackHeaderSubview =\n      NativeScreenStackHeaderSubview ||\n      require('./fabric/ScreenStackHeaderSubviewNativeComponent').default;\n    return NativeScreenStackHeaderSubview;\n  },\n\n  get NativeSearchBar() {\n    NativeSearchBar =\n      NativeSearchBar || require('./fabric/SearchBarNativeComponent').default;\n    return NativeSearchBar;\n  },\n\n  get NativeSearchBarCommands() {\n    NativeSearchBarCommands =\n      NativeSearchBarCommands ||\n      require('./fabric/SearchBarNativeComponent').Commands;\n    return NativeSearchBarCommands;\n  },\n\n  get NativeFullWindowOverlay() {\n    NativeFullWindowOverlay =\n      NativeFullWindowOverlay ||\n      require('./fabric/FullWindowOverlayNativeComponent').default;\n    return NativeFullWindowOverlay;\n  },\n};\n\ninterface FreezeWrapperProps {\n  freeze: boolean;\n  children: React.ReactNode;\n}\n\n// This component allows one more render before freezing the screen.\n// Allows activityState to reach the native side and useIsFocused to work correctly.\nfunction DelayedFreeze({ freeze, children }: FreezeWrapperProps) {\n  // flag used for determining whether freeze should be enabled\n  const [freezeState, setFreezeState] = React.useState(false);\n\n  useEffect(() => {\n    const id = setImmediate(() => {\n      setFreezeState(freeze);\n    });\n    return () => {\n      clearImmediate(id);\n    }\n  }, [freeze])\n\n  return <Freeze freeze={freeze ? freezeState : false}>{children}</Freeze>;\n}\n\nfunction ScreenStack(props: ScreenStackProps) {\n  const { children, ...rest } = props;\n  const size = React.Children.count(children);\n  // freezes all screens except the top one\n  const childrenWithFreeze = React.Children.map(children, (child, index) => {\n    // @ts-expect-error it's either SceneView in v6 or RouteView in v5\n    const { props, key } = child;\n    const descriptor = props?.descriptor ?? props?.descriptors?.[key];\n    const freezeEnabled = descriptor?.options?.freezeOnBlur ?? ENABLE_FREEZE;\n\n    return (\n      <DelayedFreeze freeze={freezeEnabled && size - index > 1}>\n        {child}\n      </DelayedFreeze>\n    );\n  });\n\n  return (\n    <ScreensNativeModules.NativeScreenStack {...rest}>\n      {childrenWithFreeze}\n    </ScreensNativeModules.NativeScreenStack>\n  );\n}\n\n// Incomplete type, all accessible properties available at:\n// react-native/Libraries/Components/View/ReactNativeViewViewConfig.js\ninterface ViewConfig extends View {\n  viewConfig: {\n    validAttributes: {\n      style: {\n        display: boolean;\n      };\n    };\n  };\n}\n\nclass InnerScreen extends React.Component<ScreenProps> {\n  private ref: React.ElementRef<typeof View> | null = null;\n  private closing = new Animated.Value(0);\n  private progress = new Animated.Value(0);\n  private goingForward = new Animated.Value(0);\n\n  setNativeProps(props: ScreenProps): void {\n    this.ref?.setNativeProps(props);\n  }\n\n  setRef = (ref: React.ElementRef<typeof View> | null): void => {\n    this.ref = ref;\n    this.props.onComponentRef?.(ref);\n  };\n\n  render() {\n    const {\n      enabled = ENABLE_SCREENS,\n      freezeOnBlur = ENABLE_FREEZE,\n      ...rest\n    } = this.props;\n\n    // To maintain default behaviour of formSheet stack presentation style & and to have resonable\n    // defaults for new medium-detent iOS API we need to set defaults here\n    const {\n      sheetAllowedDetents = 'large',\n      sheetLargestUndimmedDetent = 'all',\n      sheetGrabberVisible = false,\n      sheetCornerRadius = -1.0,\n      sheetExpandsWhenScrolledToEdge = true,\n    } = rest;\n\n    if (enabled && isPlatformSupported) {\n      AnimatedNativeScreen =\n        AnimatedNativeScreen ||\n        Animated.createAnimatedComponent(ScreensNativeModules.NativeScreen);\n\n      let {\n        // Filter out active prop in this case because it is unused and\n        // can cause problems depending on react-native version:\n        // https://github.com/react-navigation/react-navigation/issues/4886\n        active,\n        activityState,\n        children,\n        isNativeStack,\n        gestureResponseDistance,\n        onGestureCancel,\n        ...props\n      } = rest;\n\n      if (active !== undefined && activityState === undefined) {\n        console.warn(\n          'It appears that you are using old version of react-navigation library. Please update @react-navigation/bottom-tabs, @react-navigation/stack and @react-navigation/drawer to version 5.10.0 or above to take full advantage of new functionality added to react-native-screens'\n        );\n        activityState = active !== 0 ? 2 : 0; // in the new version, we need one of the screens to have value of 2 after the transition\n      }\n\n      const handleRef = (ref: ViewConfig) => {\n        if (ref?.viewConfig?.validAttributes?.style) {\n          ref.viewConfig.validAttributes.style = {\n            ...ref.viewConfig.validAttributes.style,\n            display: false,\n          };\n          this.setRef(ref);\n        }\n      };\n\n      return (\n        <DelayedFreeze freeze={freezeOnBlur && activityState === 0}>\n          <AnimatedNativeScreen\n            {...props}\n            activityState={activityState}\n            sheetAllowedDetents={sheetAllowedDetents}\n            sheetLargestUndimmedDetent={sheetLargestUndimmedDetent}\n            sheetGrabberVisible={sheetGrabberVisible}\n            sheetCornerRadius={sheetCornerRadius}\n            sheetExpandsWhenScrolledToEdge={sheetExpandsWhenScrolledToEdge}\n            gestureResponseDistance={{\n              start: gestureResponseDistance?.start ?? -1,\n              end: gestureResponseDistance?.end ?? -1,\n              top: gestureResponseDistance?.top ?? -1,\n              bottom: gestureResponseDistance?.bottom ?? -1,\n            }}\n            // This prevents showing blank screen when navigating between multiple screens with freezing\n            // https://github.com/software-mansion/react-native-screens/pull/1208\n            ref={handleRef}\n            onTransitionProgress={\n              !isNativeStack\n                ? undefined\n                : Animated.event(\n                    [\n                      {\n                        nativeEvent: {\n                          progress: this.progress,\n                          closing: this.closing,\n                          goingForward: this.goingForward,\n                        },\n                      },\n                    ],\n                    { useNativeDriver: true }\n                  )\n            }\n            onGestureCancel={\n              onGestureCancel ??\n              (() => {\n                // for internal use\n              })\n            }>\n            {!isNativeStack ? ( // see comment of this prop in types.tsx for information why it is needed\n              children\n            ) : (\n              <TransitionProgressContext.Provider\n                value={{\n                  progress: this.progress,\n                  closing: this.closing,\n                  goingForward: this.goingForward,\n                }}>\n                {children}\n              </TransitionProgressContext.Provider>\n            )}\n          </AnimatedNativeScreen>\n        </DelayedFreeze>\n      );\n    } else {\n      // same reason as above\n      let {\n        active,\n        activityState,\n        style,\n        // eslint-disable-next-line @typescript-eslint/no-unused-vars\n        onComponentRef,\n        ...props\n      } = rest;\n\n      if (active !== undefined && activityState === undefined) {\n        activityState = active !== 0 ? 2 : 0;\n      }\n      return (\n        <Animated.View\n          style={[style, { display: activityState !== 0 ? 'flex' : 'none' }]}\n          ref={this.setRef}\n          {...props}\n        />\n      );\n    }\n  }\n}\n\nfunction ScreenContainer(props: ScreenContainerProps) {\n  const { enabled = ENABLE_SCREENS, hasTwoStates, ...rest } = props;\n\n  if (enabled && isPlatformSupported) {\n    if (hasTwoStates) {\n      return <ScreensNativeModules.NativeScreenNavigationContainer {...rest} />;\n    }\n    return <ScreensNativeModules.NativeScreenContainer {...rest} />;\n  }\n  return <View {...rest} />;\n}\n\nfunction FullWindowOverlay(props: { children: ReactNode }) {\n  if (Platform.OS !== 'ios') {\n    console.warn('Importing FullWindowOverlay is only valid on iOS devices.');\n    return <View {...props} />;\n  }\n  return (\n    <ScreensNativeModules.NativeFullWindowOverlay\n      style={{ position: 'absolute', width: '100%', height: '100%' }}>\n      {props.children}\n    </ScreensNativeModules.NativeFullWindowOverlay>\n  );\n}\n\nconst styles = StyleSheet.create({\n  headerSubview: {\n    position: 'absolute',\n    top: 0,\n    right: 0,\n    flexDirection: 'row',\n    alignItems: 'center',\n    justifyContent: 'center',\n  },\n});\n\nconst ScreenStackHeaderBackButtonImage = (props: ImageProps): JSX.Element => (\n  <ScreensNativeModules.NativeScreenStackHeaderSubview\n    type=\"back\"\n    style={styles.headerSubview}>\n    <Image resizeMode=\"center\" fadeDuration={0} {...props} />\n  </ScreensNativeModules.NativeScreenStackHeaderSubview>\n);\n\nclass SearchBar extends React.Component<SearchBarProps> {\n  nativeSearchBarRef: React.RefObject<SearchBarCommands>;\n\n  constructor(props: SearchBarProps) {\n    super(props);\n    this.nativeSearchBarRef = React.createRef();\n  }\n\n  _callMethodWithRef(method: (ref: SearchBarCommands) => void) {\n    const ref = this.nativeSearchBarRef.current;\n    if (ref) {\n      method(ref);\n    } else {\n      console.warn(\n        'Reference to native search bar component has not been updated yet'\n      );\n    }\n  }\n\n  blur() {\n    this._callMethodWithRef(ref =>\n      ScreensNativeModules.NativeSearchBarCommands.blur(ref)\n    );\n  }\n\n  focus() {\n    this._callMethodWithRef(ref =>\n      ScreensNativeModules.NativeSearchBarCommands.focus(ref)\n    );\n  }\n\n  toggleCancelButton(flag: boolean) {\n    this._callMethodWithRef(ref =>\n      ScreensNativeModules.NativeSearchBarCommands.toggleCancelButton(ref, flag)\n    );\n  }\n\n  clearText() {\n    this._callMethodWithRef(ref =>\n      ScreensNativeModules.NativeSearchBarCommands.clearText(ref)\n    );\n  }\n\n  setText(text: string) {\n    this._callMethodWithRef(ref =>\n      ScreensNativeModules.NativeSearchBarCommands.setText(ref, text)\n    );\n  }\n\n  render() {\n    if (!isSearchBarAvailableForCurrentPlatform) {\n      console.warn(\n        'Importing SearchBar is only valid on iOS and Android devices.'\n      );\n      return View as any as ReactNode;\n    }\n\n    return (\n      <ScreensNativeModules.NativeSearchBar\n        {...this.props}\n        ref={this.nativeSearchBarRef}\n      />\n    );\n  }\n}\n\nconst ScreenStackHeaderRightView = (\n  props: React.PropsWithChildren<ViewProps>\n): JSX.Element => (\n  <ScreensNativeModules.NativeScreenStackHeaderSubview\n    {...props}\n    type=\"right\"\n    style={styles.headerSubview}\n  />\n);\n\nconst ScreenStackHeaderLeftView = (\n  props: React.PropsWithChildren<ViewProps>\n): JSX.Element => (\n  <ScreensNativeModules.NativeScreenStackHeaderSubview\n    {...props}\n    type=\"left\"\n    style={styles.headerSubview}\n  />\n);\n\nconst ScreenStackHeaderCenterView = (\n  props: React.PropsWithChildren<ViewProps>\n): JSX.Element => (\n  <ScreensNativeModules.NativeScreenStackHeaderSubview\n    {...props}\n    type=\"center\"\n    style={styles.headerSubview}\n  />\n);\n\nconst ScreenStackHeaderSearchBarView = (\n  props: React.PropsWithChildren<SearchBarProps>\n): JSX.Element => (\n  <ScreensNativeModules.NativeScreenStackHeaderSubview\n    {...props}\n    type=\"searchBar\"\n    style={styles.headerSubview}\n  />\n);\n\nexport type {\n  StackPresentationTypes,\n  StackAnimationTypes,\n  BlurEffectTypes,\n  ScreenReplaceTypes,\n  ScreenOrientationTypes,\n  HeaderSubviewTypes,\n  ScreenProps,\n  ScreenContainerProps,\n  ScreenStackProps,\n  ScreenStackHeaderConfigProps,\n  SearchBarProps,\n};\n\n// context to be used when the user wants to use enhanced implementation\n// e.g. to use `useReanimatedTransitionProgress` (see `reanimated` folder in repo)\nconst ScreenContext = React.createContext(InnerScreen);\n\nclass Screen extends React.Component<ScreenProps> {\n  static contextType = ScreenContext;\n\n  render() {\n    const ScreenWrapper = (this.context || InnerScreen) as React.ElementType;\n    return <ScreenWrapper {...this.props} />;\n  }\n}\n\nmodule.exports = {\n  // these are classes so they are not evaluated until used\n  // so no need to use getters for them\n  Screen,\n  ScreenContainer,\n  ScreenContext,\n  ScreenStack,\n  InnerScreen,\n  SearchBar,\n  FullWindowOverlay,\n\n  get NativeScreen() {\n    return ScreensNativeModules.NativeScreen;\n  },\n\n  get NativeScreenContainer() {\n    return ScreensNativeModules.NativeScreenContainer;\n  },\n\n  get NativeScreenNavigationContainer() {\n    return ScreensNativeModules.NativeScreenNavigationContainer;\n  },\n\n  get ScreenStackHeaderConfig() {\n    return ScreensNativeModules.NativeScreenStackHeaderConfig;\n  },\n  get ScreenStackHeaderSubview() {\n    return ScreensNativeModules.NativeScreenStackHeaderSubview;\n  },\n  get SearchBarCommands() {\n    return ScreensNativeModules.NativeSearchBarCommands;\n  },\n  // these are functions and will not be evaluated until used\n  // so no need to use getters for them\n  ScreenStackHeaderBackButtonImage,\n  ScreenStackHeaderRightView,\n  ScreenStackHeaderLeftView,\n  ScreenStackHeaderCenterView,\n  ScreenStackHeaderSearchBarView,\n\n  enableScreens,\n  enableFreeze,\n  screensEnabled,\n  shouldUseActivityState,\n  useTransitionProgress,\n\n  isSearchBarAvailableForCurrentPlatform,\n  isNewBackTitleImplementation,\n  executeNativeBackPress,\n};\n"], "mappings": ";;;;AAAA;AACA,OAAOA,KAAK,IAAIC,SAAS,QAAsC,OAAO;AACtE,SACEC,QAAQ,EACRC,KAAK,EAELC,QAAQ,EAERC,UAAU,EACVC,SAAS,EACTC,IAAI,QAGC,cAAc;AACrB,SAASC,MAAM,QAAQ,cAAc;AACrC,SAASC,OAAO,QAAQ,2BAA2B;AAEnD,OAAOC,yBAAyB,MAAM,6BAA6B;AACnE,OAAOC,qBAAqB,MAAM,yBAAyB;AAe3D,SACEC,sCAAsC,EACtCC,4BAA4B,EAC5BC,sBAAsB,QACjB,SAAS;;AAEhB;AACA,MAAMC,mBAAmB,GACvBX,QAAQ,CAACY,EAAE,KAAK,KAAK,IACrBZ,QAAQ,CAACY,EAAE,KAAK,SAAS,IACzBZ,QAAQ,CAACY,EAAE,KAAK,SAAS;AAE3B,IAAIC,cAAc,GAAGF,mBAAmB;AAExC,SAASG,aAAaA,CAAA,EAAmC;EAAA,IAAlCC,mBAAmB,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,IAAI;EAC/CH,cAAc,GAAGF,mBAAmB,IAAII,mBAAmB;EAC3D,IAAIF,cAAc,IAAI,CAACX,SAAS,CAACiB,oBAAoB,CAAC,WAAW,CAAC,EAAE;IAClEC,OAAO,CAACC,KAAK,CACV,wGACH,CAAC;EACH;AACF;AAEA,IAAIC,aAAa,GAAG,KAAK;AAEzB,SAASC,YAAYA,CAAA,EAAuC;EAAA,IAAtCC,uBAAuB,GAAAR,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,IAAI;EAClD,MAAMS,KAAK,GAAGC,QAAQ,CAACrB,OAAO,CAACsB,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;EAE/C;EACA,IAAI,EAAEF,KAAK,KAAK,CAAC,IAAIA,KAAK,IAAI,EAAE,CAAC,IAAID,uBAAuB,EAAE;IAC5DJ,OAAO,CAACQ,IAAI,CACV,kIACF,CAAC;EACH;EAEAN,aAAa,GAAGE,uBAAuB;AACzC;;AAEA;AACA,MAAMK,sBAAsB,GAAG,IAAI;AAEnC,SAASC,cAAcA,CAAA,EAAY;EACjC,OAAOjB,cAAc;AACvB;AAsBA;AACA;AACA,IAAIkB,iBAAmD;AACvD,IAAIC,0BAAqE;AACzE,IAAIC,oCAA+E;AACnF,IAAIC,iBAAwD;AAC5D,IAAIC,6BAAgF;AACpF,IAAIC,8BAEH;AACD,IAAIC,oBAAsD;AAE1D,IAAIC,eAC4B;AAChC,IAAIC,uBAA8C;AAElD,IAAIC,uBAIH;AAED,MAAMC,oBAAoB,GAAG;EAC3B,IAAIC,YAAYA,CAAA,EAAG;IACjBX,iBAAiB,GACfA,iBAAiB,IAAIY,OAAO,CAAC,gCAAgC,CAAC,CAACC,OAAO;IACxE,OAAOb,iBAAiB;EAC1B,CAAC;EAED,IAAIc,qBAAqBA,CAAA,EAAG;IAC1Bb,0BAA0B,GACxBA,0BAA0B,IAC1BW,OAAO,CAAC,yCAAyC,CAAC,CAACC,OAAO;IAC5D,OAAOZ,0BAA0B;EACnC,CAAC;EAED,IAAIc,+BAA+BA,CAAA,EAAG;IACpCb,oCAAoC,GAClCA,oCAAoC,KACnCjC,QAAQ,CAACY,EAAE,KAAK,KAAK,GAClB+B,OAAO,CAAC,mDAAmD,CAAC,CAACC,OAAO,GACpE,IAAI,CAACC,qBAAqB,CAAC;IACjC,OAAOZ,oCAAoC;EAC7C,CAAC;EAED,IAAIC,iBAAiBA,CAAA,EAAG;IACtBA,iBAAiB,GACfA,iBAAiB,IACjBS,OAAO,CAAC,qCAAqC,CAAC,CAACC,OAAO;IACxD,OAAOV,iBAAiB;EAC1B,CAAC;EAED,IAAIC,6BAA6BA,CAAA,EAAG;IAClCA,6BAA6B,GAC3BA,6BAA6B,IAC7BQ,OAAO,CAAC,iDAAiD,CAAC,CAACC,OAAO;IACpE,OAAOT,6BAA6B;EACtC,CAAC;EAED,IAAIC,8BAA8BA,CAAA,EAAG;IACnCA,8BAA8B,GAC5BA,8BAA8B,IAC9BO,OAAO,CAAC,kDAAkD,CAAC,CAACC,OAAO;IACrE,OAAOR,8BAA8B;EACvC,CAAC;EAED,IAAIE,eAAeA,CAAA,EAAG;IACpBA,eAAe,GACbA,eAAe,IAAIK,OAAO,CAAC,mCAAmC,CAAC,CAACC,OAAO;IACzE,OAAON,eAAe;EACxB,CAAC;EAED,IAAIC,uBAAuBA,CAAA,EAAG;IAC5BA,uBAAuB,GACrBA,uBAAuB,IACvBI,OAAO,CAAC,mCAAmC,CAAC,CAACI,QAAQ;IACvD,OAAOR,uBAAuB;EAChC,CAAC;EAED,IAAIC,uBAAuBA,CAAA,EAAG;IAC5BA,uBAAuB,GACrBA,uBAAuB,IACvBG,OAAO,CAAC,2CAA2C,CAAC,CAACC,OAAO;IAC9D,OAAOJ,uBAAuB;EAChC;AACF,CAAC;AAOD;AACA;AACA,SAASQ,aAAaA,CAAAC,IAAA,EAA2C;EAAA,IAA1C;IAAEC,MAAM;IAAEC;EAA6B,CAAC,GAAAF,IAAA;EAC7D;EACA,MAAM,CAACG,WAAW,EAAEC,cAAc,CAAC,GAAGzD,KAAK,CAAC0D,QAAQ,CAAC,KAAK,CAAC;EAE3DzD,SAAS,CAAC,MAAM;IACd,MAAM0D,EAAE,GAAGC,YAAY,CAAC,MAAM;MAC5BH,cAAc,CAACH,MAAM,CAAC;IACxB,CAAC,CAAC;IACF,OAAO,MAAM;MACXO,cAAc,CAACF,EAAE,CAAC;IACpB,CAAC;EACH,CAAC,EAAE,CAACL,MAAM,CAAC,CAAC;EAEZ,oBAAOtD,KAAA,CAAA8D,aAAA,CAACtD,MAAM;IAAC8C,MAAM,EAAEA,MAAM,GAAGE,WAAW,GAAG;EAAM,GAAED,QAAiB,CAAC;AAC1E;AAEA,SAASQ,WAAWA,CAACC,KAAuB,EAAE;EAC5C,MAAM;IAAET,QAAQ;IAAE,GAAGU;EAAK,CAAC,GAAGD,KAAK;EACnC,MAAME,IAAI,GAAGlE,KAAK,CAACmE,QAAQ,CAACC,KAAK,CAACb,QAAQ,CAAC;EAC3C;EACA,MAAMc,kBAAkB,GAAGrE,KAAK,CAACmE,QAAQ,CAACG,GAAG,CAACf,QAAQ,EAAE,CAACgB,KAAK,EAAEC,KAAK,KAAK;IAAA,IAAAC,iBAAA,EAAAC,kBAAA,EAAAC,qBAAA,EAAAC,mBAAA;IACxE;IACA,MAAM;MAAEZ,KAAK;MAAEa;IAAI,CAAC,GAAGN,KAAK;IAC5B,MAAMO,UAAU,IAAAL,iBAAA,GAAGT,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEc,UAAU,cAAAL,iBAAA,cAAAA,iBAAA,GAAIT,KAAK,aAALA,KAAK,gBAAAU,kBAAA,GAALV,KAAK,CAAEe,WAAW,cAAAL,kBAAA,uBAAlBA,kBAAA,CAAqBG,GAAG,CAAC;IACjE,MAAMG,aAAa,IAAAL,qBAAA,GAAGG,UAAU,aAAVA,UAAU,gBAAAF,mBAAA,GAAVE,UAAU,CAAEG,OAAO,cAAAL,mBAAA,uBAAnBA,mBAAA,CAAqBM,YAAY,cAAAP,qBAAA,cAAAA,qBAAA,GAAIjD,aAAa;IAExE,oBACE1B,KAAA,CAAA8D,aAAA,CAACV,aAAa;MAACE,MAAM,EAAE0B,aAAa,IAAId,IAAI,GAAGM,KAAK,GAAG;IAAE,GACtDD,KACY,CAAC;EAEpB,CAAC,CAAC;EAEF,oBACEvE,KAAA,CAAA8D,aAAA,CAACjB,oBAAoB,CAACP,iBAAiB,EAAK2B,IAAI,EAC7CI,kBACqC,CAAC;AAE7C;;AAEA;AACA;AAWA,MAAMc,WAAW,SAASnF,KAAK,CAACoF,SAAS,CAAc;EAAAC,YAAA;IAAA,SAAAjE,SAAA;IAAAkE,eAAA,cACD,IAAI;IAAAA,eAAA,kBACtC,IAAIpF,QAAQ,CAACqF,KAAK,CAAC,CAAC,CAAC;IAAAD,eAAA,mBACpB,IAAIpF,QAAQ,CAACqF,KAAK,CAAC,CAAC,CAAC;IAAAD,eAAA,uBACjB,IAAIpF,QAAQ,CAACqF,KAAK,CAAC,CAAC,CAAC;IAAAD,eAAA,iBAMlCE,GAAyC,IAAW;MAAA,IAAAC,qBAAA,EAAAC,WAAA;MAC5D,IAAI,CAACF,GAAG,GAAGA,GAAG;MACd,CAAAC,qBAAA,IAAAC,WAAA,OAAI,CAAC1B,KAAK,EAAC2B,cAAc,cAAAF,qBAAA,eAAzBA,qBAAA,CAAAG,IAAA,CAAAF,WAAA,EAA4BF,GAAG,CAAC;IAClC,CAAC;EAAA;EAPDK,cAAcA,CAAC7B,KAAkB,EAAQ;IAAA,IAAA8B,SAAA;IACvC,CAAAA,SAAA,OAAI,CAACN,GAAG,cAAAM,SAAA,eAARA,SAAA,CAAUD,cAAc,CAAC7B,KAAK,CAAC;EACjC;EAOA+B,MAAMA,CAAA,EAAG;IACP,MAAM;MACJC,OAAO,GAAG/E,cAAc;MACxBiE,YAAY,GAAGxD,aAAa;MAC5B,GAAGuC;IACL,CAAC,GAAG,IAAI,CAACD,KAAK;;IAEd;IACA;IACA,MAAM;MACJiC,mBAAmB,GAAG,OAAO;MAC7BC,0BAA0B,GAAG,KAAK;MAClCC,mBAAmB,GAAG,KAAK;MAC3BC,iBAAiB,GAAG,CAAC,GAAG;MACxBC,8BAA8B,GAAG;IACnC,CAAC,GAAGpC,IAAI;IAER,IAAI+B,OAAO,IAAIjF,mBAAmB,EAAE;MAAA,IAAAuF,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA;MAClChE,oBAAoB,GAClBA,oBAAoB,IACpBvC,QAAQ,CAACwG,uBAAuB,CAAC7D,oBAAoB,CAACC,YAAY,CAAC;MAErE,IAAI;QACF;QACA;QACA;QACA6D,MAAM;QACNC,aAAa;QACbrD,QAAQ;QACRsD,aAAa;QACbC,uBAAuB;QACvBC,eAAe;QACf,GAAG/C;MACL,CAAC,GAAGC,IAAI;MAER,IAAI0C,MAAM,KAAKrF,SAAS,IAAIsF,aAAa,KAAKtF,SAAS,EAAE;QACvDE,OAAO,CAACQ,IAAI,CACV,+QACF,CAAC;QACD4E,aAAa,GAAGD,MAAM,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;MACxC;;MAEA,MAAMK,SAAS,GAAIxB,GAAe,IAAK;QAAA,IAAAyB,eAAA;QACrC,IAAIzB,GAAG,aAAHA,GAAG,gBAAAyB,eAAA,GAAHzB,GAAG,CAAE0B,UAAU,cAAAD,eAAA,gBAAAA,eAAA,GAAfA,eAAA,CAAiBE,eAAe,cAAAF,eAAA,eAAhCA,eAAA,CAAkCG,KAAK,EAAE;UAC3C5B,GAAG,CAAC0B,UAAU,CAACC,eAAe,CAACC,KAAK,GAAG;YACrC,GAAG5B,GAAG,CAAC0B,UAAU,CAACC,eAAe,CAACC,KAAK;YACvCC,OAAO,EAAE;UACX,CAAC;UACD,IAAI,CAACC,MAAM,CAAC9B,GAAG,CAAC;QAClB;MACF,CAAC;MAED,oBACExF,KAAA,CAAA8D,aAAA,CAACV,aAAa;QAACE,MAAM,EAAE4B,YAAY,IAAI0B,aAAa,KAAK;MAAE,gBACzD5G,KAAA,CAAA8D,aAAA,CAACrB,oBAAoB,EAAA8E,QAAA,KACfvD,KAAK;QACT4C,aAAa,EAAEA,aAAc;QAC7BX,mBAAmB,EAAEA,mBAAoB;QACzCC,0BAA0B,EAAEA,0BAA2B;QACvDC,mBAAmB,EAAEA,mBAAoB;QACzCC,iBAAiB,EAAEA,iBAAkB;QACrCC,8BAA8B,EAAEA,8BAA+B;QAC/DS,uBAAuB,EAAE;UACvBU,KAAK,GAAAlB,qBAAA,GAAEQ,uBAAuB,aAAvBA,uBAAuB,uBAAvBA,uBAAuB,CAAEU,KAAK,cAAAlB,qBAAA,cAAAA,qBAAA,GAAI,CAAC,CAAC;UAC3CmB,GAAG,GAAAlB,sBAAA,GAAEO,uBAAuB,aAAvBA,uBAAuB,uBAAvBA,uBAAuB,CAAEW,GAAG,cAAAlB,sBAAA,cAAAA,sBAAA,GAAI,CAAC,CAAC;UACvCmB,GAAG,GAAAlB,sBAAA,GAAEM,uBAAuB,aAAvBA,uBAAuB,uBAAvBA,uBAAuB,CAAEY,GAAG,cAAAlB,sBAAA,cAAAA,sBAAA,GAAI,CAAC,CAAC;UACvCmB,MAAM,GAAAlB,sBAAA,GAAEK,uBAAuB,aAAvBA,uBAAuB,uBAAvBA,uBAAuB,CAAEa,MAAM,cAAAlB,sBAAA,cAAAA,sBAAA,GAAI,CAAC;QAC9C;QACA;QACA;QAAA;QACAjB,GAAG,EAAEwB,SAAU;QACfY,oBAAoB,EAClB,CAACf,aAAa,GACVvF,SAAS,GACTpB,QAAQ,CAAC2H,KAAK,CACZ,CACE;UACEC,WAAW,EAAE;YACXC,QAAQ,EAAE,IAAI,CAACA,QAAQ;YACvBC,OAAO,EAAE,IAAI,CAACA,OAAO;YACrBC,YAAY,EAAE,IAAI,CAACA;UACrB;QACF,CAAC,CACF,EACD;UAAEC,eAAe,EAAE;QAAK,CAC1B,CACL;QACDnB,eAAe,EACbA,eAAe,aAAfA,eAAe,cAAfA,eAAe,GACd,MAAM;UACL;QAAA;MAEH,IACA,CAACF,aAAa;MAAK;MAClBtD,QAAQ,gBAERvD,KAAA,CAAA8D,aAAA,CAACpD,yBAAyB,CAACyH,QAAQ;QACjCC,KAAK,EAAE;UACLL,QAAQ,EAAE,IAAI,CAACA,QAAQ;UACvBC,OAAO,EAAE,IAAI,CAACA,OAAO;UACrBC,YAAY,EAAE,IAAI,CAACA;QACrB;MAAE,GACD1E,QACiC,CAElB,CACT,CAAC;IAEpB,CAAC,MAAM;MACL;MACA,IAAI;QACFoD,MAAM;QACNC,aAAa;QACbQ,KAAK;QACL;QACAzB,cAAc;QACd,GAAG3B;MACL,CAAC,GAAGC,IAAI;MAER,IAAI0C,MAAM,KAAKrF,SAAS,IAAIsF,aAAa,KAAKtF,SAAS,EAAE;QACvDsF,aAAa,GAAGD,MAAM,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC;MACtC;MACA,oBACE3G,KAAA,CAAA8D,aAAA,CAAC5D,QAAQ,CAACK,IAAI,EAAAgH,QAAA;QACZH,KAAK,EAAE,CAACA,KAAK,EAAE;UAAEC,OAAO,EAAET,aAAa,KAAK,CAAC,GAAG,MAAM,GAAG;QAAO,CAAC,CAAE;QACnEpB,GAAG,EAAE,IAAI,CAAC8B;MAAO,GACbtD,KAAK,CACV,CAAC;IAEN;EACF;AACF;AAEA,SAASqE,eAAeA,CAACrE,KAA2B,EAAE;EACpD,MAAM;IAAEgC,OAAO,GAAG/E,cAAc;IAAEqH,YAAY;IAAE,GAAGrE;EAAK,CAAC,GAAGD,KAAK;EAEjE,IAAIgC,OAAO,IAAIjF,mBAAmB,EAAE;IAClC,IAAIuH,YAAY,EAAE;MAChB,oBAAOtI,KAAA,CAAA8D,aAAA,CAACjB,oBAAoB,CAACK,+BAA+B,EAAKe,IAAO,CAAC;IAC3E;IACA,oBAAOjE,KAAA,CAAA8D,aAAA,CAACjB,oBAAoB,CAACI,qBAAqB,EAAKgB,IAAO,CAAC;EACjE;EACA,oBAAOjE,KAAA,CAAA8D,aAAA,CAACvD,IAAI,EAAK0D,IAAO,CAAC;AAC3B;AAEA,SAASsE,iBAAiBA,CAACvE,KAA8B,EAAE;EACzD,IAAI5D,QAAQ,CAACY,EAAE,KAAK,KAAK,EAAE;IACzBQ,OAAO,CAACQ,IAAI,CAAC,2DAA2D,CAAC;IACzE,oBAAOhC,KAAA,CAAA8D,aAAA,CAACvD,IAAI,EAAKyD,KAAQ,CAAC;EAC5B;EACA,oBACEhE,KAAA,CAAA8D,aAAA,CAACjB,oBAAoB,CAACD,uBAAuB;IAC3CwE,KAAK,EAAE;MAAEoB,QAAQ,EAAE,UAAU;MAAEC,KAAK,EAAE,MAAM;MAAEC,MAAM,EAAE;IAAO;EAAE,GAC9D1E,KAAK,CAACT,QACqC,CAAC;AAEnD;AAEA,MAAMoF,MAAM,GAAGtI,UAAU,CAACuI,MAAM,CAAC;EAC/BC,aAAa,EAAE;IACbL,QAAQ,EAAE,UAAU;IACpBd,GAAG,EAAE,CAAC;IACNoB,KAAK,EAAE,CAAC;IACRC,aAAa,EAAE,KAAK;IACpBC,UAAU,EAAE,QAAQ;IACpBC,cAAc,EAAE;EAClB;AACF,CAAC,CAAC;AAEF,MAAMC,gCAAgC,GAAIlF,KAAiB,iBACzDhE,KAAA,CAAA8D,aAAA,CAACjB,oBAAoB,CAACL,8BAA8B;EAClD2G,IAAI,EAAC,MAAM;EACX/B,KAAK,EAAEuB,MAAM,CAACE;AAAc,gBAC5B7I,KAAA,CAAA8D,aAAA,CAAC3D,KAAK,EAAAoH,QAAA;EAAC6B,UAAU,EAAC,QAAQ;EAACC,YAAY,EAAE;AAAE,GAAKrF,KAAK,CAAG,CACL,CACtD;AAED,MAAMsF,SAAS,SAAStJ,KAAK,CAACoF,SAAS,CAAiB;EAGtDC,WAAWA,CAACrB,KAAqB,EAAE;IACjC,KAAK,CAACA,KAAK,CAAC;IAACsB,eAAA;IACb,IAAI,CAACiE,kBAAkB,gBAAGvJ,KAAK,CAACwJ,SAAS,CAAC,CAAC;EAC7C;EAEAC,kBAAkBA,CAACC,MAAwC,EAAE;IAC3D,MAAMlE,GAAG,GAAG,IAAI,CAAC+D,kBAAkB,CAACI,OAAO;IAC3C,IAAInE,GAAG,EAAE;MACPkE,MAAM,CAAClE,GAAG,CAAC;IACb,CAAC,MAAM;MACLhE,OAAO,CAACQ,IAAI,CACV,mEACF,CAAC;IACH;EACF;EAEA4H,IAAIA,CAAA,EAAG;IACL,IAAI,CAACH,kBAAkB,CAACjE,GAAG,IACzB3C,oBAAoB,CAACF,uBAAuB,CAACiH,IAAI,CAACpE,GAAG,CACvD,CAAC;EACH;EAEAqE,KAAKA,CAAA,EAAG;IACN,IAAI,CAACJ,kBAAkB,CAACjE,GAAG,IACzB3C,oBAAoB,CAACF,uBAAuB,CAACkH,KAAK,CAACrE,GAAG,CACxD,CAAC;EACH;EAEAsE,kBAAkBA,CAACC,IAAa,EAAE;IAChC,IAAI,CAACN,kBAAkB,CAACjE,GAAG,IACzB3C,oBAAoB,CAACF,uBAAuB,CAACmH,kBAAkB,CAACtE,GAAG,EAAEuE,IAAI,CAC3E,CAAC;EACH;EAEAC,SAASA,CAAA,EAAG;IACV,IAAI,CAACP,kBAAkB,CAACjE,GAAG,IACzB3C,oBAAoB,CAACF,uBAAuB,CAACqH,SAAS,CAACxE,GAAG,CAC5D,CAAC;EACH;EAEAyE,OAAOA,CAACC,IAAY,EAAE;IACpB,IAAI,CAACT,kBAAkB,CAACjE,GAAG,IACzB3C,oBAAoB,CAACF,uBAAuB,CAACsH,OAAO,CAACzE,GAAG,EAAE0E,IAAI,CAChE,CAAC;EACH;EAEAnE,MAAMA,CAAA,EAAG;IACP,IAAI,CAACnF,sCAAsC,EAAE;MAC3CY,OAAO,CAACQ,IAAI,CACV,+DACF,CAAC;MACD,OAAOzB,IAAI;IACb;IAEA,oBACEP,KAAA,CAAA8D,aAAA,CAACjB,oBAAoB,CAACH,eAAe,EAAA6E,QAAA,KAC/B,IAAI,CAACvD,KAAK;MACdwB,GAAG,EAAE,IAAI,CAAC+D;IAAmB,EAC9B,CAAC;EAEN;AACF;AAEA,MAAMY,0BAA0B,GAC9BnG,KAAyC,iBAEzChE,KAAA,CAAA8D,aAAA,CAACjB,oBAAoB,CAACL,8BAA8B,EAAA+E,QAAA,KAC9CvD,KAAK;EACTmF,IAAI,EAAC,OAAO;EACZ/B,KAAK,EAAEuB,MAAM,CAACE;AAAc,EAC7B,CACF;AAED,MAAMuB,yBAAyB,GAC7BpG,KAAyC,iBAEzChE,KAAA,CAAA8D,aAAA,CAACjB,oBAAoB,CAACL,8BAA8B,EAAA+E,QAAA,KAC9CvD,KAAK;EACTmF,IAAI,EAAC,MAAM;EACX/B,KAAK,EAAEuB,MAAM,CAACE;AAAc,EAC7B,CACF;AAED,MAAMwB,2BAA2B,GAC/BrG,KAAyC,iBAEzChE,KAAA,CAAA8D,aAAA,CAACjB,oBAAoB,CAACL,8BAA8B,EAAA+E,QAAA,KAC9CvD,KAAK;EACTmF,IAAI,EAAC,QAAQ;EACb/B,KAAK,EAAEuB,MAAM,CAACE;AAAc,EAC7B,CACF;AAED,MAAMyB,8BAA8B,GAClCtG,KAA8C,iBAE9ChE,KAAA,CAAA8D,aAAA,CAACjB,oBAAoB,CAACL,8BAA8B,EAAA+E,QAAA,KAC9CvD,KAAK;EACTmF,IAAI,EAAC,WAAW;EAChB/B,KAAK,EAAEuB,MAAM,CAACE;AAAc,EAC7B,CACF;AAgBD;AACA;AACA,MAAM0B,aAAa,gBAAGvK,KAAK,CAACwK,aAAa,CAACrF,WAAW,CAAC;AAEtD,MAAMsF,MAAM,SAASzK,KAAK,CAACoF,SAAS,CAAc;EAGhDW,MAAMA,CAAA,EAAG;IACP,MAAM2E,aAAa,GAAI,IAAI,CAACC,OAAO,IAAIxF,WAAiC;IACxE,oBAAOnF,KAAA,CAAA8D,aAAA,CAAC4G,aAAa,EAAK,IAAI,CAAC1G,KAAQ,CAAC;EAC1C;AACF;AAACsB,eAAA,CAPKmF,MAAM,iBACWF,aAAa;AAQpCK,MAAM,CAACC,OAAO,GAAG;EACf;EACA;EACAJ,MAAM;EACNpC,eAAe;EACfkC,aAAa;EACbxG,WAAW;EACXoB,WAAW;EACXmE,SAAS;EACTf,iBAAiB;EAEjB,IAAIzF,YAAYA,CAAA,EAAG;IACjB,OAAOD,oBAAoB,CAACC,YAAY;EAC1C,CAAC;EAED,IAAIG,qBAAqBA,CAAA,EAAG;IAC1B,OAAOJ,oBAAoB,CAACI,qBAAqB;EACnD,CAAC;EAED,IAAIC,+BAA+BA,CAAA,EAAG;IACpC,OAAOL,oBAAoB,CAACK,+BAA+B;EAC7D,CAAC;EAED,IAAI4H,uBAAuBA,CAAA,EAAG;IAC5B,OAAOjI,oBAAoB,CAACN,6BAA6B;EAC3D,CAAC;EACD,IAAIwI,wBAAwBA,CAAA,EAAG;IAC7B,OAAOlI,oBAAoB,CAACL,8BAA8B;EAC5D,CAAC;EACD,IAAIwI,iBAAiBA,CAAA,EAAG;IACtB,OAAOnI,oBAAoB,CAACF,uBAAuB;EACrD,CAAC;EACD;EACA;EACAuG,gCAAgC;EAChCiB,0BAA0B;EAC1BC,yBAAyB;EACzBC,2BAA2B;EAC3BC,8BAA8B;EAE9BpJ,aAAa;EACbS,YAAY;EACZO,cAAc;EACdD,sBAAsB;EACtBtB,qBAAqB;EAErBC,sCAAsC;EACtCC,4BAA4B;EAC5BC;AACF,CAAC"}