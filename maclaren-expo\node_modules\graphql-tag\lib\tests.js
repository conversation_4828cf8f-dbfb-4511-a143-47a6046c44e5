import { __makeTemplateObject } from "tslib";
import 'source-map-support/register';
import { assert } from 'chai';
import gql from './index';
var loader = require('../loader');
describe('gql', function () {
    it('parses queries', function () {
        assert.equal(gql(templateObject_1 || (templateObject_1 = __makeTemplateObject(["{ testQuery }"], ["{ testQuery }"]))).kind, 'Document');
    });
    it('parses queries when called as a function', function () {
        assert.equal(gql('{ testQuery }').kind, 'Document');
    });
    it('parses queries with weird substitutions', function () {
        var obj = Object.create(null);
        assert.equal(gql(templateObject_2 || (templateObject_2 = __makeTemplateObject(["{ field(input: \"", "\") }"], ["{ field(input: \"", "\") }"])), obj.missing).kind, 'Document');
        assert.equal(gql(templateObject_3 || (templateObject_3 = __makeTemplateObject(["{ field(input: \"", "\") }"], ["{ field(input: \"", "\") }"])), null).kind, 'Document');
        assert.equal(gql(templateObject_4 || (templateObject_4 = __makeTemplateObject(["{ field(input: \"", "\") }"], ["{ field(input: \"", "\") }"])), 0).kind, 'Document');
    });
    it('allows interpolation of documents generated by the webpack loader', function () {
        var sameFragment = "fragment SomeFragmentName on SomeType { someField }";
        var jsSource = loader.call({ cacheable: function () { } }, sameFragment);
        var module = { exports: Object.create(null) };
        Function("module", jsSource)(module);
        var document = gql(templateObject_5 || (templateObject_5 = __makeTemplateObject(["query { ...SomeFragmentName } ", ""], ["query { ...SomeFragmentName } ", ""])), module.exports);
        assert.equal(document.kind, 'Document');
        assert.equal(document.definitions.length, 2);
        assert.equal(document.definitions[0].kind, 'OperationDefinition');
        assert.equal(document.definitions[1].kind, 'FragmentDefinition');
    });
    it('parses queries through webpack loader', function () {
        var jsSource = loader.call({ cacheable: function () { } }, '{ testQuery }');
        var module = { exports: Object.create(null) };
        Function("module", jsSource)(module);
        assert.equal(module.exports.kind, 'Document');
    });
    it('parses single query through webpack loader', function () {
        var jsSource = loader.call({ cacheable: function () { } }, "\n      query Q1 { testQuery }\n    ");
        var module = { exports: Object.create(null) };
        Function("module", jsSource)(module);
        assert.equal(module.exports.kind, 'Document');
        assert.exists(module.exports.Q1);
        assert.equal(module.exports.Q1.kind, 'Document');
        assert.equal(module.exports.Q1.definitions.length, 1);
    });
    it('parses single query and exports as default', function () {
        var jsSource = loader.call({ cacheable: function () { } }, "\n      query Q1 { testQuery }\n    ");
        var module = { exports: Object.create(null) };
        Function("module", jsSource)(module);
        assert.deepEqual(module.exports.definitions, module.exports.Q1.definitions);
    });
    it('parses multiple queries through webpack loader', function () {
        var jsSource = loader.call({ cacheable: function () { } }, "\n      query Q1 { testQuery }\n      query Q2 { testQuery2 }\n    ");
        var module = { exports: Object.create(null) };
        Function("module", jsSource)(module);
        assert.exists(module.exports.Q1);
        assert.exists(module.exports.Q2);
        assert.equal(module.exports.Q1.kind, 'Document');
        assert.equal(module.exports.Q2.kind, 'Document');
        assert.equal(module.exports.Q1.definitions.length, 1);
        assert.equal(module.exports.Q2.definitions.length, 1);
    });
    it('parses fragments with variable definitions', function () {
        gql.enableExperimentalFragmentVariables();
        var parsed = gql(templateObject_6 || (templateObject_6 = __makeTemplateObject(["fragment A ($arg: String!) on Type { testQuery }"], ["fragment A ($arg: String!) on Type { testQuery }"])));
        assert.equal(parsed.kind, 'Document');
        assert.exists(parsed.definitions[0].variableDefinitions);
        gql.disableExperimentalFragmentVariables();
    });
    it('does not nest queries needlessly in named exports', function () {
        var jsSource = loader.call({ cacheable: function () { } }, "\n      query Q1 { testQuery }\n      query Q2 { testQuery2 }\n      query Q3 { test Query3 }\n    ");
        var module = { exports: Object.create(null) };
        Function("module", jsSource)(module);
        assert.notExists(module.exports.Q2.Q1);
        assert.notExists(module.exports.Q3.Q1);
        assert.notExists(module.exports.Q3.Q2);
    });
    it('tracks fragment dependencies from multiple queries through webpack loader', function () {
        var jsSource = loader.call({ cacheable: function () { } }, "\n      fragment F1 on F { testQuery }\n      fragment F2 on F { testQuery2 }\n      fragment F3 on F { testQuery3 }\n      query Q1 { ...F1 }\n      query Q2 { ...F2 }\n      query Q3 {\n        ...F1\n        ...F2\n      }\n    ");
        var module = { exports: Object.create(null) };
        Function("module", jsSource)(module);
        assert.exists(module.exports.Q1);
        assert.exists(module.exports.Q2);
        assert.exists(module.exports.Q3);
        var Q1 = module.exports.Q1.definitions;
        var Q2 = module.exports.Q2.definitions;
        var Q3 = module.exports.Q3.definitions;
        assert.equal(Q1.length, 2);
        assert.equal(Q1[0].name.value, 'Q1');
        assert.equal(Q1[1].name.value, 'F1');
        assert.equal(Q2.length, 2);
        assert.equal(Q2[0].name.value, 'Q2');
        assert.equal(Q2[1].name.value, 'F2');
        assert.equal(Q3.length, 3);
        assert.equal(Q3[0].name.value, 'Q3');
        assert.equal(Q3[1].name.value, 'F1');
        assert.equal(Q3[2].name.value, 'F2');
        var F1 = module.exports.F1.definitions;
        var F2 = module.exports.F2.definitions;
        var F3 = module.exports.F3.definitions;
        assert.equal(F1.length, 1);
        assert.equal(F1[0].name.value, 'F1');
        assert.equal(F2.length, 1);
        assert.equal(F2[0].name.value, 'F2');
        assert.equal(F3.length, 1);
        assert.equal(F3[0].name.value, 'F3');
    });
    it('tracks fragment dependencies across nested fragments', function () {
        var jsSource = loader.call({ cacheable: function () { } }, "\n      fragment F11 on F { testQuery }\n      fragment F22 on F {\n        ...F11\n        testQuery2\n      }\n      fragment F33 on F {\n        ...F22\n        testQuery3\n      }\n\n      query Q1 {\n        ...F33\n      }\n\n      query Q2 {\n        id\n      }\n    ");
        var module = { exports: Object.create(null) };
        Function("module", jsSource)(module);
        assert.exists(module.exports.Q1);
        assert.exists(module.exports.Q2);
        var Q1 = module.exports.Q1.definitions;
        var Q2 = module.exports.Q2.definitions;
        assert.equal(Q1.length, 4);
        assert.equal(Q1[0].name.value, 'Q1');
        assert.equal(Q1[1].name.value, 'F33');
        assert.equal(Q1[2].name.value, 'F22');
        assert.equal(Q1[3].name.value, 'F11');
        assert.equal(Q2.length, 1);
        var F11 = module.exports.F11.definitions;
        var F22 = module.exports.F22.definitions;
        var F33 = module.exports.F33.definitions;
        assert.equal(F11.length, 1);
        assert.equal(F11[0].name.value, 'F11');
        assert.equal(F22.length, 2);
        assert.equal(F22[0].name.value, 'F22');
        assert.equal(F22[1].name.value, 'F11');
        assert.equal(F33.length, 3);
        assert.equal(F33[0].name.value, 'F33');
        assert.equal(F33[1].name.value, 'F22');
        assert.equal(F33[2].name.value, 'F11');
    });
    it('correctly imports other files through the webpack loader', function () {
        var query = "#import \"./fragment_definition.graphql\"\n      query {\n        author {\n          ...authorDetails\n        }\n      }";
        var jsSource = loader.call({ cacheable: function () { } }, query);
        var module = { exports: Object.create(null) };
        var require = function (path) {
            assert.equal(path, './fragment_definition.graphql');
            return gql(templateObject_7 || (templateObject_7 = __makeTemplateObject(["\n        fragment authorDetails on Author {\n          firstName\n          lastName\n        }"], ["\n        fragment authorDetails on Author {\n          firstName\n          lastName\n        }"])));
        };
        Function("module,require", jsSource)(module, require);
        assert.equal(module.exports.kind, 'Document');
        var definitions = module.exports.definitions;
        assert.equal(definitions.length, 2);
        assert.equal(definitions[0].kind, 'OperationDefinition');
        assert.equal(definitions[1].kind, 'FragmentDefinition');
    });
    it('tracks fragment dependencies across fragments loaded via the webpack loader', function () {
        var query = "#import \"./fragment_definition.graphql\"\n      fragment F111 on F {\n        ...F222\n      }\n\n      query Q1 {\n        ...F111\n      }\n\n      query Q2 {\n        a\n      }\n      ";
        var jsSource = loader.call({ cacheable: function () { } }, query);
        var module = { exports: Object.create(null) };
        var require = function (path) {
            assert.equal(path, './fragment_definition.graphql');
            return gql(templateObject_8 || (templateObject_8 = __makeTemplateObject(["\n        fragment F222 on F {\n          f1\n          f2\n        }"], ["\n        fragment F222 on F {\n          f1\n          f2\n        }"])));
        };
        Function("module,require", jsSource)(module, require);
        assert.exists(module.exports.Q1);
        assert.exists(module.exports.Q2);
        var Q1 = module.exports.Q1.definitions;
        var Q2 = module.exports.Q2.definitions;
        assert.equal(Q1.length, 3);
        assert.equal(Q1[0].name.value, 'Q1');
        assert.equal(Q1[1].name.value, 'F111');
        assert.equal(Q1[2].name.value, 'F222');
        assert.equal(Q2.length, 1);
    });
    it('does not complain when presented with normal comments', function (done) {
        assert.doesNotThrow(function () {
            var query = "#normal comment\n        query {\n          author {\n            ...authorDetails\n          }\n        }";
            var jsSource = loader.call({ cacheable: function () { } }, query);
            var module = { exports: Object.create(null) };
            Function("module", jsSource)(module);
            assert.equal(module.exports.kind, 'Document');
            done();
        });
    });
    it('returns the same object for the same query', function () {
        assert.isTrue(gql(templateObject_9 || (templateObject_9 = __makeTemplateObject(["{ sameQuery }"], ["{ sameQuery }"]))) === gql(templateObject_10 || (templateObject_10 = __makeTemplateObject(["{ sameQuery }"], ["{ sameQuery }"]))));
    });
    it('returns the same object for the same query, even with whitespace differences', function () {
        assert.isTrue(gql(templateObject_11 || (templateObject_11 = __makeTemplateObject(["{ sameQuery }"], ["{ sameQuery }"]))) === gql(templateObject_12 || (templateObject_12 = __makeTemplateObject(["  { sameQuery,   }"], ["  { sameQuery,   }"]))));
    });
    var fragmentAst = gql(templateObject_13 || (templateObject_13 = __makeTemplateObject(["\n  fragment UserFragment on User {\n    firstName\n    lastName\n  }\n"], ["\n  fragment UserFragment on User {\n    firstName\n    lastName\n  }\n"])));
    it('returns the same object for the same fragment', function () {
        assert.isTrue(gql(templateObject_14 || (templateObject_14 = __makeTemplateObject(["fragment same on Same { sameQuery }"], ["fragment same on Same { sameQuery }"]))) === gql(templateObject_15 || (templateObject_15 = __makeTemplateObject(["fragment same on Same { sameQuery }"], ["fragment same on Same { sameQuery }"]))));
    });
    it('returns the same object for the same document with substitution', function () {
        assert.isTrue(gql(templateObject_16 || (templateObject_16 = __makeTemplateObject(["{ ...UserFragment } ", ""], ["{ ...UserFragment } ", ""])), fragmentAst) === gql(templateObject_17 || (templateObject_17 = __makeTemplateObject(["{ ...UserFragment } ", ""], ["{ ...UserFragment } ", ""])), fragmentAst));
    });
    it('can reference a fragment that references as fragment', function () {
        var secondFragmentAst = gql(templateObject_18 || (templateObject_18 = __makeTemplateObject(["\n      fragment SecondUserFragment on User {\n        ...UserFragment\n      }\n      ", "\n    "], ["\n      fragment SecondUserFragment on User {\n        ...UserFragment\n      }\n      ", "\n    "])), fragmentAst);
        var ast = gql(templateObject_19 || (templateObject_19 = __makeTemplateObject(["\n      {\n        user(id: 5) {\n          ...SecondUserFragment\n        }\n      }\n      ", "\n    "], ["\n      {\n        user(id: 5) {\n          ...SecondUserFragment\n        }\n      }\n      ", "\n    "])), secondFragmentAst);
        assert.deepEqual(ast, gql(templateObject_20 || (templateObject_20 = __makeTemplateObject(["\n      {\n        user(id: 5) {\n          ...SecondUserFragment\n        }\n      }\n      fragment SecondUserFragment on User {\n        ...UserFragment\n      }\n      fragment UserFragment on User {\n        firstName\n        lastName\n      }\n    "], ["\n      {\n        user(id: 5) {\n          ...SecondUserFragment\n        }\n      }\n      fragment SecondUserFragment on User {\n        ...UserFragment\n      }\n      fragment UserFragment on User {\n        firstName\n        lastName\n      }\n    "]))));
    });
    describe('fragment warnings', function () {
        var warnings = [];
        var oldConsoleWarn = console.warn;
        beforeEach(function () {
            gql.resetCaches();
            warnings = [];
            console.warn = function (w) { return warnings.push(w); };
        });
        afterEach(function () {
            console.warn = oldConsoleWarn;
        });
        it('warns if you use the same fragment name for different fragments', function () {
            var frag1 = gql(templateObject_21 || (templateObject_21 = __makeTemplateObject(["fragment TestSame on Bar { fieldOne }"], ["fragment TestSame on Bar { fieldOne }"])));
            var frag2 = gql(templateObject_22 || (templateObject_22 = __makeTemplateObject(["fragment TestSame on Bar { fieldTwo }"], ["fragment TestSame on Bar { fieldTwo }"])));
            assert.isFalse(frag1 === frag2);
            assert.equal(warnings.length, 1);
        });
        it('does not warn if you use the same fragment name for the same fragment', function () {
            var frag1 = gql(templateObject_23 || (templateObject_23 = __makeTemplateObject(["fragment TestDifferent on Bar { fieldOne }"], ["fragment TestDifferent on Bar { fieldOne }"])));
            var frag2 = gql(templateObject_24 || (templateObject_24 = __makeTemplateObject(["fragment TestDifferent on Bar { fieldOne }"], ["fragment TestDifferent on Bar { fieldOne }"])));
            assert.isTrue(frag1 === frag2);
            assert.equal(warnings.length, 0);
        });
        it('does not warn if you use the same embedded fragment in two different queries', function () {
            var frag1 = gql(templateObject_25 || (templateObject_25 = __makeTemplateObject(["fragment TestEmbedded on Bar { field }"], ["fragment TestEmbedded on Bar { field }"])));
            var query1 = gql(templateObject_26 || (templateObject_26 = __makeTemplateObject(["{ bar { fieldOne ...TestEmbedded } } ", ""], ["{ bar { fieldOne ...TestEmbedded } } ", ""])), frag1);
            var query2 = gql(templateObject_27 || (templateObject_27 = __makeTemplateObject(["{ bar { fieldTwo ...TestEmbedded } } ", ""], ["{ bar { fieldTwo ...TestEmbedded } } ", ""])), frag1);
            assert.isFalse(query1 === query2);
            assert.equal(warnings.length, 0);
        });
        it('does not warn if you use the same fragment name for embedded and non-embedded fragments', function () {
            var frag1 = gql(templateObject_28 || (templateObject_28 = __makeTemplateObject(["fragment TestEmbeddedTwo on Bar { field }"], ["fragment TestEmbeddedTwo on Bar { field }"])));
            gql(templateObject_29 || (templateObject_29 = __makeTemplateObject(["{ bar { ...TestEmbedded } } ", ""], ["{ bar { ...TestEmbedded } } ", ""])), frag1);
            gql(templateObject_30 || (templateObject_30 = __makeTemplateObject(["{ bar { ...TestEmbedded } } fragment TestEmbeddedTwo on Bar { field }"], ["{ bar { ...TestEmbedded } } fragment TestEmbeddedTwo on Bar { field }"])));
            assert.equal(warnings.length, 0);
        });
    });
    describe('unique fragments', function () {
        beforeEach(function () {
            gql.resetCaches();
        });
        it('strips duplicate fragments from the document', function () {
            var frag1 = gql(templateObject_31 || (templateObject_31 = __makeTemplateObject(["fragment TestDuplicate on Bar { field }"], ["fragment TestDuplicate on Bar { field }"])));
            var query1 = gql(templateObject_32 || (templateObject_32 = __makeTemplateObject(["{ bar { fieldOne ...TestDuplicate } } ", " ", ""], ["{ bar { fieldOne ...TestDuplicate } } ", " ", ""])), frag1, frag1);
            var query2 = gql(templateObject_33 || (templateObject_33 = __makeTemplateObject(["{ bar { fieldOne ...TestDuplicate } } ", ""], ["{ bar { fieldOne ...TestDuplicate } } ", ""])), frag1);
            assert.equal(query1.definitions.length, 2);
            assert.equal(query1.definitions[1].kind, 'FragmentDefinition');
            assert.deepEqual(query1.definitions, query2.definitions);
        });
        it('ignores duplicate fragments from second-level imports when using the webpack loader', function () {
            var load = function (require, query) {
                var jsSource = loader.call({ cacheable: function () { } }, query);
                var module = { exports: Object.create(null) };
                Function("require,module", jsSource)(require, module);
                return module.exports;
            };
            var test_require = function (path) {
                switch (path) {
                    case './friends.graphql':
                        return load(test_require, [
                            '#import "./person.graphql"',
                            'fragment friends on Hero { friends { ...person } }',
                        ].join('\n'));
                    case './enemies.graphql':
                        return load(test_require, [
                            '#import "./person.graphql"',
                            'fragment enemies on Hero { enemies { ...person } }',
                        ].join('\n'));
                    case './person.graphql':
                        return load(test_require, 'fragment person on Person { name }\n');
                    default:
                        return null;
                }
                ;
            };
            var result = load(test_require, [
                '#import "./friends.graphql"',
                '#import "./enemies.graphql"',
                'query { hero { ...friends ...enemies } }',
            ].join('\n'));
            assert.equal(result.kind, 'Document');
            assert.equal(result.definitions.length, 4, 'after deduplication, only 4 fragments should remain');
            assert.equal(result.definitions[0].kind, 'OperationDefinition');
            var fragments = result.definitions.slice(1);
            assert(fragments.every(function (fragment) { return fragment.kind === 'FragmentDefinition'; }));
            assert(fragments.some(function (fragment) { return fragment.name.value === 'friends'; }));
            assert(fragments.some(function (fragment) { return fragment.name.value === 'enemies'; }));
            assert(fragments.some(function (fragment) { return fragment.name.value === 'person'; }));
        });
    });
});
var templateObject_1, templateObject_2, templateObject_3, templateObject_4, templateObject_5, templateObject_6, templateObject_7, templateObject_8, templateObject_9, templateObject_10, templateObject_11, templateObject_12, templateObject_13, templateObject_14, templateObject_15, templateObject_16, templateObject_17, templateObject_18, templateObject_19, templateObject_20, templateObject_21, templateObject_22, templateObject_23, templateObject_24, templateObject_25, templateObject_26, templateObject_27, templateObject_28, templateObject_29, templateObject_30, templateObject_31, templateObject_32, templateObject_33;
//# sourceMappingURL=tests.js.map