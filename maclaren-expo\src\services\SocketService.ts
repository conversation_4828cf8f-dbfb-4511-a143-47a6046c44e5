/**
 * Servicio de Socket.IO para MACLAREN
 * Maneja la comunicación en tiempo real entre conductores y pasajeros
 */

import io, {Socket} from 'socket.io-client';
import {Alert} from 'react-native';

export interface User {
  id: string;
  name: string;
  email: string;
  phone: string;
  userType: 'passenger' | 'driver';
}

export interface TripRequest {
  id: string;
  passenger: string;
  pickup: string;
  destination: string;
  fare: number;
  distance?: number;
  estimatedTime?: number;
}

export interface Message {
  tripId: string;
  senderId: string;
  senderType: 'passenger' | 'driver';
  senderName: string;
  message: string;
  timestamp: Date;
}

export interface LocationUpdate {
  tripId: string;
  userId: string;
  userType: 'passenger' | 'driver';
  latitude: number;
  longitude: number;
  timestamp: Date;
}

class SocketServiceClass {
  private socket: Socket | null = null;
  private currentUser: User | null = null;
  private isConnected = false;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;

  // Callbacks para eventos
  private onTripRequestCallback?: (trip: TripRequest) => void;
  private onTripAcceptedCallback?: (data: any) => void;
  private onNewMessageCallback?: (message: Message) => void;
  private onLocationUpdateCallback?: (location: LocationUpdate) => void;
  private onTripStatusUpdateCallback?: (data: any) => void;
  private onConnectionChangeCallback?: (connected: boolean) => void;

  connect(user: User) {
    if (this.socket && this.isConnected) {
      console.log('Socket ya está conectado');
      return;
    }

    this.currentUser = user;
    
    // Conectar al servidor (cambiar URL en producción)
    this.socket = io('http://localhost:3002', {
      transports: ['websocket'],
      timeout: 10000,
      forceNew: true,
    });

    this.setupEventListeners();
    
    console.log(`🔌 Conectando socket para ${user.name} (${user.userType})`);
  }

  private setupEventListeners() {
    if (!this.socket) return;

    // Eventos de conexión
    this.socket.on('connect', () => {
      console.log('✅ Socket conectado');
      this.isConnected = true;
      this.reconnectAttempts = 0;
      
      // Unirse con datos del usuario
      if (this.currentUser) {
        this.socket?.emit('join', {
          userId: this.currentUser.id,
          userType: this.currentUser.userType,
          name: this.currentUser.name,
        });
      }

      this.onConnectionChangeCallback?.(true);
    });

    this.socket.on('disconnect', (reason) => {
      console.log('❌ Socket desconectado:', reason);
      this.isConnected = false;
      this.onConnectionChangeCallback?.(false);
      
      // Intentar reconectar automáticamente
      if (reason === 'io server disconnect') {
        // El servidor desconectó, reconectar manualmente
        this.reconnect();
      }
    });

    this.socket.on('connect_error', (error) => {
      console.error('❌ Error de conexión:', error);
      this.isConnected = false;
      this.onConnectionChangeCallback?.(false);
      this.reconnect();
    });

    // Eventos de la aplicación
    this.socket.on('new_trip_request', (tripData: TripRequest) => {
      console.log('🚗 Nueva solicitud de viaje:', tripData);
      this.onTripRequestCallback?.(tripData);
    });

    this.socket.on('trip_accepted', (data: any) => {
      console.log('✅ Viaje aceptado:', data);
      this.onTripAcceptedCallback?.(data);
    });

    this.socket.on('new_message', (messageData: Message) => {
      console.log('💬 Nuevo mensaje:', messageData);
      this.onNewMessageCallback?.(messageData);
    });

    this.socket.on('location_updated', (locationData: LocationUpdate) => {
      console.log('📍 Ubicación actualizada:', locationData);
      this.onLocationUpdateCallback?.(locationData);
    });

    this.socket.on('trip_status_updated', (statusData: any) => {
      console.log('🔄 Estado del viaje actualizado:', statusData);
      this.onTripStatusUpdateCallback?.(statusData);
    });
  }

  private reconnect() {
    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      console.log('❌ Máximo de intentos de reconexión alcanzado');
      Alert.alert(
        'Error de Conexión',
        'No se pudo conectar al servidor. Verifica tu conexión a internet.',
      );
      return;
    }

    this.reconnectAttempts++;
    console.log(`🔄 Reintentando conexión (${this.reconnectAttempts}/${this.maxReconnectAttempts})`);
    
    setTimeout(() => {
      if (this.currentUser) {
        this.connect(this.currentUser);
      }
    }, 2000 * this.reconnectAttempts); // Incrementar delay
  }

  disconnect() {
    if (this.socket) {
      this.socket.disconnect();
      this.socket = null;
    }
    this.isConnected = false;
    this.currentUser = null;
    this.reconnectAttempts = 0;
    console.log('🔌 Socket desconectado manualmente');
  }

  // Métodos para enviar eventos
  requestTrip(tripData: Omit<TripRequest, 'id'>) {
    if (!this.socket || !this.isConnected) {
      Alert.alert('Error', 'No hay conexión al servidor');
      return;
    }

    this.socket.emit('new_trip_request', {
      id: `trip_${Date.now()}`,
      ...tripData,
    });
  }

  acceptTrip(tripId: string) {
    if (!this.socket || !this.isConnected) {
      Alert.alert('Error', 'No hay conexión al servidor');
      return;
    }

    this.socket.emit('accept_trip', {
      tripId,
      driverId: this.currentUser?.id,
    });
  }

  sendMessage(tripId: string, message: string) {
    if (!this.socket || !this.isConnected || !this.currentUser) {
      Alert.alert('Error', 'No hay conexión al servidor');
      return;
    }

    const messageData: Message = {
      tripId,
      senderId: this.currentUser.id,
      senderType: this.currentUser.userType,
      senderName: this.currentUser.name,
      message,
      timestamp: new Date(),
    };

    this.socket.emit('send_message', messageData);
  }

  updateLocation(tripId: string, latitude: number, longitude: number) {
    if (!this.socket || !this.isConnected || !this.currentUser) {
      return;
    }

    const locationData: LocationUpdate = {
      tripId,
      userId: this.currentUser.id,
      userType: this.currentUser.userType,
      latitude,
      longitude,
      timestamp: new Date(),
    };

    this.socket.emit('update_location', locationData);
  }

  updateTripStatus(tripId: string, status: string) {
    if (!this.socket || !this.isConnected || !this.currentUser) {
      Alert.alert('Error', 'No hay conexión al servidor');
      return;
    }

    this.socket.emit('update_trip_status', {
      tripId,
      status,
      userId: this.currentUser.id,
    });
  }

  // Métodos para registrar callbacks
  onTripRequest(callback: (trip: TripRequest) => void) {
    this.onTripRequestCallback = callback;
  }

  onTripAccepted(callback: (data: any) => void) {
    this.onTripAcceptedCallback = callback;
  }

  onNewMessage(callback: (message: Message) => void) {
    this.onNewMessageCallback = callback;
  }

  onLocationUpdate(callback: (location: LocationUpdate) => void) {
    this.onLocationUpdateCallback = callback;
  }

  onTripStatusUpdate(callback: (data: any) => void) {
    this.onTripStatusUpdateCallback = callback;
  }

  onConnectionChange(callback: (connected: boolean) => void) {
    this.onConnectionChangeCallback = callback;
  }

  // Getters
  get connected() {
    return this.isConnected;
  }

  get user() {
    return this.currentUser;
  }
}

export const SocketService = new SocketServiceClass();
