{"expo": {"name": "MACLAREN", "slug": "maclaren-moto-app", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/icon.png", "userInterfaceStyle": "light", "splash": {"image": "./assets/splash.png", "resizeMode": "contain", "backgroundColor": "#ff6b35"}, "assetBundlePatterns": ["**/*"], "ios": {"supportsTablet": false, "bundleIdentifier": "com.maclaren.motoapp", "infoPlist": {"NSLocationWhenInUseUsageDescription": "MACLAREN necesita acceso a tu ubicación para encontrar conductores cercanos y mostrar tu posición en tiempo real.", "NSLocationAlwaysAndWhenInUseUsageDescription": "MACLAREN necesita acceso a tu ubicación para encontrar conductores cercanos y mostrar tu posición en tiempo real.", "NSCameraUsageDescription": "MACLAREN necesita acceso a la cámara para tomar fotos de perfil y documentos.", "NSMicrophoneUsageDescription": "MACLAREN necesita acceso al micrófono para llamadas de voz."}}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/adaptive-icon.png", "backgroundColor": "#ff6b35"}, "package": "com.maclaren.motoapp", "permissions": ["ACCESS_FINE_LOCATION", "ACCESS_COARSE_LOCATION", "CAMERA", "RECORD_AUDIO", "VIBRATE", "RECEIVE_BOOT_COMPLETED"]}, "web": {"favicon": "./assets/favicon.png"}, "plugins": ["expo-location", "expo-notifications", "expo-camera", "expo-image-picker", ["expo-splash-screen", {"backgroundColor": "#ff6b35", "image": "./assets/splash.png", "imageWidth": 200}]], "extra": {"eas": {"projectId": "your-project-id-here"}}}}