{"version": 3, "names": ["React", "createContext", "undefined"], "sources": ["TransitionProgressContext.tsx"], "sourcesContent": ["import * as React from 'react';\nimport { Animated } from 'react-native';\n\ntype TransitionProgressContextBody = {\n  progress: Animated.Value;\n  closing: Animated.Value;\n  goingForward: Animated.Value;\n};\n\nexport default React.createContext<TransitionProgressContextBody | undefined>(\n  undefined\n);\n"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAS9B,4BAAeA,KAAK,CAACC,aAAa,CAChCC,SACF,CAAC"}