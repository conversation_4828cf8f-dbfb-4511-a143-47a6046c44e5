/**
 * Pantalla de Bienvenida - MACLAREN Expo
 * Primera pantalla que ve el usuario
 */

import React, { useEffect } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Dimensions,
  Image,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import * as Animatable from 'react-native-animatable';
import { StatusBar } from 'expo-status-bar';

const { width, height } = Dimensions.get('window');

const WelcomeScreen = ({ navigation }) => {
  useEffect(() => {
    // Auto-navegar después de 3 segundos si el usuario no hace nada
    const timer = setTimeout(() => {
      // navigation.navigate('Login');
    }, 10000);

    return () => clearTimeout(timer);
  }, []);

  const handleGetStarted = () => {
    navigation.navigate('Login');
  };

  return (
    <LinearGradient
      colors={['#ff6b35', '#f7931e']}
      style={styles.container}>
      
      <StatusBar style="light" />
      
      {/* Logo y título principal */}
      <Animatable.View 
        animation="fadeInDown" 
        duration={1000}
        style={styles.logoContainer}>
        
        <View style={styles.logoCircle}>
          <Text style={styles.logoEmoji}>🏍️</Text>
        </View>
        
        <Text style={styles.appName}>MACLAREN</Text>
        <Text style={styles.tagline}>Tu moto, tu destino</Text>
      </Animatable.View>

      {/* Características principales */}
      <Animatable.View 
        animation="fadeInUp" 
        duration={1000}
        delay={500}
        style={styles.featuresContainer}>
        
        <View style={styles.featureItem}>
          <Text style={styles.featureIcon}>⚡</Text>
          <Text style={styles.featureText}>Viajes rápidos y seguros</Text>
        </View>
        
        <View style={styles.featureItem}>
          <Text style={styles.featureIcon}>💰</Text>
          <Text style={styles.featureText}>Tarifas justas y transparentes</Text>
        </View>
        
        <View style={styles.featureItem}>
          <Text style={styles.featureIcon}>📍</Text>
          <Text style={styles.featureText}>Seguimiento en tiempo real</Text>
        </View>
        
        <View style={styles.featureItem}>
          <Text style={styles.featureIcon}>💬</Text>
          <Text style={styles.featureText}>Chat directo con tu conductor</Text>
        </View>
      </Animatable.View>

      {/* Botones de acción */}
      <Animatable.View 
        animation="fadeInUp" 
        duration={1000}
        delay={1000}
        style={styles.buttonsContainer}>
        
        <TouchableOpacity
          style={styles.primaryButton}
          onPress={handleGetStarted}
          activeOpacity={0.8}>
          <Text style={styles.primaryButtonText}>🚀 Comenzar</Text>
        </TouchableOpacity>
        
        <View style={styles.userTypeButtons}>
          <TouchableOpacity
            style={styles.userTypeButton}
            onPress={() => navigation.navigate('Login', { userType: 'passenger' })}
            activeOpacity={0.8}>
            <Text style={styles.userTypeIcon}>👤</Text>
            <Text style={styles.userTypeText}>Soy Pasajero</Text>
          </TouchableOpacity>
          
          <TouchableOpacity
            style={styles.userTypeButton}
            onPress={() => navigation.navigate('Login', { userType: 'driver' })}
            activeOpacity={0.8}>
            <Text style={styles.userTypeIcon}>🏍️</Text>
            <Text style={styles.userTypeText}>Soy Conductor</Text>
          </TouchableOpacity>
        </View>
      </Animatable.View>

      {/* Footer */}
      <Animatable.View 
        animation="fadeIn" 
        duration={1000}
        delay={1500}
        style={styles.footer}>
        <Text style={styles.footerText}>
          Conectando motociclistas con pasajeros
        </Text>
        <Text style={styles.versionText}>v1.0.0</Text>
      </Animatable.View>
    </LinearGradient>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 30,
    paddingVertical: 60,
  },
  logoContainer: {
    alignItems: 'center',
    marginTop: 40,
  },
  logoCircle: {
    width: 120,
    height: 120,
    borderRadius: 60,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 20,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.3,
    shadowRadius: 4.65,
    elevation: 8,
  },
  logoEmoji: {
    fontSize: 60,
  },
  appName: {
    fontSize: 36,
    fontWeight: 'bold',
    color: 'white',
    marginBottom: 10,
    textAlign: 'center',
    letterSpacing: 2,
  },
  tagline: {
    fontSize: 18,
    color: 'rgba(255, 255, 255, 0.9)',
    textAlign: 'center',
    fontStyle: 'italic',
  },
  featuresContainer: {
    width: '100%',
    paddingVertical: 20,
  },
  featureItem: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.15)',
    borderRadius: 15,
    paddingHorizontal: 20,
    paddingVertical: 15,
    marginBottom: 12,
  },
  featureIcon: {
    fontSize: 24,
    marginRight: 15,
  },
  featureText: {
    fontSize: 16,
    color: 'white',
    fontWeight: '600',
    flex: 1,
  },
  buttonsContainer: {
    width: '100%',
    alignItems: 'center',
  },
  primaryButton: {
    backgroundColor: 'white',
    borderRadius: 25,
    paddingVertical: 15,
    paddingHorizontal: 40,
    marginBottom: 20,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.3,
    shadowRadius: 4.65,
    elevation: 8,
    minWidth: 200,
  },
  primaryButtonText: {
    color: '#ff6b35',
    fontSize: 18,
    fontWeight: 'bold',
    textAlign: 'center',
  },
  userTypeButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    width: '100%',
  },
  userTypeButton: {
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    borderRadius: 15,
    paddingVertical: 15,
    paddingHorizontal: 20,
    alignItems: 'center',
    flex: 1,
    marginHorizontal: 5,
  },
  userTypeIcon: {
    fontSize: 24,
    marginBottom: 5,
  },
  userTypeText: {
    color: 'white',
    fontSize: 14,
    fontWeight: '600',
    textAlign: 'center',
  },
  footer: {
    alignItems: 'center',
  },
  footerText: {
    color: 'rgba(255, 255, 255, 0.8)',
    fontSize: 14,
    textAlign: 'center',
    marginBottom: 5,
  },
  versionText: {
    color: 'rgba(255, 255, 255, 0.6)',
    fontSize: 12,
  },
});

export default WelcomeScreen;
