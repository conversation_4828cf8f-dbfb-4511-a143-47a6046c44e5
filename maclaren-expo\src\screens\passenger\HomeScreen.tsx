/**
 * Pantalla Principal del Pasajero - MACLAREN
 * Solicitar viajes, ver estado y comunicarse con conductor
 */

import React, {useState, useEffect, useRef} from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  Alert,
  ScrollView,
  Dimensions,
  StatusBar,
  Modal,
} from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import * as Animatable from 'react-native-animatable';
import {SocketService, TripRequest} from '../../services/SocketService';
import {LocationService} from '../../services/LocationService';
import {NotificationService} from '../../services/NotificationService';

const {width, height} = Dimensions.get('window');

interface FareOption {
  id: string;
  name: string;
  description: string;
  price: number;
  icon: string;
  distance: string;
  time: string;
}

const HomeScreen: React.FC = () => {
  const [pickup, setPickup] = useState('');
  const [destination, setDestination] = useState('');
  const [selectedFare, setSelectedFare] = useState<FareOption | null>(null);
  const [currentTrip, setCurrentTrip] = useState<any>(null);
  const [tripStatus, setTripStatus] = useState<string>('');
  const [driverInfo, setDriverInfo] = useState<any>(null);
  const [isRequestingTrip, setIsRequestingTrip] = useState(false);
  const [showTripModal, setShowTripModal] = useState(false);
  const [userLocation, setUserLocation] = useState<any>(null);

  const fareOptions: FareOption[] = [
    {
      id: 'express',
      name: 'MACLAREN Express',
      description: '1-3 km • 5-10 min',
      price: 5000,
      icon: '🏍️',
      distance: '1-3 km',
      time: '5-10 min',
    },
    {
      id: 'standard',
      name: 'MACLAREN Standard',
      description: '3-6 km • 10-15 min',
      price: 8000,
      icon: '🏍️',
      distance: '3-6 km',
      time: '10-15 min',
    },
    {
      id: 'plus',
      name: 'MACLAREN Plus',
      description: '6-10 km • 15-25 min',
      price: 12000,
      icon: '🏍️',
      distance: '6-10 km',
      time: '15-25 min',
    },
  ];

  useEffect(() => {
    initializeScreen();
    setupSocketListeners();
    getCurrentLocation();

    return () => {
      // Cleanup
    };
  }, []);

  const initializeScreen = () => {
    StatusBar.setBarStyle('light-content');
    StatusBar.setBackgroundColor('#ff6b35');
  };

  const setupSocketListeners = () => {
    SocketService.onTripAccepted((data) => {
      setTripStatus('accepted');
      setDriverInfo({
        name: 'Juan Pérez',
        phone: '+57 ************',
        vehicle: 'Honda CB 160F',
        plate: 'ABC-123',
        rating: 4.8,
      });
      setShowTripModal(true);
      
      NotificationService.showNotification(
        '¡Viaje aceptado!',
        'Un conductor ha aceptado tu viaje',
      );
    });

    SocketService.onTripStatusUpdate((data) => {
      setTripStatus(data.status);
      
      if (data.status === 'completed') {
        Alert.alert(
          '¡Viaje completado!',
          'Gracias por usar MACLAREN. ¿Cómo calificarías tu experiencia?',
          [
            {text: 'Después', style: 'cancel'},
            {text: 'Calificar', onPress: () => showRatingModal()},
          ],
        );
        resetTrip();
      }
    });

    SocketService.onNewMessage((message) => {
      if (currentTrip && message.tripId === currentTrip.id) {
        NotificationService.showNotification(
          `Mensaje de ${message.senderName}`,
          message.message,
        );
      }
    });
  };

  const getCurrentLocation = async () => {
    try {
      const location = await LocationService.getCurrentPosition();
      setUserLocation(location);
      
      // Simular dirección (en producción usar geocoding)
      setPickup('Mi ubicación actual');
    } catch (error) {
      console.error('Error obteniendo ubicación:', error);
      Alert.alert(
        'Ubicación',
        'No se pudo obtener tu ubicación. Por favor activa el GPS.',
      );
    }
  };

  const requestTrip = async () => {
    if (!pickup.trim() || !destination.trim() || !selectedFare) {
      Alert.alert('Error', 'Por favor completa todos los campos');
      return;
    }

    setIsRequestingTrip(true);

    try {
      const tripData = {
        passenger: SocketService.user?.name || 'Usuario',
        pickup: pickup.trim(),
        destination: destination.trim(),
        fare: selectedFare.price,
        distance: parseFloat(selectedFare.distance.split('-')[1]) || 5,
        estimatedTime: parseInt(selectedFare.time.split('-')[1]) || 10,
      };

      // Enviar solicitud via Socket
      SocketService.requestTrip(tripData);

      // Crear trip local
      const newTrip = {
        id: `trip_${Date.now()}`,
        ...tripData,
        status: 'searching',
        createdAt: new Date(),
      };

      setCurrentTrip(newTrip);
      setTripStatus('searching');
      setShowTripModal(true);

      console.log('✅ Viaje solicitado:', newTrip);
    } catch (error) {
      console.error('Error solicitando viaje:', error);
      Alert.alert('Error', 'No se pudo solicitar el viaje');
    } finally {
      setIsRequestingTrip(false);
    }
  };

  const cancelTrip = () => {
    Alert.alert(
      'Cancelar Viaje',
      '¿Estás seguro de que quieres cancelar el viaje?',
      [
        {text: 'No', style: 'cancel'},
        {
          text: 'Sí, cancelar',
          style: 'destructive',
          onPress: () => {
            if (currentTrip) {
              SocketService.updateTripStatus(currentTrip.id, 'cancelled');
            }
            resetTrip();
          },
        },
      ],
    );
  };

  const resetTrip = () => {
    setCurrentTrip(null);
    setTripStatus('');
    setDriverInfo(null);
    setShowTripModal(false);
    setDestination('');
    setSelectedFare(null);
  };

  const showRatingModal = () => {
    // Implementar modal de calificación
    Alert.alert('Calificación', 'Función de calificación en desarrollo');
  };

  const openChat = () => {
    // Navegar a pantalla de chat
    Alert.alert('Chat', 'Función de chat en desarrollo');
  };

  const callDriver = () => {
    if (driverInfo?.phone) {
      Alert.alert(
        'Llamar Conductor',
        `¿Quieres llamar a ${driverInfo.name}?`,
        [
          {text: 'Cancelar', style: 'cancel'},
          {text: 'Llamar', onPress: () => {
            // Implementar llamada
            console.log('Llamando a:', driverInfo.phone);
          }},
        ],
      );
    }
  };

  const getTripStatusInfo = () => {
    switch (tripStatus) {
      case 'searching':
        return {
          icon: '⏳',
          title: 'Buscando conductor...',
          message: 'Estamos encontrando el motero más cercano',
          color: '#f39c12',
        };
      case 'accepted':
        return {
          icon: '✅',
          title: '¡Conductor encontrado!',
          message: 'Tu motero está en camino',
          color: '#27ae60',
        };
      case 'in_progress':
        return {
          icon: '🚗',
          title: 'Viaje en curso',
          message: 'Disfruta tu viaje',
          color: '#3498db',
        };
      default:
        return {
          icon: '⏳',
          title: 'Preparando...',
          message: 'Un momento por favor',
          color: '#95a5a6',
        };
    }
  };

  const statusInfo = getTripStatusInfo();

  return (
    <LinearGradient colors={['#ff6b35', '#f7931e']} style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor="#ff6b35" />
      
      {/* Header */}
      <View style={styles.header}>
        <Text style={styles.greeting}>¡Hola! 👋</Text>
        <Text style={styles.question}>¿A dónde vamos?</Text>
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        
        {/* Ubicaciones */}
        <Animatable.View animation="fadeInUp" duration={600} style={styles.locationCard}>
          <View style={styles.locationInput}>
            <Text style={styles.locationIcon}>📍</Text>
            <TextInput
              style={styles.locationText}
              placeholder="Punto de recogida"
              placeholderTextColor="#999"
              value={pickup}
              onChangeText={setPickup}
              editable={!currentTrip}
            />
            <TouchableOpacity onPress={getCurrentLocation}>
              <Text style={styles.gpsIcon}>🎯</Text>
            </TouchableOpacity>
          </View>

          <View style={styles.locationInput}>
            <Text style={styles.locationIcon}>🎯</Text>
            <TextInput
              style={styles.locationText}
              placeholder="¿A dónde vas?"
              placeholderTextColor="#999"
              value={destination}
              onChangeText={setDestination}
              editable={!currentTrip}
            />
          </View>
        </Animatable.View>

        {/* Opciones de Tarifa */}
        {!currentTrip && (
          <Animatable.View animation="fadeInUp" duration={600} delay={200} style={styles.fareCard}>
            <Text style={styles.fareTitle}>Elige tu viaje</Text>
            {fareOptions.map((option) => (
              <TouchableOpacity
                key={option.id}
                style={[
                  styles.fareOption,
                  selectedFare?.id === option.id && styles.fareOptionSelected,
                ]}
                onPress={() => setSelectedFare(option)}>
                <View style={styles.fareInfo}>
                  <Text style={styles.fareIcon}>{option.icon}</Text>
                  <View style={styles.fareDetails}>
                    <Text style={styles.fareName}>{option.name}</Text>
                    <Text style={styles.fareDescription}>{option.description}</Text>
                  </View>
                </View>
                <Text style={styles.farePrice}>${option.price.toLocaleString()}</Text>
              </TouchableOpacity>
            ))}
          </Animatable.View>
        )}

        {/* Botón de Solicitar */}
        {!currentTrip && (
          <Animatable.View animation="fadeInUp" duration={600} delay={400}>
            <TouchableOpacity
              style={[
                styles.requestButton,
                (!pickup || !destination || !selectedFare || isRequestingTrip) && styles.requestButtonDisabled,
              ]}
              onPress={requestTrip}
              disabled={!pickup || !destination || !selectedFare || isRequestingTrip}>
              <Text style={styles.requestButtonText}>
                {isRequestingTrip
                  ? 'Solicitando...'
                  : selectedFare
                  ? `Solicitar viaje por $${selectedFare.price.toLocaleString()}`
                  : 'Completa todos los campos'}
              </Text>
            </TouchableOpacity>
          </Animatable.View>
        )}
      </ScrollView>

      {/* Modal de Estado del Viaje */}
      <Modal
        visible={showTripModal}
        animationType="slide"
        transparent={true}
        onRequestClose={() => {}}>
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            <View style={styles.statusHeader}>
              <Text style={styles.statusIcon}>{statusInfo.icon}</Text>
              <Text style={styles.statusTitle}>{statusInfo.title}</Text>
              <Text style={styles.statusMessage}>{statusInfo.message}</Text>
            </View>

            {driverInfo && (
              <View style={styles.driverCard}>
                <View style={styles.driverInfo}>
                  <View style={styles.driverAvatar}>
                    <Text style={styles.driverAvatarText}>👤</Text>
                  </View>
                  <View style={styles.driverDetails}>
                    <Text style={styles.driverName}>{driverInfo.name}</Text>
                    <Text style={styles.driverVehicle}>
                      ⭐ {driverInfo.rating} • {driverInfo.vehicle} • {driverInfo.plate}
                    </Text>
                    <Text style={styles.driverPhone}>📱 {driverInfo.phone}</Text>
                  </View>
                </View>

                <View style={styles.driverActions}>
                  <TouchableOpacity style={styles.actionButton} onPress={callDriver}>
                    <Text style={styles.actionButtonText}>📞 Llamar</Text>
                  </TouchableOpacity>
                  <TouchableOpacity style={[styles.actionButton, styles.actionButtonPrimary]} onPress={openChat}>
                    <Text style={[styles.actionButtonText, styles.actionButtonTextPrimary]}>💬 Chat</Text>
                  </TouchableOpacity>
                </View>
              </View>
            )}

            <TouchableOpacity style={styles.cancelButton} onPress={cancelTrip}>
              <Text style={styles.cancelButtonText}>❌ Cancelar Viaje</Text>
            </TouchableOpacity>
          </View>
        </View>
      </Modal>
    </LinearGradient>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    paddingTop: 60,
    paddingHorizontal: 20,
    paddingBottom: 20,
  },
  greeting: {
    fontSize: 24,
    fontWeight: 'bold',
    color: 'white',
    marginBottom: 5,
  },
  question: {
    fontSize: 18,
    color: 'white',
    opacity: 0.9,
  },
  content: {
    flex: 1,
    paddingHorizontal: 20,
  },
  locationCard: {
    backgroundColor: 'white',
    borderRadius: 15,
    padding: 20,
    marginBottom: 20,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  locationInput: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 15,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  locationIcon: {
    fontSize: 20,
    marginRight: 15,
    width: 25,
    textAlign: 'center',
  },
  locationText: {
    flex: 1,
    fontSize: 16,
    color: '#333',
  },
  gpsIcon: {
    fontSize: 20,
    color: '#ff6b35',
  },
  fareCard: {
    backgroundColor: 'white',
    borderRadius: 15,
    padding: 20,
    marginBottom: 20,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  fareTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 15,
  },
  fareOption: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 15,
    paddingHorizontal: 15,
    borderRadius: 10,
    borderWidth: 2,
    borderColor: '#f0f0f0',
    marginBottom: 10,
  },
  fareOptionSelected: {
    borderColor: '#ff6b35',
    backgroundColor: '#fff5f2',
  },
  fareInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  fareIcon: {
    fontSize: 24,
    marginRight: 15,
  },
  fareDetails: {
    flex: 1,
  },
  fareName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 2,
  },
  fareDescription: {
    fontSize: 14,
    color: '#666',
  },
  farePrice: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#ff6b35',
  },
  requestButton: {
    backgroundColor: 'rgba(255,255,255,0.2)',
    borderRadius: 15,
    padding: 18,
    alignItems: 'center',
    marginBottom: 20,
    borderWidth: 2,
    borderColor: 'rgba(255,255,255,0.3)',
  },
  requestButtonDisabled: {
    opacity: 0.5,
  },
  requestButtonText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: 'white',
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0,0,0,0.5)',
    justifyContent: 'flex-end',
  },
  modalContent: {
    backgroundColor: 'white',
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    padding: 20,
    minHeight: height * 0.4,
  },
  statusHeader: {
    alignItems: 'center',
    marginBottom: 20,
  },
  statusIcon: {
    fontSize: 60,
    marginBottom: 10,
  },
  statusTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 5,
  },
  statusMessage: {
    fontSize: 14,
    color: '#666',
    textAlign: 'center',
  },
  driverCard: {
    backgroundColor: '#f8f9fa',
    borderRadius: 15,
    padding: 20,
    marginBottom: 20,
  },
  driverInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 15,
  },
  driverAvatar: {
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: '#ff6b35',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 15,
  },
  driverAvatarText: {
    fontSize: 24,
    color: 'white',
  },
  driverDetails: {
    flex: 1,
  },
  driverName: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 2,
  },
  driverVehicle: {
    fontSize: 14,
    color: '#666',
    marginBottom: 2,
  },
  driverPhone: {
    fontSize: 14,
    color: '#666',
  },
  driverActions: {
    flexDirection: 'row',
    gap: 10,
  },
  actionButton: {
    flex: 1,
    backgroundColor: '#e9ecef',
    borderRadius: 10,
    padding: 12,
    alignItems: 'center',
  },
  actionButtonPrimary: {
    backgroundColor: '#ff6b35',
  },
  actionButtonText: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#333',
  },
  actionButtonTextPrimary: {
    color: 'white',
  },
  cancelButton: {
    backgroundColor: '#e74c3c',
    borderRadius: 10,
    padding: 15,
    alignItems: 'center',
  },
  cancelButtonText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: 'white',
  },
});

export default HomeScreen;
