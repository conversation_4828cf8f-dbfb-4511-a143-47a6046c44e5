{"version": 3, "file": "graphql-tag.umd.js", "sources": ["../src/index.ts"], "sourcesContent": ["import { parse } from 'graphql';\n\nimport {\n  DocumentNode,\n  DefinitionNode,\n  Location,\n} from 'graphql/language/ast';\n\n// A map docString -> graphql document\nconst docCache = new Map<string, DocumentNode>();\n\n// A map fragmentName -> [normalized source]\nconst fragmentSourceMap = new Map<string, Set<string>>();\n\nlet printFragmentWarnings = true;\nlet experimentalFragmentVariables = false;\n\n// Strip insignificant whitespace\n// Note that this could do a lot more, such as reorder fields etc.\nfunction normalize(string: string) {\n  return string.replace(/[\\s,]+/g, ' ').trim();\n}\n\nfunction cacheKeyFromLoc(loc: Location) {\n  return normalize(loc.source.body.substring(loc.start, loc.end));\n}\n\n// Take a unstripped parsed document (query/mutation or even fragment), and\n// check all fragment definitions, checking for name->source uniqueness.\n// We also want to make sure only unique fragments exist in the document.\nfunction processFragments(ast: DocumentNode) {\n  const seenKeys = new Set<string>();\n  const definitions: DefinitionNode[] = [];\n\n  ast.definitions.forEach(fragmentDefinition => {\n    if (fragmentDefinition.kind === 'FragmentDefinition') {\n      var fragmentName = fragmentDefinition.name.value;\n      var sourceKey = cacheKeyFromLoc(fragmentDefinition.loc!);\n\n      // We know something about this fragment\n      let sourceKeySet = fragmentSourceMap.get(fragmentName)!;\n      if (sourceKeySet && !sourceKeySet.has(sourceKey)) {\n        // this is a problem because the app developer is trying to register another fragment with\n        // the same name as one previously registered. So, we tell them about it.\n        if (printFragmentWarnings) {\n          console.warn(\"Warning: fragment with name \" + fragmentName + \" already exists.\\n\"\n            + \"graphql-tag enforces all fragment names across your application to be unique; read more about\\n\"\n            + \"this in the docs: http://dev.apollodata.com/core/fragments.html#unique-names\");\n        }\n      } else if (!sourceKeySet) {\n        fragmentSourceMap.set(fragmentName, sourceKeySet = new Set);\n      }\n\n      sourceKeySet.add(sourceKey);\n\n      if (!seenKeys.has(sourceKey)) {\n        seenKeys.add(sourceKey);\n        definitions.push(fragmentDefinition);\n      }\n    } else {\n      definitions.push(fragmentDefinition);\n    }\n  });\n\n  return {\n    ...ast,\n    definitions,\n  };\n}\n\nfunction stripLoc(doc: DocumentNode) {\n  const workSet = new Set<Record<string, any>>(doc.definitions);\n\n  workSet.forEach(node => {\n    if (node.loc) delete node.loc;\n    Object.keys(node).forEach(key => {\n      const value = node[key];\n      if (value && typeof value === 'object') {\n        workSet.add(value);\n      }\n    });\n  });\n\n  const loc = doc.loc as Record<string, any>;\n  if (loc) {\n    delete loc.startToken;\n    delete loc.endToken;\n  }\n\n  return doc;\n}\n\nfunction parseDocument(source: string) {\n  var cacheKey = normalize(source);\n  if (!docCache.has(cacheKey)) {\n    const parsed = parse(source, {\n      experimentalFragmentVariables,\n      allowLegacyFragmentVariables: experimentalFragmentVariables,\n    } as any);\n    if (!parsed || parsed.kind !== 'Document') {\n      throw new Error('Not a valid GraphQL document.');\n    }\n    docCache.set(\n      cacheKey,\n      // check that all \"new\" fragments inside the documents are consistent with\n      // existing fragments of the same name\n      stripLoc(processFragments(parsed)),\n    );\n  }\n  return docCache.get(cacheKey)!;\n}\n\n// XXX This should eventually disallow arbitrary string interpolation, like Relay does\nexport function gql(\n  literals: string | readonly string[],\n  ...args: any[]\n) {\n\n  if (typeof literals === 'string') {\n    literals = [literals];\n  }\n\n  let result = literals[0];\n\n  args.forEach((arg, i) => {\n    if (arg && arg.kind === 'Document') {\n      result += arg.loc.source.body;\n    } else {\n      result += arg;\n    }\n    result += literals[i + 1];\n  });\n\n  return parseDocument(result);\n}\n\nexport function resetCaches() {\n  docCache.clear();\n  fragmentSourceMap.clear();\n}\n\nexport function disableFragmentWarnings() {\n  printFragmentWarnings = false;\n}\n\nexport function enableExperimentalFragmentVariables() {\n  experimentalFragmentVariables = true;\n}\n\nexport function disableExperimentalFragmentVariables() {\n  experimentalFragmentVariables = false;\n}\n\nconst extras = {\n  gql,\n  resetCaches,\n  disableFragmentWarnings,\n  enableExperimentalFragmentVariables,\n  disableExperimentalFragmentVariables,\n};\n\nexport namespace gql {\n  export const {\n    gql,\n    resetCaches,\n    disableFragmentWarnings,\n    enableExperimentalFragmentVariables,\n    disableExperimentalFragmentVariables,\n  } = extras;\n}\n\ngql.default = gql;\n\nexport default gql;\n"], "names": ["parse"], "mappings": ";;;;;;IASA,IAAM,QAAQ,GAAG,IAAI,GAAG,EAAwB,CAAC;IAGjD,IAAM,iBAAiB,GAAG,IAAI,GAAG,EAAuB,CAAC;IAEzD,IAAI,qBAAqB,GAAG,IAAI,CAAC;IACjC,IAAI,6BAA6B,GAAG,KAAK,CAAC;IAI1C,SAAS,SAAS,CAAC,MAAc;QAC/B,OAAO,MAAM,CAAC,OAAO,CAAC,SAAS,EAAE,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC;IAC/C,CAAC;IAED,SAAS,eAAe,CAAC,GAAa;QACpC,OAAO,SAAS,CAAC,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,KAAK,EAAE,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;IAClE,CAAC;IAKD,SAAS,gBAAgB,CAAC,GAAiB;QACzC,IAAM,QAAQ,GAAG,IAAI,GAAG,EAAU,CAAC;QACnC,IAAM,WAAW,GAAqB,EAAE,CAAC;QAEzC,GAAG,CAAC,WAAW,CAAC,OAAO,CAAC,UAAA,kBAAkB;YACxC,IAAI,kBAAkB,CAAC,IAAI,KAAK,oBAAoB,EAAE;gBACpD,IAAI,YAAY,GAAG,kBAAkB,CAAC,IAAI,CAAC,KAAK,CAAC;gBACjD,IAAI,SAAS,GAAG,eAAe,CAAC,kBAAkB,CAAC,GAAI,CAAC,CAAC;gBAGzD,IAAI,YAAY,GAAG,iBAAiB,CAAC,GAAG,CAAC,YAAY,CAAE,CAAC;gBACxD,IAAI,YAAY,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,SAAS,CAAC,EAAE;oBAGhD,IAAI,qBAAqB,EAAE;wBACzB,OAAO,CAAC,IAAI,CAAC,8BAA8B,GAAG,YAAY,GAAG,oBAAoB;8BAC7E,iGAAiG;8BACjG,8EAA8E,CAAC,CAAC;qBACrF;iBACF;qBAAM,IAAI,CAAC,YAAY,EAAE;oBACxB,iBAAiB,CAAC,GAAG,CAAC,YAAY,EAAE,YAAY,GAAG,IAAI,GAAG,CAAC,CAAC;iBAC7D;gBAED,YAAY,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;gBAE5B,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,SAAS,CAAC,EAAE;oBAC5B,QAAQ,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;oBACxB,WAAW,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;iBACtC;aACF;iBAAM;gBACL,WAAW,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;aACtC;SACF,CAAC,CAAC;QAEH,yCACK,GAAG,KACN,WAAW,aAAA,IACX;IACJ,CAAC;IAED,SAAS,QAAQ,CAAC,GAAiB;QACjC,IAAM,OAAO,GAAG,IAAI,GAAG,CAAsB,GAAG,CAAC,WAAW,CAAC,CAAC;QAE9D,OAAO,CAAC,OAAO,CAAC,UAAA,IAAI;YAClB,IAAI,IAAI,CAAC,GAAG;gBAAE,OAAO,IAAI,CAAC,GAAG,CAAC;YAC9B,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,UAAA,GAAG;gBAC3B,IAAM,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC;gBACxB,IAAI,KAAK,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;oBACtC,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;iBACpB;aACF,CAAC,CAAC;SACJ,CAAC,CAAC;QAEH,IAAM,GAAG,GAAG,GAAG,CAAC,GAA0B,CAAC;QAC3C,IAAI,GAAG,EAAE;YACP,OAAO,GAAG,CAAC,UAAU,CAAC;YACtB,OAAO,GAAG,CAAC,QAAQ,CAAC;SACrB;QAED,OAAO,GAAG,CAAC;IACb,CAAC;IAED,SAAS,aAAa,CAAC,MAAc;QACnC,IAAI,QAAQ,GAAG,SAAS,CAAC,MAAM,CAAC,CAAC;QACjC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE;YAC3B,IAAM,MAAM,GAAGA,aAAK,CAAC,MAAM,EAAE;gBAC3B,6BAA6B,+BAAA;gBAC7B,4BAA4B,EAAE,6BAA6B;aACrD,CAAC,CAAC;YACV,IAAI,CAAC,MAAM,IAAI,MAAM,CAAC,IAAI,KAAK,UAAU,EAAE;gBACzC,MAAM,IAAI,KAAK,CAAC,+BAA+B,CAAC,CAAC;aAClD;YACD,QAAQ,CAAC,GAAG,CACV,QAAQ,EAGR,QAAQ,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAC,CACnC,CAAC;SACH;QACD,OAAO,QAAQ,CAAC,GAAG,CAAC,QAAQ,CAAE,CAAC;IACjC,CAAC;aAGe,GAAG,CACjB,QAAoC;QACpC,cAAc;aAAd,UAAc,EAAd,qBAAc,EAAd,IAAc;YAAd,6BAAc;;QAGd,IAAI,OAAO,QAAQ,KAAK,QAAQ,EAAE;YAChC,QAAQ,GAAG,CAAC,QAAQ,CAAC,CAAC;SACvB;QAED,IAAI,MAAM,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;QAEzB,IAAI,CAAC,OAAO,CAAC,UAAC,GAAG,EAAE,CAAC;YAClB,IAAI,GAAG,IAAI,GAAG,CAAC,IAAI,KAAK,UAAU,EAAE;gBAClC,MAAM,IAAI,GAAG,CAAC,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC;aAC/B;iBAAM;gBACL,MAAM,IAAI,GAAG,CAAC;aACf;YACD,MAAM,IAAI,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;SAC3B,CAAC,CAAC;QAEH,OAAO,aAAa,CAAC,MAAM,CAAC,CAAC;IAC/B,CAAC;aAEe,WAAW;QACzB,QAAQ,CAAC,KAAK,EAAE,CAAC;QACjB,iBAAiB,CAAC,KAAK,EAAE,CAAC;IAC5B,CAAC;aAEe,uBAAuB;QACrC,qBAAqB,GAAG,KAAK,CAAC;IAChC,CAAC;aAEe,mCAAmC;QACjD,6BAA6B,GAAG,IAAI,CAAC;IACvC,CAAC;aAEe,oCAAoC;QAClD,6BAA6B,GAAG,KAAK,CAAC;IACxC,CAAC;IAED,IAAM,MAAM,GAAG;QACb,GAAG,KAAA;QACH,WAAW,aAAA;QACX,uBAAuB,yBAAA;QACvB,mCAAmC,qCAAA;QACnC,oCAAoC,sCAAA;KACrC,CAAC;IAEF,WAAiB,KAAG;QAEhB,SAAG,GAKD,MAAM,IALL,EACH,iBAAW,GAIT,MAAM,YAJG,EACX,6BAAuB,GAGrB,MAAM,wBAHe,EACvB,yCAAmC,GAEjC,MAAM,oCAF2B,EACnC,0CAAoC,GAClC,MAAM,qCAD4B,CAC3B;IACb,CAAC,EARgB,GAAG,KAAH,GAAG,QAQnB;IAED,GAAG,CAAC,SAAO,CAAA,GAAG,GAAG,CAAC;AAElB,gBAAe,GAAG;;;;;;;;;;;;;;;"}