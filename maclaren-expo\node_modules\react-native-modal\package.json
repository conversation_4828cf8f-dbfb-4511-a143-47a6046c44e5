{"name": "react-native-modal", "version": "13.0.1", "description": "An enhanced React Native modal", "main": "dist/index.js", "types": "dist/index.d.ts", "files": ["dist/"], "scripts": {"lint": "eslint \"src/**/*.{ts, tsx}\"", "test": "yarn run test:ts && yarn run lint", "release": "yarn semantic-release", "build": "tsc", "dev": "tsc --watch", "test:ts": "tsc --noEmit", "build:e2e:android:debug": "detox build -c android.emu.debug", "build:e2e:android:release": "detox build -c android.emu.release", "test:e2e:android:debug": "detox test -c android.emu.debug", "test:e2e:android:release": "detox test -c android.emu.release", "build:e2e:ios:debug": "detox build -c ios.sim.debug", "build:e2e:ios:release": "detox build -c ios.sim.release", "test:e2e:ios:debug": "detox test -c ios.sim.debug", "test:e2e:ios:release": "detox test -c ios.sim.release"}, "husky": {"hooks": {"pre-commit": "pretty-quick --staged"}}, "keywords": ["react-native", "react", "native", "modal", "android", "ios", "backdrop", "simple", "animated"], "author": "<PERSON><PERSON><PERSON><PERSON>", "license": "MIT", "homepage": "https://github.com/react-native-community/react-native-modal", "repository": {"type": "git", "url": "https://github.com/react-native-community/react-native-modal"}, "dependencies": {"prop-types": "^15.6.2", "react-native-animatable": "1.3.3"}, "devDependencies": {"@babel/core": "^7.5.5", "@babel/runtime": "^7.6.3", "@react-native-community/eslint-config": "^0.0.5", "@semantic-release/git": "^9.0.0", "@types/react": "^16.9.9", "@types/react-native": "^0.60.21", "@typescript-eslint/eslint-plugin": "^2.4.0", "@typescript-eslint/parser": "^2.4.0", "babel-core": "7.0.0-bridge.0", "babel-jest": "^24.9.0", "babel-plugin-module-resolver": "3.1.3", "detox": "14.7.0", "eslint": "^6.5.1", "eslint-plugin-prettier": "^3.1.1", "husky": "^3.0.9", "jest": "^24.9.0", "metro-react-native-babel-preset": "0.56.0", "postinstall": "^0.5.1", "prettier": "^1.18.2", "pretty-quick": "^2.0.0", "react": "16.9.0", "react-native": "0.61.0", "react-test-renderer": "16.8.6", "semantic-release": "17.3.0", "typescript": "3.7.2"}, "peerDependencies": {"react": "*", "react-native": ">=0.65.0"}, "jest": {"preset": "react-native"}, "detox": {"runner-config": "example/e2e/config.json", "configurations": {"ios.sim.debug": {"binaryPath": "example/ios/build/Build/Products/Debug-iphonesimulator/ModalExample.app", "build": "export RCT_NO_LAUNCH_PACKAGER=true && xcodebuild -workspace example/ios/ModalExample.xcworkspace -scheme ModalExample -configuration Debug -sdk iphonesimulator -derivedDataPath example/ios/build -UseModernBuildSystem=YES | xcpretty -k", "type": "ios.simulator", "device": {"type": "iPhone 8"}}, "ios.sim.release": {"binaryPath": "example/ios/build/Build/Products/Debug-iphonesimulator/ModalExample.app", "build": "export RCT_NO_LAUNCH_PACKAGER=true && xcodebuild -workspace example/ios/ModalExample.xcworkspace -scheme ModalExample -configuration Release -sdk iphonesimulator -derivedDataPath example/ios/build -UseModernBuildSystem=YES | xcpretty -k", "type": "ios.simulator", "device": {"type": "iPhone 8"}}, "android.emu.debug": {"binaryPath": "example/android/app/build/outputs/apk/debug/app-debug.apk", "build": "export RCT_NO_LAUNCH_PACKAGER=true && pushd example/android && ./gradlew assembleDebug assembleAndroidTest -DtestBuildType=debug && popd", "type": "android.emulator", "name": "Pixel_2_API_28"}, "android.emu.release": {"binaryPath": "example/android/app/build/outputs/apk/release/app-release.apk", "build": "export RCT_NO_LAUNCH_PACKAGER=true && pushd example/android && ./gradlew assembleRelease assembleAndroidTest -DtestBuildType=release && popd", "type": "android.emulator", "name": "Pixel_2_API_28"}}, "test-runner": "jest"}}