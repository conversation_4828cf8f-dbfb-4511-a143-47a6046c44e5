{"version": 3, "names": ["default", "createNativeStackNavigator", "NativeStackView", "useHeaderHeight", "HeaderHeightContext", "useAnimatedHeaderHeight", "AnimatedHeaderHeightContext"], "sources": ["index.tsx"], "sourcesContent": ["/**\n * Navigators\n */\nexport { default as createNativeStackNavigator } from './navigators/createNativeStackNavigator';\n\n/**\n * Views\n */\nexport { default as NativeStackView } from './views/NativeStackView';\n\n/**\n * Utilities\n */\nexport { default as useHeaderHeight } from './utils/useHeaderHeight';\nexport { default as HeaderHeightContext } from './utils/HeaderHeightContext';\n\nexport { default as useAnimatedHeaderHeight } from './utils/useAnimatedHeaderHeight';\nexport { default as AnimatedHeaderHeightContext } from './utils/AnimatedHeaderHeightContext';\n\n/**\n * Types\n */\nexport type {\n  NativeStackNavigationOptions,\n  NativeStackNavigationProp,\n  NativeStackScreenProps,\n} from './types';\n"], "mappings": "AAAA;AACA;AACA;AACA,SAASA,OAAO,IAAIC,0BAA0B,QAAQ,yCAAyC;;AAE/F;AACA;AACA;AACA,SAASD,OAAO,IAAIE,eAAe,QAAQ,yBAAyB;;AAEpE;AACA;AACA;AACA,SAASF,OAAO,IAAIG,eAAe,QAAQ,yBAAyB;AACpE,SAASH,OAAO,IAAII,mBAAmB,QAAQ,6BAA6B;AAE5E,SAASJ,OAAO,IAAIK,uBAAuB,QAAQ,iCAAiC;AACpF,SAASL,OAAO,IAAIM,2BAA2B,QAAQ,qCAAqC;;AAE5F;AACA;AACA"}