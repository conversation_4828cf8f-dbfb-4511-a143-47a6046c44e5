{"version": 3, "names": ["React", "Platform", "InnerScreen", "Animated", "useEvent", "useSharedValue", "ReanimatedTransitionProgressContext", "useSafeAreaFrame", "useSafeAreaInsets", "getDefaultHeaderHeight", "getStatusBarHeight", "ReanimatedHeaderHeightContext", "AnimatedScreen", "createAnimatedComponent", "ENABLE_FABRIC", "_global", "global", "_IS_FABRIC", "ReanimatedNativeStackScreen", "forwardRef", "props", "ref", "_rest$statusBarTransl", "children", "rest", "stackPresentation", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dimensions", "topInset", "top", "isStatusBarTranslucent", "statusBarTranslucent", "statusBarHeight", "defaultHeaderHeight", "cachedHeaderHeight", "useRef", "headerHeight", "progress", "closing", "goingForward", "createElement", "_extends", "onTransitionProgressReanimated", "event", "value", "OS", "onHeaderHeightChangeReanimated", "current", "Provider", "displayName"], "sources": ["ReanimatedNativeStackScreen.tsx"], "sourcesContent": ["import React from 'react';\nimport { Platform } from 'react-native';\nimport {\n  HeaderHeightChangeEventType,\n  InnerScreen,\n  ScreenProps,\n  TransitionProgressEventType,\n} from 'react-native-screens';\n\n// @ts-ignore file to be used only if `react-native-reanimated` available in the project\nimport Animated, { useEvent, useSharedValue } from 'react-native-reanimated';\nimport ReanimatedTransitionProgressContext from './ReanimatedTransitionProgressContext';\nimport {\n  useSafeAreaFrame,\n  useSafeAreaInsets,\n} from 'react-native-safe-area-context';\nimport getDefaultHeaderHeight from '../native-stack/utils/getDefaultHeaderHeight';\nimport getStatusBarHeight from '../native-stack/utils/getStatusBarHeight';\nimport ReanimatedHeaderHeightContext from './ReanimatedHeaderHeightContext';\n\nconst AnimatedScreen = Animated.createAnimatedComponent(\n  InnerScreen as unknown as React.ComponentClass\n);\n\n// We use prop added to global by reanimated since it seems safer than the one from RN. See:\n// https://github.com/software-mansion/react-native-reanimated/blob/3fe8b35b05e82b2f2aefda1fb97799cf81e4b7bb/src/reanimated2/UpdateProps.ts#L46\n// @ts-expect-error nativeFabricUIManager is not yet included in the RN types\nconst ENABLE_FABRIC = !!global?._IS_FABRIC;\n\nconst ReanimatedNativeStackScreen = React.forwardRef<\n  typeof AnimatedScreen,\n  ScreenProps\n>((props, ref) => {\n  const { children, ...rest } = props;\n  const { stackPresentation = 'push', hasLargeHeader } = rest;\n\n  const dimensions = useSafeAreaFrame();\n  const topInset = useSafeAreaInsets().top;\n  const isStatusBarTranslucent = rest.statusBarTranslucent ?? false;\n  const statusBarHeight = getStatusBarHeight(\n    topInset,\n    dimensions,\n    isStatusBarTranslucent\n  );\n\n  // Default header height, normally used in `useHeaderHeight` hook.\n  // Here, it is used for returning a default value for shared value.\n  const defaultHeaderHeight = getDefaultHeaderHeight(\n    dimensions,\n    statusBarHeight,\n    stackPresentation,\n    hasLargeHeader\n  );\n\n  const cachedHeaderHeight = React.useRef(defaultHeaderHeight);\n  const headerHeight = useSharedValue(defaultHeaderHeight);\n\n  const progress = useSharedValue(0);\n  const closing = useSharedValue(0);\n  const goingForward = useSharedValue(0);\n\n  return (\n    <AnimatedScreen\n      // @ts-ignore some problems with ref and onTransitionProgressReanimated being \"fake\" prop for parsing of `useEvent` return value\n      ref={ref}\n      onTransitionProgressReanimated={useEvent(\n        (event: TransitionProgressEventType) => {\n          'worklet';\n          progress.value = event.progress;\n          closing.value = event.closing;\n          goingForward.value = event.goingForward;\n        },\n        [\n          // This should not be necessary, but is not properly managed by `react-native-reanimated`\n          // @ts-ignore wrong type\n          Platform.OS === 'android'\n            ? 'onTransitionProgress'\n            : // for some reason there is a difference in required event name between architectures\n            ENABLE_FABRIC\n            ? 'onTransitionProgress'\n            : 'topTransitionProgress',\n        ]\n      )}\n      onHeaderHeightChangeReanimated={useEvent(\n        (event: HeaderHeightChangeEventType) => {\n          'worklet';\n          if (event.headerHeight !== cachedHeaderHeight.current) {\n            headerHeight.value = event.headerHeight;\n            cachedHeaderHeight.current = event.headerHeight;\n          }\n        },\n        [\n          // @ts-ignore wrong type\n          Platform.OS === 'android'\n            ? 'onHeaderHeightChange'\n            : ENABLE_FABRIC\n            ? 'onHeaderHeightChange'\n            : 'topHeaderHeightChange',\n        ]\n      )}\n      {...rest}>\n      <ReanimatedHeaderHeightContext.Provider value={headerHeight}>\n        <ReanimatedTransitionProgressContext.Provider\n          value={{\n            progress,\n            closing,\n            goingForward,\n          }}>\n          {children}\n        </ReanimatedTransitionProgressContext.Provider>\n      </ReanimatedHeaderHeightContext.Provider>\n    </AnimatedScreen>\n  );\n});\n\nReanimatedNativeStackScreen.displayName = 'ReanimatedNativeStackScreen';\n\nexport default ReanimatedNativeStackScreen;\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,QAAQ,QAAQ,cAAc;AACvC,SAEEC,WAAW,QAGN,sBAAsB;;AAE7B;AACA,OAAOC,QAAQ,IAAIC,QAAQ,EAAEC,cAAc,QAAQ,yBAAyB;AAC5E,OAAOC,mCAAmC,MAAM,uCAAuC;AACvF,SACEC,gBAAgB,EAChBC,iBAAiB,QACZ,gCAAgC;AACvC,OAAOC,sBAAsB,MAAM,8CAA8C;AACjF,OAAOC,kBAAkB,MAAM,0CAA0C;AACzE,OAAOC,6BAA6B,MAAM,iCAAiC;AAE3E,MAAMC,cAAc,GAAGT,QAAQ,CAACU,uBAAuB,CACrDX,WACF,CAAC;;AAED;AACA;AACA;AACA,MAAMY,aAAa,GAAG,CAAC,GAAAC,OAAA,GAACC,MAAM,cAAAD,OAAA,eAANA,OAAA,CAAQE,UAAU;AAE1C,MAAMC,2BAA2B,gBAAGlB,KAAK,CAACmB,UAAU,CAGlD,CAACC,KAAK,EAAEC,GAAG,KAAK;EAAA,IAAAC,qBAAA;EAChB,MAAM;IAAEC,QAAQ;IAAE,GAAGC;EAAK,CAAC,GAAGJ,KAAK;EACnC,MAAM;IAAEK,iBAAiB,GAAG,MAAM;IAAEC;EAAe,CAAC,GAAGF,IAAI;EAE3D,MAAMG,UAAU,GAAGpB,gBAAgB,CAAC,CAAC;EACrC,MAAMqB,QAAQ,GAAGpB,iBAAiB,CAAC,CAAC,CAACqB,GAAG;EACxC,MAAMC,sBAAsB,IAAAR,qBAAA,GAAGE,IAAI,CAACO,oBAAoB,cAAAT,qBAAA,cAAAA,qBAAA,GAAI,KAAK;EACjE,MAAMU,eAAe,GAAGtB,kBAAkB,CACxCkB,QAAQ,EACRD,UAAU,EACVG,sBACF,CAAC;;EAED;EACA;EACA,MAAMG,mBAAmB,GAAGxB,sBAAsB,CAChDkB,UAAU,EACVK,eAAe,EACfP,iBAAiB,EACjBC,cACF,CAAC;EAED,MAAMQ,kBAAkB,GAAGlC,KAAK,CAACmC,MAAM,CAACF,mBAAmB,CAAC;EAC5D,MAAMG,YAAY,GAAG/B,cAAc,CAAC4B,mBAAmB,CAAC;EAExD,MAAMI,QAAQ,GAAGhC,cAAc,CAAC,CAAC,CAAC;EAClC,MAAMiC,OAAO,GAAGjC,cAAc,CAAC,CAAC,CAAC;EACjC,MAAMkC,YAAY,GAAGlC,cAAc,CAAC,CAAC,CAAC;EAEtC,oBACEL,KAAA,CAAAwC,aAAA,CAAC5B;EACC;EAAA,EAAA6B,QAAA;IACApB,GAAG,EAAEA,GAAI;IACTqB,8BAA8B,EAAEtC,QAAQ,CACrCuC,KAAkC,IAAK;MACtC,SAAS;;MACTN,QAAQ,CAACO,KAAK,GAAGD,KAAK,CAACN,QAAQ;MAC/BC,OAAO,CAACM,KAAK,GAAGD,KAAK,CAACL,OAAO;MAC7BC,YAAY,CAACK,KAAK,GAAGD,KAAK,CAACJ,YAAY;IACzC,CAAC,EACD;IACE;IACA;IACAtC,QAAQ,CAAC4C,EAAE,KAAK,SAAS,GACrB,sBAAsB;IACtB;IACF/B,aAAa,GACX,sBAAsB,GACtB,uBAAuB,CAE/B,CAAE;IACFgC,8BAA8B,EAAE1C,QAAQ,CACrCuC,KAAkC,IAAK;MACtC,SAAS;;MACT,IAAIA,KAAK,CAACP,YAAY,KAAKF,kBAAkB,CAACa,OAAO,EAAE;QACrDX,YAAY,CAACQ,KAAK,GAAGD,KAAK,CAACP,YAAY;QACvCF,kBAAkB,CAACa,OAAO,GAAGJ,KAAK,CAACP,YAAY;MACjD;IACF,CAAC,EACD;IACE;IACAnC,QAAQ,CAAC4C,EAAE,KAAK,SAAS,GACrB,sBAAsB,GACtB/B,aAAa,GACb,sBAAsB,GACtB,uBAAuB,CAE/B;EAAE,GACEU,IAAI,gBACRxB,KAAA,CAAAwC,aAAA,CAAC7B,6BAA6B,CAACqC,QAAQ;IAACJ,KAAK,EAAER;EAAa,gBAC1DpC,KAAA,CAAAwC,aAAA,CAAClC,mCAAmC,CAAC0C,QAAQ;IAC3CJ,KAAK,EAAE;MACLP,QAAQ;MACRC,OAAO;MACPC;IACF;EAAE,GACDhB,QAC2C,CACR,CAC1B,CAAC;AAErB,CAAC,CAAC;AAEFL,2BAA2B,CAAC+B,WAAW,GAAG,6BAA6B;AAEvE,eAAe/B,2BAA2B"}