/**
 * Pantalla de Perfil del Conductor - MACLAREN
 * Configuración y datos del conductor
 */

import React, {useState, useEffect} from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  ScrollView,
  StatusBar,
  Alert,
  Switch,
} from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import * as Animatable from 'react-native-animatable';
import AsyncStorage from '@react-native-async-storage/async-storage';

interface ProfileScreenProps {
  onLogout: () => void;
}

interface DriverData {
  name: string;
  email: string;
  phone: string;
  rating: number;
  totalTrips: number;
  totalEarnings: number;
  memberSince: string;
  vehicle: {
    model: string;
    licensePlate: string;
    licenseNumber: string;
  };
}

const DriverProfileScreen: React.FC<ProfileScreenProps> = ({onLogout}) => {
  const [driverData, setDriverData] = useState<DriverData>({
    name: 'Conductor',
    email: '<EMAIL>',
    phone: '+57 ************',
    rating: 4.8,
    totalTrips: 156,
    totalEarnings: 2450000,
    memberSince: '2024',
    vehicle: {
      model: 'Yamaha FZ 150',
      licensePlate: 'ABC123',
      licenseNumber: '12345678',
    },
  });
  
  const [notifications, setNotifications] = useState(true);
  const [autoAccept, setAutoAccept] = useState(false);
  const [soundAlerts, setSoundAlerts] = useState(true);

  useEffect(() => {
    loadDriverData();
  }, []);

  const loadDriverData = async () => {
    try {
      const storedUserData = await AsyncStorage.getItem('userData');
      if (storedUserData) {
        const parsedData = JSON.parse(storedUserData);
        setDriverData({
          name: parsedData.name || 'Conductor',
          email: parsedData.email || '<EMAIL>',
          phone: parsedData.phone || '+57 ************',
          rating: 4.8,
          totalTrips: 156,
          totalEarnings: 2450000,
          memberSince: '2024',
          vehicle: parsedData.vehicle || {
            model: 'Yamaha FZ 150',
            licensePlate: 'ABC123',
            licenseNumber: '12345678',
          },
        });
      }
    } catch (error) {
      console.error('Error cargando datos del conductor:', error);
    }
  };

  const handleLogout = () => {
    Alert.alert(
      'Cerrar Sesión',
      '¿Estás seguro que deseas cerrar sesión?',
      [
        {text: 'Cancelar', style: 'cancel'},
        {
          text: 'Cerrar Sesión',
          style: 'destructive',
          onPress: onLogout,
        },
      ]
    );
  };

  const handleEditProfile = () => {
    Alert.alert('Próximamente', 'La edición de perfil estará disponible pronto');
  };

  const handleVehicleInfo = () => {
    Alert.alert(
      'Información del Vehículo',
      `Modelo: ${driverData.vehicle.model}\nPlaca: ${driverData.vehicle.licensePlate}\nLicencia: ${driverData.vehicle.licenseNumber}`
    );
  };

  const handleDocuments = () => {
    Alert.alert('Próximamente', 'La gestión de documentos estará disponible pronto');
  };

  const handleSupport = () => {
    Alert.alert('Soporte', 'Contacta con nosotros:\n\n📧 <EMAIL>\n📱 +57 300 MACLAREN');
  };

  return (
    <View style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor="#ff6b35" />
      
      {/* Header con información del conductor */}
      <LinearGradient
        colors={['#ff6b35', '#f7931e']}
        style={styles.header}>
        
        <Animatable.View 
          animation="fadeInDown"
          duration={800}
          style={styles.profileInfo}>
          
          <View style={styles.avatar}>
            <Text style={styles.avatarText}>
              {driverData.name.charAt(0).toUpperCase()}
            </Text>
          </View>
          
          <Text style={styles.userName}>{driverData.name}</Text>
          <Text style={styles.userEmail}>{driverData.email}</Text>
          
          <View style={styles.userStats}>
            <View style={styles.statItem}>
              <Text style={styles.statValue}>⭐ {driverData.rating}</Text>
              <Text style={styles.statLabel}>Rating</Text>
            </View>
            <View style={styles.statDivider} />
            <View style={styles.statItem}>
              <Text style={styles.statValue}>{driverData.totalTrips}</Text>
              <Text style={styles.statLabel}>Viajes</Text>
            </View>
            <View style={styles.statDivider} />
            <View style={styles.statItem}>
              <Text style={styles.statValue}>
                ${Math.round(driverData.totalEarnings / 1000)}K
              </Text>
              <Text style={styles.statLabel}>Ganado</Text>
            </View>
          </View>
        </Animatable.View>
      </LinearGradient>

      {/* Contenido principal */}
      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        
        {/* Información del vehículo */}
        <Animatable.View 
          animation="fadeInUp"
          duration={800}
          delay={200}
          style={styles.vehicleSection}>
          
          <Text style={styles.sectionTitle}>🏍️ Mi Vehículo</Text>
          
          <TouchableOpacity 
            style={styles.vehicleCard}
            onPress={handleVehicleInfo}>
            <View style={styles.vehicleInfo}>
              <Text style={styles.vehicleModel}>{driverData.vehicle.model}</Text>
              <Text style={styles.vehiclePlate}>Placa: {driverData.vehicle.licensePlate}</Text>
            </View>
            <Text style={styles.menuArrow}>›</Text>
          </TouchableOpacity>
        </Animatable.View>

        {/* Sección de cuenta */}
        <Animatable.View 
          animation="fadeInUp"
          duration={800}
          delay={400}
          style={styles.section}>
          
          <Text style={styles.sectionTitle}>Mi Cuenta</Text>
          
          <TouchableOpacity 
            style={styles.menuItem}
            onPress={handleEditProfile}>
            <Text style={styles.menuIcon}>👤</Text>
            <Text style={styles.menuText}>Editar Perfil</Text>
            <Text style={styles.menuArrow}>›</Text>
          </TouchableOpacity>
          
          <TouchableOpacity 
            style={styles.menuItem}
            onPress={handleDocuments}>
            <Text style={styles.menuIcon}>📄</Text>
            <Text style={styles.menuText}>Documentos</Text>
            <Text style={styles.menuArrow}>›</Text>
          </TouchableOpacity>
          
          <TouchableOpacity style={styles.menuItem}>
            <Text style={styles.menuIcon}>💳</Text>
            <Text style={styles.menuText}>Información de Pago</Text>
            <Text style={styles.menuArrow}>›</Text>
          </TouchableOpacity>
        </Animatable.View>

        {/* Configuración de conductor */}
        <Animatable.View 
          animation="fadeInUp"
          duration={800}
          delay={600}
          style={styles.section}>
          
          <Text style={styles.sectionTitle}>Configuración de Conductor</Text>
          
          <View style={styles.menuItem}>
            <Text style={styles.menuIcon}>🔔</Text>
            <Text style={styles.menuText}>Notificaciones</Text>
            <Switch
              value={notifications}
              onValueChange={setNotifications}
              trackColor={{false: '#ccc', true: '#ff6b35'}}
              thumbColor={notifications ? '#fff' : '#f4f3f4'}
            />
          </View>
          
          <View style={styles.menuItem}>
            <Text style={styles.menuIcon}>⚡</Text>
            <Text style={styles.menuText}>Auto-aceptar Viajes</Text>
            <Switch
              value={autoAccept}
              onValueChange={setAutoAccept}
              trackColor={{false: '#ccc', true: '#ff6b35'}}
              thumbColor={autoAccept ? '#fff' : '#f4f3f4'}
            />
          </View>
          
          <View style={styles.menuItem}>
            <Text style={styles.menuIcon}>🔊</Text>
            <Text style={styles.menuText}>Alertas de Sonido</Text>
            <Switch
              value={soundAlerts}
              onValueChange={setSoundAlerts}
              trackColor={{false: '#ccc', true: '#ff6b35'}}
              thumbColor={soundAlerts ? '#fff' : '#f4f3f4'}
            />
          </View>
        </Animatable.View>

        {/* Sección de ayuda */}
        <Animatable.View 
          animation="fadeInUp"
          duration={800}
          delay={800}
          style={styles.section}>
          
          <Text style={styles.sectionTitle}>Ayuda y Soporte</Text>
          
          <TouchableOpacity 
            style={styles.menuItem}
            onPress={handleSupport}>
            <Text style={styles.menuIcon}>💬</Text>
            <Text style={styles.menuText}>Contactar Soporte</Text>
            <Text style={styles.menuArrow}>›</Text>
          </TouchableOpacity>
          
          <TouchableOpacity style={styles.menuItem}>
            <Text style={styles.menuIcon}>📊</Text>
            <Text style={styles.menuText}>Centro de Conductores</Text>
            <Text style={styles.menuArrow}>›</Text>
          </TouchableOpacity>
          
          <TouchableOpacity style={styles.menuItem}>
            <Text style={styles.menuIcon}>📋</Text>
            <Text style={styles.menuText}>Términos de Servicio</Text>
            <Text style={styles.menuArrow}>›</Text>
          </TouchableOpacity>
        </Animatable.View>

        {/* Botón de cerrar sesión */}
        <Animatable.View 
          animation="fadeInUp"
          duration={800}
          delay={1000}
          style={styles.logoutSection}>
          
          <TouchableOpacity 
            style={styles.logoutButton}
            onPress={handleLogout}>
            <Text style={styles.logoutText}>🚪 Cerrar Sesión</Text>
          </TouchableOpacity>
        </Animatable.View>

        {/* Información de la app */}
        <View style={styles.appInfo}>
          <Text style={styles.appVersion}>MACLAREN Conductor v1.0.0</Text>
          <Text style={styles.appCopyright}>© 2024 MACLAREN. Todos los derechos reservados.</Text>
        </View>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  header: {
    paddingTop: 50,
    paddingBottom: 30,
    paddingHorizontal: 20,
    alignItems: 'center',
  },
  profileInfo: {
    alignItems: 'center',
  },
  avatar: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: 'rgba(255, 255, 255, 0.3)',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 15,
  },
  avatarText: {
    fontSize: 32,
    fontWeight: 'bold',
    color: 'white',
  },
  userName: {
    fontSize: 24,
    fontWeight: 'bold',
    color: 'white',
    marginBottom: 5,
  },
  userEmail: {
    fontSize: 16,
    color: 'rgba(255, 255, 255, 0.8)',
    marginBottom: 20,
  },
  userStats: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    borderRadius: 15,
    paddingVertical: 15,
    paddingHorizontal: 20,
  },
  statItem: {
    alignItems: 'center',
    flex: 1,
  },
  statValue: {
    fontSize: 18,
    fontWeight: 'bold',
    color: 'white',
    marginBottom: 2,
  },
  statLabel: {
    fontSize: 12,
    color: 'rgba(255, 255, 255, 0.8)',
  },
  statDivider: {
    width: 1,
    height: 30,
    backgroundColor: 'rgba(255, 255, 255, 0.3)',
    marginHorizontal: 15,
  },
  content: {
    flex: 1,
    paddingHorizontal: 20,
  },
  vehicleSection: {
    marginTop: 20,
  },
  vehicleCard: {
    backgroundColor: 'white',
    borderRadius: 15,
    padding: 20,
    flexDirection: 'row',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  vehicleInfo: {
    flex: 1,
  },
  vehicleModel: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 5,
  },
  vehiclePlate: {
    fontSize: 14,
    color: '#666',
  },
  section: {
    backgroundColor: 'white',
    borderRadius: 15,
    marginTop: 20,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    padding: 20,
    paddingBottom: 10,
  },
  menuItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 15,
    borderBottomWidth: 1,
    borderBottomColor: '#f8f9fa',
  },
  menuIcon: {
    fontSize: 20,
    marginRight: 15,
    width: 25,
  },
  menuText: {
    fontSize: 16,
    color: '#333',
    flex: 1,
  },
  menuArrow: {
    fontSize: 20,
    color: '#ccc',
  },
  logoutSection: {
    marginTop: 30,
    marginBottom: 20,
  },
  logoutButton: {
    backgroundColor: '#e74c3c',
    borderRadius: 15,
    paddingVertical: 15,
    alignItems: 'center',
    shadowColor: '#e74c3c',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.3,
    shadowRadius: 4,
    elevation: 3,
  },
  logoutText: {
    color: 'white',
    fontSize: 18,
    fontWeight: 'bold',
  },
  appInfo: {
    alignItems: 'center',
    paddingVertical: 20,
  },
  appVersion: {
    fontSize: 14,
    color: '#666',
    marginBottom: 5,
  },
  appCopyright: {
    fontSize: 12,
    color: '#999',
    textAlign: 'center',
  },
});

export default DriverProfileScreen;
