/**
 * Servicio de Notificaciones para MACLAREN - Expo
 * Maneja notificaciones push y locales usando Expo Notifications
 */

import * as Notifications from 'expo-notifications';
import * as Device from 'expo-device';
import Constants from 'expo-constants';
import { Platform, Alert } from 'react-native';

class NotificationServiceClass {
  constructor() {
    this.expoPushToken = null;
    this.notificationListener = null;
    this.responseListener = null;
  }

  async initialize() {
    try {
      // Configurar el comportamiento de las notificaciones
      Notifications.setNotificationHandler({
        handleNotification: async () => ({
          shouldShowAlert: true,
          shouldPlaySound: true,
          shouldSetBadge: false,
        }),
      });

      // Registrar para notificaciones push
      await this.registerForPushNotifications();

      // Configurar listeners
      this.setupNotificationListeners();

      console.log('✅ Servicio de notificaciones inicializado');
      return true;
    } catch (error) {
      console.error('❌ Error inicializando servicio de notificaciones:', error);
      return false;
    }
  }

  async registerForPushNotifications() {
    let token;

    if (Platform.OS === 'android') {
      await Notifications.setNotificationChannelAsync('default', {
        name: 'default',
        importance: Notifications.AndroidImportance.MAX,
        vibrationPattern: [0, 250, 250, 250],
        lightColor: '#FF6B35',
      });
    }

    if (Device.isDevice) {
      const { status: existingStatus } = await Notifications.getPermissionsAsync();
      let finalStatus = existingStatus;
      
      if (existingStatus !== 'granted') {
        const { status } = await Notifications.requestPermissionsAsync();
        finalStatus = status;
      }
      
      if (finalStatus !== 'granted') {
        Alert.alert(
          'Notificaciones',
          'Las notificaciones te ayudarán a recibir actualizaciones importantes sobre tus viajes.'
        );
        return;
      }
      
      token = (await Notifications.getExpoPushTokenAsync({
        projectId: Constants.expoConfig?.extra?.eas?.projectId,
      })).data;
      
      console.log('📱 Token de notificaciones push:', token);
      this.expoPushToken = token;
    } else {
      console.log('⚠️ Debe usar un dispositivo físico para notificaciones push');
    }

    return token;
  }

  setupNotificationListeners() {
    // Listener para notificaciones recibidas mientras la app está en primer plano
    this.notificationListener = Notifications.addNotificationReceivedListener(notification => {
      console.log('📨 Notificación recibida:', notification);
      this.handleNotificationReceived(notification);
    });

    // Listener para cuando el usuario toca una notificación
    this.responseListener = Notifications.addNotificationResponseReceivedListener(response => {
      console.log('👆 Notificación tocada:', response);
      this.handleNotificationResponse(response);
    });
  }

  handleNotificationReceived(notification) {
    // Aquí puedes manejar la notificación recibida
    const { title, body, data } = notification.request.content;
    
    // Ejemplo: mostrar una alerta o actualizar el estado de la app
    if (data?.type === 'new_trip_request') {
      // Manejar nueva solicitud de viaje
      console.log('🚗 Nueva solicitud de viaje recibida');
    } else if (data?.type === 'trip_accepted') {
      // Manejar viaje aceptado
      console.log('✅ Viaje aceptado');
    }
  }

  handleNotificationResponse(response) {
    // Aquí puedes manejar cuando el usuario toca la notificación
    const { data } = response.notification.request.content;
    
    // Ejemplo: navegar a una pantalla específica
    if (data?.screen) {
      console.log(`🧭 Navegar a: ${data.screen}`);
      // Aquí implementarías la navegación
    }
  }

  // Enviar notificación local
  async showLocalNotification(title, body, data = {}) {
    try {
      await Notifications.scheduleNotificationAsync({
        content: {
          title,
          body,
          data,
          sound: 'default',
        },
        trigger: null, // Mostrar inmediatamente
      });
      
      console.log('📱 Notificación local enviada:', title);
    } catch (error) {
      console.error('❌ Error enviando notificación local:', error);
    }
  }

  // Programar notificación para más tarde
  async scheduleNotification(title, body, triggerDate, data = {}) {
    try {
      const identifier = await Notifications.scheduleNotificationAsync({
        content: {
          title,
          body,
          data,
          sound: 'default',
        },
        trigger: triggerDate,
      });
      
      console.log('⏰ Notificación programada:', identifier);
      return identifier;
    } catch (error) {
      console.error('❌ Error programando notificación:', error);
      return null;
    }
  }

  // Cancelar notificación programada
  async cancelNotification(identifier) {
    try {
      await Notifications.cancelScheduledNotificationAsync(identifier);
      console.log('❌ Notificación cancelada:', identifier);
    } catch (error) {
      console.error('❌ Error cancelando notificación:', error);
    }
  }

  // Cancelar todas las notificaciones programadas
  async cancelAllNotifications() {
    try {
      await Notifications.cancelAllScheduledNotificationsAsync();
      console.log('🧹 Todas las notificaciones canceladas');
    } catch (error) {
      console.error('❌ Error cancelando notificaciones:', error);
    }
  }

  // Obtener notificaciones programadas
  async getScheduledNotifications() {
    try {
      const notifications = await Notifications.getAllScheduledNotificationsAsync();
      console.log('📋 Notificaciones programadas:', notifications.length);
      return notifications;
    } catch (error) {
      console.error('❌ Error obteniendo notificaciones programadas:', error);
      return [];
    }
  }

  // Limpiar badge de la app (iOS)
  async clearBadge() {
    try {
      await Notifications.setBadgeCountAsync(0);
      console.log('🔢 Badge limpiado');
    } catch (error) {
      console.error('❌ Error limpiando badge:', error);
    }
  }

  // Establecer badge de la app (iOS)
  async setBadge(count) {
    try {
      await Notifications.setBadgeCountAsync(count);
      console.log(`🔢 Badge establecido a: ${count}`);
    } catch (error) {
      console.error('❌ Error estableciendo badge:', error);
    }
  }

  // Notificaciones específicas de MACLAREN
  async notifyNewTripRequest(passengerName, pickup, destination) {
    await this.showLocalNotification(
      '🚗 Nueva Solicitud de Viaje',
      `${passengerName} necesita un viaje de ${pickup} a ${destination}`,
      {
        type: 'new_trip_request',
        screen: 'DriverHome',
      }
    );
  }

  async notifyTripAccepted(driverName) {
    await this.showLocalNotification(
      '✅ Viaje Aceptado',
      `${driverName} aceptó tu solicitud y va en camino`,
      {
        type: 'trip_accepted',
        screen: 'PassengerHome',
      }
    );
  }

  async notifyDriverArrived(driverName) {
    await this.showLocalNotification(
      '📍 Conductor Llegó',
      `${driverName} ha llegado a tu ubicación`,
      {
        type: 'driver_arrived',
        screen: 'TripMap',
      }
    );
  }

  async notifyTripCompleted(fare) {
    await this.showLocalNotification(
      '🎉 Viaje Completado',
      `Viaje finalizado. Total: $${fare.toLocaleString()}`,
      {
        type: 'trip_completed',
        screen: 'TripHistory',
      }
    );
  }

  async notifyNewMessage(senderName, message) {
    await this.showLocalNotification(
      `💬 Mensaje de ${senderName}`,
      message,
      {
        type: 'new_message',
        screen: 'Chat',
      }
    );
  }

  // Getters
  get pushToken() {
    return this.expoPushToken;
  }

  // Limpiar recursos
  cleanup() {
    if (this.notificationListener) {
      Notifications.removeNotificationSubscription(this.notificationListener);
      this.notificationListener = null;
    }
    
    if (this.responseListener) {
      Notifications.removeNotificationSubscription(this.responseListener);
      this.responseListener = null;
    }
    
    console.log('🧹 Servicio de notificaciones limpiado');
  }
}

export const NotificationService = new NotificationServiceClass();
