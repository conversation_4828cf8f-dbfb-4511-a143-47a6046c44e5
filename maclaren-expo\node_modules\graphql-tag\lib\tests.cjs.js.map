{"version": 3, "file": "tests.cjs.js", "sources": ["../src/index.ts", "../src/tests.ts"], "sourcesContent": ["import { parse } from 'graphql';\n\nimport {\n  DocumentNode,\n  DefinitionNode,\n  Location,\n} from 'graphql/language/ast';\n\n// A map docString -> graphql document\nconst docCache = new Map<string, DocumentNode>();\n\n// A map fragmentName -> [normalized source]\nconst fragmentSourceMap = new Map<string, Set<string>>();\n\nlet printFragmentWarnings = true;\nlet experimentalFragmentVariables = false;\n\n// Strip insignificant whitespace\n// Note that this could do a lot more, such as reorder fields etc.\nfunction normalize(string: string) {\n  return string.replace(/[\\s,]+/g, ' ').trim();\n}\n\nfunction cacheKeyFromLoc(loc: Location) {\n  return normalize(loc.source.body.substring(loc.start, loc.end));\n}\n\n// Take a unstripped parsed document (query/mutation or even fragment), and\n// check all fragment definitions, checking for name->source uniqueness.\n// We also want to make sure only unique fragments exist in the document.\nfunction processFragments(ast: DocumentNode) {\n  const seenKeys = new Set<string>();\n  const definitions: DefinitionNode[] = [];\n\n  ast.definitions.forEach(fragmentDefinition => {\n    if (fragmentDefinition.kind === 'FragmentDefinition') {\n      var fragmentName = fragmentDefinition.name.value;\n      var sourceKey = cacheKeyFromLoc(fragmentDefinition.loc!);\n\n      // We know something about this fragment\n      let sourceKeySet = fragmentSourceMap.get(fragmentName)!;\n      if (sourceKeySet && !sourceKeySet.has(sourceKey)) {\n        // this is a problem because the app developer is trying to register another fragment with\n        // the same name as one previously registered. So, we tell them about it.\n        if (printFragmentWarnings) {\n          console.warn(\"Warning: fragment with name \" + fragmentName + \" already exists.\\n\"\n            + \"graphql-tag enforces all fragment names across your application to be unique; read more about\\n\"\n            + \"this in the docs: http://dev.apollodata.com/core/fragments.html#unique-names\");\n        }\n      } else if (!sourceKeySet) {\n        fragmentSourceMap.set(fragmentName, sourceKeySet = new Set);\n      }\n\n      sourceKeySet.add(sourceKey);\n\n      if (!seenKeys.has(sourceKey)) {\n        seenKeys.add(sourceKey);\n        definitions.push(fragmentDefinition);\n      }\n    } else {\n      definitions.push(fragmentDefinition);\n    }\n  });\n\n  return {\n    ...ast,\n    definitions,\n  };\n}\n\nfunction stripLoc(doc: DocumentNode) {\n  const workSet = new Set<Record<string, any>>(doc.definitions);\n\n  workSet.forEach(node => {\n    if (node.loc) delete node.loc;\n    Object.keys(node).forEach(key => {\n      const value = node[key];\n      if (value && typeof value === 'object') {\n        workSet.add(value);\n      }\n    });\n  });\n\n  const loc = doc.loc as Record<string, any>;\n  if (loc) {\n    delete loc.startToken;\n    delete loc.endToken;\n  }\n\n  return doc;\n}\n\nfunction parseDocument(source: string) {\n  var cacheKey = normalize(source);\n  if (!docCache.has(cacheKey)) {\n    const parsed = parse(source, {\n      experimentalFragmentVariables,\n      allowLegacyFragmentVariables: experimentalFragmentVariables,\n    } as any);\n    if (!parsed || parsed.kind !== 'Document') {\n      throw new Error('Not a valid GraphQL document.');\n    }\n    docCache.set(\n      cacheKey,\n      // check that all \"new\" fragments inside the documents are consistent with\n      // existing fragments of the same name\n      stripLoc(processFragments(parsed)),\n    );\n  }\n  return docCache.get(cacheKey)!;\n}\n\n// XXX This should eventually disallow arbitrary string interpolation, like Relay does\nexport function gql(\n  literals: string | readonly string[],\n  ...args: any[]\n) {\n\n  if (typeof literals === 'string') {\n    literals = [literals];\n  }\n\n  let result = literals[0];\n\n  args.forEach((arg, i) => {\n    if (arg && arg.kind === 'Document') {\n      result += arg.loc.source.body;\n    } else {\n      result += arg;\n    }\n    result += literals[i + 1];\n  });\n\n  return parseDocument(result);\n}\n\nexport function resetCaches() {\n  docCache.clear();\n  fragmentSourceMap.clear();\n}\n\nexport function disableFragmentWarnings() {\n  printFragmentWarnings = false;\n}\n\nexport function enableExperimentalFragmentVariables() {\n  experimentalFragmentVariables = true;\n}\n\nexport function disableExperimentalFragmentVariables() {\n  experimentalFragmentVariables = false;\n}\n\nconst extras = {\n  gql,\n  resetCaches,\n  disableFragmentWarnings,\n  enableExperimentalFragmentVariables,\n  disableExperimentalFragmentVariables,\n};\n\nexport namespace gql {\n  export const {\n    gql,\n    resetCaches,\n    disableFragmentWarnings,\n    enableExperimentalFragmentVariables,\n    disableExperimentalFragmentVariables,\n  } = extras;\n}\n\ngql.default = gql;\n\nexport default gql;\n", "import 'source-map-support/register';\n\nimport { assert } from 'chai';\nimport { DocumentNode, FragmentDefinitionNode } from 'graphql';\n\nimport gql from './index';\nconst loader = require('../loader');\n\ndescribe('gql', () => {\n  it('parses queries', () => {\n    assert.equal(gql`{ testQuery }`.kind, 'Document');\n  });\n\n  it('parses queries when called as a function', () => {\n    assert.equal(gql('{ testQuery }').kind, 'Document');\n  });\n\n  it('parses queries with weird substitutions', () => {\n    const obj = Object.create(null);\n    assert.equal(gql`{ field(input: \"${obj.missing}\") }`.kind, 'Document');\n    assert.equal(gql`{ field(input: \"${null}\") }`.kind, 'Document');\n    assert.equal(gql`{ field(input: \"${0}\") }`.kind, 'Document');\n  });\n\n  it('allows interpolation of documents generated by the webpack loader', () => {\n    const sameFragment = \"fragment SomeFragmentName on SomeType { someField }\";\n\n    const jsSource = loader.call(\n      { cacheable() {} },\n      sameFragment,\n    );\n    const module = { exports: Object.create(null) };\n\n    Function(\"module\", jsSource)(module);\n\n    const document = gql`query { ...SomeFragmentName } ${module.exports}`;\n    assert.equal(document.kind, 'Document');\n    assert.equal(document.definitions.length, 2);\n    assert.equal(document.definitions[0].kind, 'OperationDefinition');\n    assert.equal(document.definitions[1].kind, 'FragmentDefinition');\n  });\n\n  it('parses queries through webpack loader', () => {\n    const jsSource = loader.call({ cacheable() {} }, '{ testQuery }');\n    const module = { exports: Object.create(null) };\n    Function(\"module\", jsSource)(module);\n    assert.equal(module.exports.kind, 'Document');\n  });\n\n  it('parses single query through webpack loader', () => {\n    const jsSource = loader.call({ cacheable() {} }, `\n      query Q1 { testQuery }\n    `);\n    const module = { exports: Object.create(null) };\n    Function(\"module\", jsSource)(module);\n\n    assert.equal(module.exports.kind, 'Document');\n    assert.exists(module.exports.Q1);\n    assert.equal(module.exports.Q1.kind, 'Document');\n    assert.equal(module.exports.Q1.definitions.length, 1);\n  });\n\n  it('parses single query and exports as default', () => {\n    const jsSource = loader.call({ cacheable() {} }, `\n      query Q1 { testQuery }\n    `);\n    const module = { exports: Object.create(null) };\n    Function(\"module\", jsSource)(module);\n    assert.deepEqual(module.exports.definitions, module.exports.Q1.definitions);\n  });\n\n  it('parses multiple queries through webpack loader', () => {\n    const jsSource = loader.call({ cacheable() {} }, `\n      query Q1 { testQuery }\n      query Q2 { testQuery2 }\n    `);\n    const module = { exports: Object.create(null) };\n    Function(\"module\", jsSource)(module);\n\n    assert.exists(module.exports.Q1);\n    assert.exists(module.exports.Q2);\n    assert.equal(module.exports.Q1.kind, 'Document');\n    assert.equal(module.exports.Q2.kind, 'Document');\n    assert.equal(module.exports.Q1.definitions.length, 1);\n    assert.equal(module.exports.Q2.definitions.length, 1);\n  });\n\n  it('parses fragments with variable definitions', () => {\n    gql.enableExperimentalFragmentVariables();\n\n    const parsed: any = gql`fragment A ($arg: String!) on Type { testQuery }`;\n    assert.equal(parsed.kind, 'Document');\n    assert.exists(parsed.definitions[0].variableDefinitions);\n\n    gql.disableExperimentalFragmentVariables()\n  });\n\n  // see https://github.com/apollographql/graphql-tag/issues/168\n  it('does not nest queries needlessly in named exports', () => {\n    const jsSource = loader.call({ cacheable() {} }, `\n      query Q1 { testQuery }\n      query Q2 { testQuery2 }\n      query Q3 { test Query3 }\n    `);\n    const module = { exports: Object.create(null) };\n    Function(\"module\", jsSource)(module);\n\n    assert.notExists(module.exports.Q2.Q1);\n    assert.notExists(module.exports.Q3.Q1);\n    assert.notExists(module.exports.Q3.Q2);\n  });\n\n  it('tracks fragment dependencies from multiple queries through webpack loader', () => {\n    const jsSource = loader.call({ cacheable() {} }, `\n      fragment F1 on F { testQuery }\n      fragment F2 on F { testQuery2 }\n      fragment F3 on F { testQuery3 }\n      query Q1 { ...F1 }\n      query Q2 { ...F2 }\n      query Q3 {\n        ...F1\n        ...F2\n      }\n    `);\n    const module = { exports: Object.create(null) };\n    Function(\"module\", jsSource)(module);\n\n    assert.exists(module.exports.Q1);\n    assert.exists(module.exports.Q2);\n    assert.exists(module.exports.Q3);\n    const Q1 = module.exports.Q1.definitions;\n    const Q2 = module.exports.Q2.definitions;\n    const Q3 = module.exports.Q3.definitions;\n\n    assert.equal(Q1.length, 2);\n    assert.equal(Q1[0].name.value, 'Q1');\n    assert.equal(Q1[1].name.value, 'F1');\n\n    assert.equal(Q2.length, 2);\n    assert.equal(Q2[0].name.value, 'Q2');\n    assert.equal(Q2[1].name.value, 'F2');\n\n    assert.equal(Q3.length, 3);\n    assert.equal(Q3[0].name.value, 'Q3');\n    assert.equal(Q3[1].name.value, 'F1');\n    assert.equal(Q3[2].name.value, 'F2');\n\n      const F1 = module.exports.F1.definitions;\n      const F2 = module.exports.F2.definitions;\n      const F3 = module.exports.F3.definitions;\n\n      assert.equal(F1.length, 1);\n      assert.equal(F1[0].name.value, 'F1');\n      assert.equal(F2.length, 1);\n      assert.equal(F2[0].name.value, 'F2');\n      assert.equal(F3.length, 1);\n      assert.equal(F3[0].name.value, 'F3');\n\n    });\n\n  it('tracks fragment dependencies across nested fragments', () => {\n    const jsSource = loader.call({ cacheable() {} }, `\n      fragment F11 on F { testQuery }\n      fragment F22 on F {\n        ...F11\n        testQuery2\n      }\n      fragment F33 on F {\n        ...F22\n        testQuery3\n      }\n\n      query Q1 {\n        ...F33\n      }\n\n      query Q2 {\n        id\n      }\n    `);\n\n    const module = { exports: Object.create(null) };\n    Function(\"module\", jsSource)(module);\n\n    assert.exists(module.exports.Q1);\n    assert.exists(module.exports.Q2);\n\n    const Q1 = module.exports.Q1.definitions;\n    const Q2 = module.exports.Q2.definitions;\n\n    assert.equal(Q1.length, 4);\n    assert.equal(Q1[0].name.value, 'Q1');\n    assert.equal(Q1[1].name.value, 'F33');\n    assert.equal(Q1[2].name.value, 'F22');\n    assert.equal(Q1[3].name.value, 'F11');\n\n      assert.equal(Q2.length, 1);\n\n      const F11 = module.exports.F11.definitions;\n      const F22 = module.exports.F22.definitions;\n      const F33 = module.exports.F33.definitions;\n\n      assert.equal(F11.length, 1);\n      assert.equal(F11[0].name.value, 'F11');\n      assert.equal(F22.length, 2);\n      assert.equal(F22[0].name.value, 'F22');\n      assert.equal(F22[1].name.value, 'F11');\n      assert.equal(F33.length, 3);\n      assert.equal(F33[0].name.value, 'F33');\n      assert.equal(F33[1].name.value, 'F22');\n      assert.equal(F33[2].name.value, 'F11');\n    });\n\n  it('correctly imports other files through the webpack loader', () => {\n    const query = `#import \"./fragment_definition.graphql\"\n      query {\n        author {\n          ...authorDetails\n        }\n      }`;\n    const jsSource = loader.call({ cacheable() {} }, query);\n    const module = { exports: Object.create(null) };\n    const require = (path: string) => {\n      assert.equal(path, './fragment_definition.graphql');\n      return gql`\n        fragment authorDetails on Author {\n          firstName\n          lastName\n        }`;\n    };\n    Function(\"module,require\", jsSource)(module, require);\n    assert.equal(module.exports.kind, 'Document');\n    const definitions = module.exports.definitions;\n    assert.equal(definitions.length, 2);\n    assert.equal(definitions[0].kind, 'OperationDefinition');\n    assert.equal(definitions[1].kind, 'FragmentDefinition');\n  });\n\n  it('tracks fragment dependencies across fragments loaded via the webpack loader', () => {\n    const query = `#import \"./fragment_definition.graphql\"\n      fragment F111 on F {\n        ...F222\n      }\n\n      query Q1 {\n        ...F111\n      }\n\n      query Q2 {\n        a\n      }\n      `;\n    const jsSource = loader.call({ cacheable() {} }, query);\n    const module = { exports: Object.create(null) };\n    const require = (path: string) => {\n      assert.equal(path, './fragment_definition.graphql');\n      return gql`\n        fragment F222 on F {\n          f1\n          f2\n        }`;\n    };\n    Function(\"module,require\", jsSource)(module, require);\n\n    assert.exists(module.exports.Q1);\n    assert.exists(module.exports.Q2);\n\n    const Q1 = module.exports.Q1.definitions;\n    const Q2 = module.exports.Q2.definitions;\n\n    assert.equal(Q1.length, 3);\n    assert.equal(Q1[0].name.value, 'Q1');\n    assert.equal(Q1[1].name.value, 'F111');\n    assert.equal(Q1[2].name.value, 'F222');\n\n    assert.equal(Q2.length, 1);\n  });\n\n  it('does not complain when presented with normal comments', (done) => {\n    assert.doesNotThrow(() => {\n      const query = `#normal comment\n        query {\n          author {\n            ...authorDetails\n          }\n        }`;\n      const jsSource = loader.call({ cacheable() {} }, query);\n      const module = { exports: Object.create(null) };\n      Function(\"module\", jsSource)(module);\n      assert.equal(module.exports.kind, 'Document');\n      done();\n    });\n  });\n\n  it('returns the same object for the same query', () => {\n    assert.isTrue(gql`{ sameQuery }` === gql`{ sameQuery }`);\n  });\n\n  it('returns the same object for the same query, even with whitespace differences', () => {\n    assert.isTrue(gql`{ sameQuery }` === gql`  { sameQuery,   }`);\n  });\n\n  const fragmentAst = gql`\n  fragment UserFragment on User {\n    firstName\n    lastName\n  }\n`;\n\n  it('returns the same object for the same fragment', () => {\n    assert.isTrue(gql`fragment same on Same { sameQuery }` ===\n      gql`fragment same on Same { sameQuery }`);\n  });\n\n  it('returns the same object for the same document with substitution', () => {\n    // We know that calling `gql` on a fragment string will always return\n    // the same document, so we can reuse `fragmentAst`\n    assert.isTrue(gql`{ ...UserFragment } ${fragmentAst}` ===\n      gql`{ ...UserFragment } ${fragmentAst}`);\n  });\n\n  it('can reference a fragment that references as fragment', () => {\n    const secondFragmentAst = gql`\n      fragment SecondUserFragment on User {\n        ...UserFragment\n      }\n      ${fragmentAst}\n    `;\n\n    const ast = gql`\n      {\n        user(id: 5) {\n          ...SecondUserFragment\n        }\n      }\n      ${secondFragmentAst}\n    `;\n\n    assert.deepEqual(ast, gql`\n      {\n        user(id: 5) {\n          ...SecondUserFragment\n        }\n      }\n      fragment SecondUserFragment on User {\n        ...UserFragment\n      }\n      fragment UserFragment on User {\n        firstName\n        lastName\n      }\n    `);\n  });\n\n  describe('fragment warnings', () => {\n    let warnings = [];\n    const oldConsoleWarn = console.warn;\n    beforeEach(() => {\n      gql.resetCaches();\n      warnings = [];\n      console.warn = (w: string) => warnings.push(w);\n    });\n    afterEach(() => {\n      console.warn = oldConsoleWarn;\n    });\n\n    it('warns if you use the same fragment name for different fragments', () => {\n      const frag1 = gql`fragment TestSame on Bar { fieldOne }`;\n      const frag2 = gql`fragment TestSame on Bar { fieldTwo }`;\n\n      assert.isFalse(frag1 === frag2);\n      assert.equal(warnings.length, 1);\n    });\n\n    it('does not warn if you use the same fragment name for the same fragment', () => {\n      const frag1 = gql`fragment TestDifferent on Bar { fieldOne }`;\n      const frag2 = gql`fragment TestDifferent on Bar { fieldOne }`;\n\n      assert.isTrue(frag1 === frag2);\n      assert.equal(warnings.length, 0);\n    });\n\n    it('does not warn if you use the same embedded fragment in two different queries', () => {\n      const frag1 = gql`fragment TestEmbedded on Bar { field }`;\n      const query1 = gql`{ bar { fieldOne ...TestEmbedded } } ${frag1}`;\n      const query2 = gql`{ bar { fieldTwo ...TestEmbedded } } ${frag1}`;\n\n      assert.isFalse(query1 === query2);\n      assert.equal(warnings.length, 0);\n    });\n\n    it('does not warn if you use the same fragment name for embedded and non-embedded fragments', () => {\n      const frag1 = gql`fragment TestEmbeddedTwo on Bar { field }`;\n      gql`{ bar { ...TestEmbedded } } ${frag1}`;\n      gql`{ bar { ...TestEmbedded } } fragment TestEmbeddedTwo on Bar { field }`;\n\n      assert.equal(warnings.length, 0);\n    });\n  });\n\n  describe('unique fragments', () => {\n    beforeEach(() => {\n      gql.resetCaches();\n    });\n\n    it('strips duplicate fragments from the document', () => {\n      const frag1 = gql`fragment TestDuplicate on Bar { field }`;\n      const query1 = gql`{ bar { fieldOne ...TestDuplicate } } ${frag1} ${frag1}`;\n      const query2 = gql`{ bar { fieldOne ...TestDuplicate } } ${frag1}`;\n\n      assert.equal(query1.definitions.length, 2);\n      assert.equal(query1.definitions[1].kind, 'FragmentDefinition');\n      // We don't test strict equality between the two queries because the source.body parsed from the\n      // document is not the same, but the set of definitions should be.\n      assert.deepEqual(query1.definitions, query2.definitions);\n    });\n\n    it('ignores duplicate fragments from second-level imports when using the webpack loader', () => {\n      // take a require function and a query string, use the webpack loader to process it\n      const load = (\n        require: (path: string) => DocumentNode | null,\n        query: string,\n      ): DocumentNode | null => {\n        const jsSource = loader.call({ cacheable() {} }, query);\n        const module = { exports: Object.create(null) };\n        Function(\"require,module\", jsSource)(require, module);\n        return module.exports;\n      }\n\n      const test_require = (path: string) => {\n        switch (path) {\n        case './friends.graphql':\n          return load(test_require, [\n            '#import \"./person.graphql\"',\n            'fragment friends on Hero { friends { ...person } }',\n          ].join('\\n'));\n        case './enemies.graphql':\n          return load(test_require, [\n            '#import \"./person.graphql\"',\n            'fragment enemies on Hero { enemies { ...person } }',\n          ].join('\\n'));\n        case './person.graphql':\n          return load(test_require, 'fragment person on Person { name }\\n');\n        default:\n          return null;\n        };\n      };\n\n      const result = load(test_require, [\n        '#import \"./friends.graphql\"',\n        '#import \"./enemies.graphql\"',\n        'query { hero { ...friends ...enemies } }',\n      ].join('\\n'))!;\n\n      assert.equal(result.kind, 'Document');\n      assert.equal(result.definitions.length, 4, 'after deduplication, only 4 fragments should remain');\n      assert.equal(result.definitions[0].kind, 'OperationDefinition');\n\n      // the rest of the definitions should be fragments and contain one of\n      // each: \"friends\", \"enemies\", \"person\". Order does not matter\n      const fragments = result.definitions.slice(1) as FragmentDefinitionNode[];\n      assert(fragments.every(fragment => fragment.kind === 'FragmentDefinition'))\n      assert(fragments.some(fragment => fragment.name.value === 'friends'))\n      assert(fragments.some(fragment => fragment.name.value === 'enemies'))\n      assert(fragments.some(fragment => fragment.name.value === 'person'))\n    });\n  });\n\n  // How to make this work?\n  // it.only('can reference a fragment passed as a document via shorthand', () => {\n  //   const ast = gql`\n  //     {\n  //       user(id: 5) {\n  //         ...${userFragmentDocument}\n  //       }\n  //     }\n  //   `;\n  //\n  //   assert.deepEqual(ast, gql`\n  //     {\n  //       user(id: 5) {\n  //         ...UserFragment\n  //       }\n  //     }\n  //     fragment UserFragment on User {\n  //       firstName\n  //       lastName\n  //     }\n  //   `);\n  // });\n});\n"], "names": ["parse", "assert", "gql"], "mappings": ";;;;;;;AASA,IAAM,QAAQ,GAAG,IAAI,GAAG,EAAwB,CAAC;AAGjD,IAAM,iBAAiB,GAAG,IAAI,GAAG,EAAuB,CAAC;AAEzD,IAAI,qBAAqB,GAAG,IAAI,CAAC;AACjC,IAAI,6BAA6B,GAAG,KAAK,CAAC;AAI1C,SAAS,SAAS,CAAC,MAAc;IAC/B,OAAO,MAAM,CAAC,OAAO,CAAC,SAAS,EAAE,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC;AAC/C,CAAC;AAED,SAAS,eAAe,CAAC,GAAa;IACpC,OAAO,SAAS,CAAC,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,KAAK,EAAE,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;AAClE,CAAC;AAKD,SAAS,gBAAgB,CAAC,GAAiB;IACzC,IAAM,QAAQ,GAAG,IAAI,GAAG,EAAU,CAAC;IACnC,IAAM,WAAW,GAAqB,EAAE,CAAC;IAEzC,GAAG,CAAC,WAAW,CAAC,OAAO,CAAC,UAAA,kBAAkB;QACxC,IAAI,kBAAkB,CAAC,IAAI,KAAK,oBAAoB,EAAE;YACpD,IAAI,YAAY,GAAG,kBAAkB,CAAC,IAAI,CAAC,KAAK,CAAC;YACjD,IAAI,SAAS,GAAG,eAAe,CAAC,kBAAkB,CAAC,GAAI,CAAC,CAAC;YAGzD,IAAI,YAAY,GAAG,iBAAiB,CAAC,GAAG,CAAC,YAAY,CAAE,CAAC;YACxD,IAAI,YAAY,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,SAAS,CAAC,EAAE;gBAGhD,IAAI,qBAAqB,EAAE;oBACzB,OAAO,CAAC,IAAI,CAAC,8BAA8B,GAAG,YAAY,GAAG,oBAAoB;0BAC7E,iGAAiG;0BACjG,8EAA8E,CAAC,CAAC;iBACrF;aACF;iBAAM,IAAI,CAAC,YAAY,EAAE;gBACxB,iBAAiB,CAAC,GAAG,CAAC,YAAY,EAAE,YAAY,GAAG,IAAI,GAAG,CAAC,CAAC;aAC7D;YAED,YAAY,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;YAE5B,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,SAAS,CAAC,EAAE;gBAC5B,QAAQ,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;gBACxB,WAAW,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;aACtC;SACF;aAAM;YACL,WAAW,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;SACtC;KACF,CAAC,CAAC;IAEH,yCACK,GAAG,KACN,WAAW,aAAA,IACX;AACJ,CAAC;AAED,SAAS,QAAQ,CAAC,GAAiB;IACjC,IAAM,OAAO,GAAG,IAAI,GAAG,CAAsB,GAAG,CAAC,WAAW,CAAC,CAAC;IAE9D,OAAO,CAAC,OAAO,CAAC,UAAA,IAAI;QAClB,IAAI,IAAI,CAAC,GAAG;YAAE,OAAO,IAAI,CAAC,GAAG,CAAC;QAC9B,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,UAAA,GAAG;YAC3B,IAAM,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC;YACxB,IAAI,KAAK,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;gBACtC,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;aACpB;SACF,CAAC,CAAC;KACJ,CAAC,CAAC;IAEH,IAAM,GAAG,GAAG,GAAG,CAAC,GAA0B,CAAC;IAC3C,IAAI,GAAG,EAAE;QACP,OAAO,GAAG,CAAC,UAAU,CAAC;QACtB,OAAO,GAAG,CAAC,QAAQ,CAAC;KACrB;IAED,OAAO,GAAG,CAAC;AACb,CAAC;AAED,SAAS,aAAa,CAAC,MAAc;IACnC,IAAI,QAAQ,GAAG,SAAS,CAAC,MAAM,CAAC,CAAC;IACjC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE;QAC3B,IAAM,MAAM,GAAGA,aAAK,CAAC,MAAM,EAAE;YAC3B,6BAA6B,+BAAA;YAC7B,4BAA4B,EAAE,6BAA6B;SACrD,CAAC,CAAC;QACV,IAAI,CAAC,MAAM,IAAI,MAAM,CAAC,IAAI,KAAK,UAAU,EAAE;YACzC,MAAM,IAAI,KAAK,CAAC,+BAA+B,CAAC,CAAC;SAClD;QACD,QAAQ,CAAC,GAAG,CACV,QAAQ,EAGR,QAAQ,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAC,CACnC,CAAC;KACH;IACD,OAAO,QAAQ,CAAC,GAAG,CAAC,QAAQ,CAAE,CAAC;AACjC,CAAC;SAGe,GAAG,CACjB,QAAoC;IACpC,cAAc;SAAd,UAAc,EAAd,qBAAc,EAAd,IAAc;QAAd,6BAAc;;IAGd,IAAI,OAAO,QAAQ,KAAK,QAAQ,EAAE;QAChC,QAAQ,GAAG,CAAC,QAAQ,CAAC,CAAC;KACvB;IAED,IAAI,MAAM,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;IAEzB,IAAI,CAAC,OAAO,CAAC,UAAC,GAAG,EAAE,CAAC;QAClB,IAAI,GAAG,IAAI,GAAG,CAAC,IAAI,KAAK,UAAU,EAAE;YAClC,MAAM,IAAI,GAAG,CAAC,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC;SAC/B;aAAM;YACL,MAAM,IAAI,GAAG,CAAC;SACf;QACD,MAAM,IAAI,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;KAC3B,CAAC,CAAC;IAEH,OAAO,aAAa,CAAC,MAAM,CAAC,CAAC;AAC/B,CAAC;SAEe,WAAW;IACzB,QAAQ,CAAC,KAAK,EAAE,CAAC;IACjB,iBAAiB,CAAC,KAAK,EAAE,CAAC;AAC5B,CAAC;SAEe,uBAAuB;IACrC,qBAAqB,GAAG,KAAK,CAAC;AAChC,CAAC;SAEe,mCAAmC;IACjD,6BAA6B,GAAG,IAAI,CAAC;AACvC,CAAC;SAEe,oCAAoC;IAClD,6BAA6B,GAAG,KAAK,CAAC;AACxC,CAAC;AAED,IAAM,MAAM,GAAG;IACb,GAAG,KAAA;IACH,WAAW,aAAA;IACX,uBAAuB,yBAAA;IACvB,mCAAmC,qCAAA;IACnC,oCAAoC,sCAAA;CACrC,CAAC;AAEF,WAAiB,KAAG;IAEhB,SAAG,GAKD,MAAM,IALL,EACH,iBAAW,GAIT,MAAM,YAJG,EACX,6BAAuB,GAGrB,MAAM,wBAHe,EACvB,yCAAmC,GAEjC,MAAM,oCAF2B,EACnC,0CAAoC,GAClC,MAAM,qCAD4B,CAC3B;AACb,CAAC,EARgB,GAAG,KAAH,GAAG,QAQnB;AAED,GAAG,CAAC,SAAO,CAAA,GAAG,GAAG,CAAC;AAElB,YAAe,GAAG;;ACvKlB,IAAM,MAAM,GAAG,OAAO,CAAC,WAAW,CAAC,CAAC;AAEpC,QAAQ,CAAC,KAAK,EAAE;IACd,EAAE,CAAC,gBAAgB,EAAE;QACnBC,WAAM,CAAC,KAAK,CAACC,KAAG,wFAAA,eAAe,KAAC,IAAI,EAAE,UAAU,CAAC,CAAC;KACnD,CAAC,CAAC;IAEH,EAAE,CAAC,0CAA0C,EAAE;QAC7CD,WAAM,CAAC,KAAK,CAACC,KAAG,CAAC,eAAe,CAAC,CAAC,IAAI,EAAE,UAAU,CAAC,CAAC;KACrD,CAAC,CAAC;IAEH,EAAE,CAAC,yCAAyC,EAAE;QAC5C,IAAM,GAAG,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;QAChCD,WAAM,CAAC,KAAK,CAACC,KAAG,qGAAA,mBAAmB,EAAW,OAAM,KAAjB,GAAG,CAAC,OAAO,EAAO,IAAI,EAAE,UAAU,CAAC,CAAC;QACvED,WAAM,CAAC,KAAK,CAACC,KAAG,qGAAA,mBAAmB,EAAI,OAAM,KAAV,IAAI,EAAO,IAAI,EAAE,UAAU,CAAC,CAAC;QAChED,WAAM,CAAC,KAAK,CAACC,KAAG,qGAAA,mBAAmB,EAAC,OAAM,KAAP,CAAC,EAAO,IAAI,EAAE,UAAU,CAAC,CAAC;KAC9D,CAAC,CAAC;IAEH,EAAE,CAAC,mEAAmE,EAAE;QACtE,IAAM,YAAY,GAAG,qDAAqD,CAAC;QAE3E,IAAM,QAAQ,GAAG,MAAM,CAAC,IAAI,CAC1B,EAAE,SAAS,iBAAK,EAAE,EAClB,YAAY,CACb,CAAC;QACF,IAAM,MAAM,GAAG,EAAE,OAAO,EAAE,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC;QAEhD,QAAQ,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC,MAAM,CAAC,CAAC;QAErC,IAAM,QAAQ,GAAGA,KAAG,6GAAA,gCAAiC,EAAc,EAAE,KAAhB,MAAM,CAAC,OAAO,CAAE,CAAC;QACtED,WAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,IAAI,EAAE,UAAU,CAAC,CAAC;QACxCA,WAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,WAAW,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;QAC7CA,WAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,qBAAqB,CAAC,CAAC;QAClEA,WAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,oBAAoB,CAAC,CAAC;KAClE,CAAC,CAAC;IAEH,EAAE,CAAC,uCAAuC,EAAE;QAC1C,IAAM,QAAQ,GAAG,MAAM,CAAC,IAAI,CAAC,EAAE,SAAS,iBAAK,EAAE,EAAE,eAAe,CAAC,CAAC;QAClE,IAAM,MAAM,GAAG,EAAE,OAAO,EAAE,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC;QAChD,QAAQ,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC,MAAM,CAAC,CAAC;QACrCA,WAAM,CAAC,KAAK,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,EAAE,UAAU,CAAC,CAAC;KAC/C,CAAC,CAAC;IAEH,EAAE,CAAC,4CAA4C,EAAE;QAC/C,IAAM,QAAQ,GAAG,MAAM,CAAC,IAAI,CAAC,EAAE,SAAS,iBAAK,EAAE,EAAE,sCAEhD,CAAC,CAAC;QACH,IAAM,MAAM,GAAG,EAAE,OAAO,EAAE,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC;QAChD,QAAQ,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC,MAAM,CAAC,CAAC;QAErCA,WAAM,CAAC,KAAK,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,EAAE,UAAU,CAAC,CAAC;QAC9CA,WAAM,CAAC,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;QACjCA,WAAM,CAAC,KAAK,CAAC,MAAM,CAAC,OAAO,CAAC,EAAE,CAAC,IAAI,EAAE,UAAU,CAAC,CAAC;QACjDA,WAAM,CAAC,KAAK,CAAC,MAAM,CAAC,OAAO,CAAC,EAAE,CAAC,WAAW,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;KACvD,CAAC,CAAC;IAEH,EAAE,CAAC,4CAA4C,EAAE;QAC/C,IAAM,QAAQ,GAAG,MAAM,CAAC,IAAI,CAAC,EAAE,SAAS,iBAAK,EAAE,EAAE,sCAEhD,CAAC,CAAC;QACH,IAAM,MAAM,GAAG,EAAE,OAAO,EAAE,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC;QAChD,QAAQ,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC,MAAM,CAAC,CAAC;QACrCA,WAAM,CAAC,SAAS,CAAC,MAAM,CAAC,OAAO,CAAC,WAAW,EAAE,MAAM,CAAC,OAAO,CAAC,EAAE,CAAC,WAAW,CAAC,CAAC;KAC7E,CAAC,CAAC;IAEH,EAAE,CAAC,gDAAgD,EAAE;QACnD,IAAM,QAAQ,GAAG,MAAM,CAAC,IAAI,CAAC,EAAE,SAAS,iBAAK,EAAE,EAAE,qEAGhD,CAAC,CAAC;QACH,IAAM,MAAM,GAAG,EAAE,OAAO,EAAE,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC;QAChD,QAAQ,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC,MAAM,CAAC,CAAC;QAErCA,WAAM,CAAC,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;QACjCA,WAAM,CAAC,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;QACjCA,WAAM,CAAC,KAAK,CAAC,MAAM,CAAC,OAAO,CAAC,EAAE,CAAC,IAAI,EAAE,UAAU,CAAC,CAAC;QACjDA,WAAM,CAAC,KAAK,CAAC,MAAM,CAAC,OAAO,CAAC,EAAE,CAAC,IAAI,EAAE,UAAU,CAAC,CAAC;QACjDA,WAAM,CAAC,KAAK,CAAC,MAAM,CAAC,OAAO,CAAC,EAAE,CAAC,WAAW,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;QACtDA,WAAM,CAAC,KAAK,CAAC,MAAM,CAAC,OAAO,CAAC,EAAE,CAAC,WAAW,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;KACvD,CAAC,CAAC;IAEH,EAAE,CAAC,4CAA4C,EAAE;QAC/CC,KAAG,CAAC,mCAAmC,EAAE,CAAC;QAE1C,IAAM,MAAM,GAAQA,KAAG,2HAAA,kDAAkD,IAAA,CAAC;QAC1ED,WAAM,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,EAAE,UAAU,CAAC,CAAC;QACtCA,WAAM,CAAC,MAAM,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,mBAAmB,CAAC,CAAC;QAEzDC,KAAG,CAAC,oCAAoC,EAAE,CAAA;KAC3C,CAAC,CAAC;IAGH,EAAE,CAAC,mDAAmD,EAAE;QACtD,IAAM,QAAQ,GAAG,MAAM,CAAC,IAAI,CAAC,EAAE,SAAS,iBAAK,EAAE,EAAE,qGAIhD,CAAC,CAAC;QACH,IAAM,MAAM,GAAG,EAAE,OAAO,EAAE,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC;QAChD,QAAQ,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC,MAAM,CAAC,CAAC;QAErCD,WAAM,CAAC,SAAS,CAAC,MAAM,CAAC,OAAO,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;QACvCA,WAAM,CAAC,SAAS,CAAC,MAAM,CAAC,OAAO,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;QACvCA,WAAM,CAAC,SAAS,CAAC,MAAM,CAAC,OAAO,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;KACxC,CAAC,CAAC;IAEH,EAAE,CAAC,2EAA2E,EAAE;QAC9E,IAAM,QAAQ,GAAG,MAAM,CAAC,IAAI,CAAC,EAAE,SAAS,iBAAK,EAAE,EAAE,yOAUhD,CAAC,CAAC;QACH,IAAM,MAAM,GAAG,EAAE,OAAO,EAAE,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC;QAChD,QAAQ,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC,MAAM,CAAC,CAAC;QAErCA,WAAM,CAAC,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;QACjCA,WAAM,CAAC,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;QACjCA,WAAM,CAAC,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;QACjC,IAAM,EAAE,GAAG,MAAM,CAAC,OAAO,CAAC,EAAE,CAAC,WAAW,CAAC;QACzC,IAAM,EAAE,GAAG,MAAM,CAAC,OAAO,CAAC,EAAE,CAAC,WAAW,CAAC;QACzC,IAAM,EAAE,GAAG,MAAM,CAAC,OAAO,CAAC,EAAE,CAAC,WAAW,CAAC;QAEzCA,WAAM,CAAC,KAAK,CAAC,EAAE,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;QAC3BA,WAAM,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;QACrCA,WAAM,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;QAErCA,WAAM,CAAC,KAAK,CAAC,EAAE,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;QAC3BA,WAAM,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;QACrCA,WAAM,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;QAErCA,WAAM,CAAC,KAAK,CAAC,EAAE,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;QAC3BA,WAAM,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;QACrCA,WAAM,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;QACrCA,WAAM,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;QAEnC,IAAM,EAAE,GAAG,MAAM,CAAC,OAAO,CAAC,EAAE,CAAC,WAAW,CAAC;QACzC,IAAM,EAAE,GAAG,MAAM,CAAC,OAAO,CAAC,EAAE,CAAC,WAAW,CAAC;QACzC,IAAM,EAAE,GAAG,MAAM,CAAC,OAAO,CAAC,EAAE,CAAC,WAAW,CAAC;QAEzCA,WAAM,CAAC,KAAK,CAAC,EAAE,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;QAC3BA,WAAM,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;QACrCA,WAAM,CAAC,KAAK,CAAC,EAAE,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;QAC3BA,WAAM,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;QACrCA,WAAM,CAAC,KAAK,CAAC,EAAE,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;QAC3BA,WAAM,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;KAEtC,CAAC,CAAC;IAEL,EAAE,CAAC,sDAAsD,EAAE;QACzD,IAAM,QAAQ,GAAG,MAAM,CAAC,IAAI,CAAC,EAAE,SAAS,iBAAK,EAAE,EAAE,qRAkBhD,CAAC,CAAC;QAEH,IAAM,MAAM,GAAG,EAAE,OAAO,EAAE,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC;QAChD,QAAQ,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC,MAAM,CAAC,CAAC;QAErCA,WAAM,CAAC,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;QACjCA,WAAM,CAAC,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;QAEjC,IAAM,EAAE,GAAG,MAAM,CAAC,OAAO,CAAC,EAAE,CAAC,WAAW,CAAC;QACzC,IAAM,EAAE,GAAG,MAAM,CAAC,OAAO,CAAC,EAAE,CAAC,WAAW,CAAC;QAEzCA,WAAM,CAAC,KAAK,CAAC,EAAE,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;QAC3BA,WAAM,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;QACrCA,WAAM,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;QACtCA,WAAM,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;QACtCA,WAAM,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;QAEpCA,WAAM,CAAC,KAAK,CAAC,EAAE,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;QAE3B,IAAM,GAAG,GAAG,MAAM,CAAC,OAAO,CAAC,GAAG,CAAC,WAAW,CAAC;QAC3C,IAAM,GAAG,GAAG,MAAM,CAAC,OAAO,CAAC,GAAG,CAAC,WAAW,CAAC;QAC3C,IAAM,GAAG,GAAG,MAAM,CAAC,OAAO,CAAC,GAAG,CAAC,WAAW,CAAC;QAE3CA,WAAM,CAAC,KAAK,CAAC,GAAG,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;QAC5BA,WAAM,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;QACvCA,WAAM,CAAC,KAAK,CAAC,GAAG,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;QAC5BA,WAAM,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;QACvCA,WAAM,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;QACvCA,WAAM,CAAC,KAAK,CAAC,GAAG,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;QAC5BA,WAAM,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;QACvCA,WAAM,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;QACvCA,WAAM,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;KACxC,CAAC,CAAC;IAEL,EAAE,CAAC,0DAA0D,EAAE;QAC7D,IAAM,KAAK,GAAG,4HAKV,CAAC;QACL,IAAM,QAAQ,GAAG,MAAM,CAAC,IAAI,CAAC,EAAE,SAAS,iBAAK,EAAE,EAAE,KAAK,CAAC,CAAC;QACxD,IAAM,MAAM,GAAG,EAAE,OAAO,EAAE,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC;QAChD,IAAM,OAAO,GAAG,UAAC,IAAY;YAC3BA,WAAM,CAAC,KAAK,CAAC,IAAI,EAAE,+BAA+B,CAAC,CAAC;YACpD,OAAOC,KAAG,2KAAA,kGAIN,KAAC;SACN,CAAC;QACF,QAAQ,CAAC,gBAAgB,EAAE,QAAQ,CAAC,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;QACtDD,WAAM,CAAC,KAAK,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,EAAE,UAAU,CAAC,CAAC;QAC9C,IAAM,WAAW,GAAG,MAAM,CAAC,OAAO,CAAC,WAAW,CAAC;QAC/CA,WAAM,CAAC,KAAK,CAAC,WAAW,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;QACpCA,WAAM,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,qBAAqB,CAAC,CAAC;QACzDA,WAAM,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,oBAAoB,CAAC,CAAC;KACzD,CAAC,CAAC;IAEH,EAAE,CAAC,6EAA6E,EAAE;QAChF,IAAM,KAAK,GAAG,+LAYX,CAAC;QACJ,IAAM,QAAQ,GAAG,MAAM,CAAC,IAAI,CAAC,EAAE,SAAS,iBAAK,EAAE,EAAE,KAAK,CAAC,CAAC;QACxD,IAAM,MAAM,GAAG,EAAE,OAAO,EAAE,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC;QAChD,IAAM,OAAO,GAAG,UAAC,IAAY;YAC3BA,WAAM,CAAC,KAAK,CAAC,IAAI,EAAE,+BAA+B,CAAC,CAAC;YACpD,OAAOC,KAAG,gJAAA,uEAIN,KAAC;SACN,CAAC;QACF,QAAQ,CAAC,gBAAgB,EAAE,QAAQ,CAAC,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;QAEtDD,WAAM,CAAC,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;QACjCA,WAAM,CAAC,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;QAEjC,IAAM,EAAE,GAAG,MAAM,CAAC,OAAO,CAAC,EAAE,CAAC,WAAW,CAAC;QACzC,IAAM,EAAE,GAAG,MAAM,CAAC,OAAO,CAAC,EAAE,CAAC,WAAW,CAAC;QAEzCA,WAAM,CAAC,KAAK,CAAC,EAAE,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;QAC3BA,WAAM,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;QACrCA,WAAM,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;QACvCA,WAAM,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;QAEvCA,WAAM,CAAC,KAAK,CAAC,EAAE,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;KAC5B,CAAC,CAAC;IAEH,EAAE,CAAC,uDAAuD,EAAE,UAAC,IAAI;QAC/DA,WAAM,CAAC,YAAY,CAAC;YAClB,IAAM,KAAK,GAAG,4GAKV,CAAC;YACL,IAAM,QAAQ,GAAG,MAAM,CAAC,IAAI,CAAC,EAAE,SAAS,iBAAK,EAAE,EAAE,KAAK,CAAC,CAAC;YACxD,IAAM,MAAM,GAAG,EAAE,OAAO,EAAE,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC;YAChD,QAAQ,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC,MAAM,CAAC,CAAC;YACrCA,WAAM,CAAC,KAAK,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,EAAE,UAAU,CAAC,CAAC;YAC9C,IAAI,EAAE,CAAC;SACR,CAAC,CAAC;KACJ,CAAC,CAAC;IAEH,EAAE,CAAC,4CAA4C,EAAE;QAC/CA,WAAM,CAAC,MAAM,CAACC,KAAG,wFAAA,eAAe,SAAKA,KAAG,0FAAA,eAAe,IAAA,CAAC,CAAC;KAC1D,CAAC,CAAC;IAEH,EAAE,CAAC,8EAA8E,EAAE;QACjFD,WAAM,CAAC,MAAM,CAACC,KAAG,0FAAA,eAAe,SAAKA,KAAG,+FAAA,oBAAoB,IAAA,CAAC,CAAC;KAC/D,CAAC,CAAC;IAEH,IAAM,WAAW,GAAGA,KAAG,oJAAA,yEAKxB,IAAA,CAAC;IAEA,EAAE,CAAC,+CAA+C,EAAE;QAClDD,WAAM,CAAC,MAAM,CAACC,KAAG,gHAAA,qCAAqC,SACpDA,KAAG,gHAAA,qCAAqC,IAAA,CAAC,CAAC;KAC7C,CAAC,CAAC;IAEH,EAAE,CAAC,iEAAiE,EAAE;QAGpED,WAAM,CAAC,MAAM,CAACC,KAAG,qGAAA,sBAAuB,EAAW,EAAE,KAAb,WAAW,MACjDA,KAAG,qGAAA,sBAAuB,EAAW,EAAE,KAAb,WAAW,CAAE,CAAC,CAAC;KAC5C,CAAC,CAAC;IAEH,EAAE,CAAC,sDAAsD,EAAE;QACzD,IAAM,iBAAiB,GAAGA,KAAG,8KAAA,yFAIzB,EAAW,QACd,KADG,WAAW,CACd,CAAC;QAEF,IAAM,GAAG,GAAGA,KAAG,oLAAA,+FAMX,EAAiB,QACpB,KADG,iBAAiB,CACpB,CAAC;QAEFD,WAAM,CAAC,SAAS,CAAC,GAAG,EAAEC,KAAG,4UAAA,iQAaxB,KAAC,CAAC;KACJ,CAAC,CAAC;IAEH,QAAQ,CAAC,mBAAmB,EAAE;QAC5B,IAAI,QAAQ,GAAG,EAAE,CAAC;QAClB,IAAM,cAAc,GAAG,OAAO,CAAC,IAAI,CAAC;QACpC,UAAU,CAAC;YACTA,KAAG,CAAC,WAAW,EAAE,CAAC;YAClB,QAAQ,GAAG,EAAE,CAAC;YACd,OAAO,CAAC,IAAI,GAAG,UAAC,CAAS,IAAK,OAAA,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,GAAA,CAAC;SAChD,CAAC,CAAC;QACH,SAAS,CAAC;YACR,OAAO,CAAC,IAAI,GAAG,cAAc,CAAC;SAC/B,CAAC,CAAC;QAEH,EAAE,CAAC,iEAAiE,EAAE;YACpE,IAAM,KAAK,GAAGA,KAAG,kHAAA,uCAAuC,IAAA,CAAC;YACzD,IAAM,KAAK,GAAGA,KAAG,kHAAA,uCAAuC,IAAA,CAAC;YAEzDD,WAAM,CAAC,OAAO,CAAC,KAAK,KAAK,KAAK,CAAC,CAAC;YAChCA,WAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;SAClC,CAAC,CAAC;QAEH,EAAE,CAAC,uEAAuE,EAAE;YAC1E,IAAM,KAAK,GAAGC,KAAG,uHAAA,4CAA4C,IAAA,CAAC;YAC9D,IAAM,KAAK,GAAGA,KAAG,uHAAA,4CAA4C,IAAA,CAAC;YAE9DD,WAAM,CAAC,MAAM,CAAC,KAAK,KAAK,KAAK,CAAC,CAAC;YAC/BA,WAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;SAClC,CAAC,CAAC;QAEH,EAAE,CAAC,8EAA8E,EAAE;YACjF,IAAM,KAAK,GAAGC,KAAG,mHAAA,wCAAwC,IAAA,CAAC;YAC1D,IAAM,MAAM,GAAGA,KAAG,sHAAA,uCAAwC,EAAK,EAAE,KAAP,KAAK,CAAE,CAAC;YAClE,IAAM,MAAM,GAAGA,KAAG,sHAAA,uCAAwC,EAAK,EAAE,KAAP,KAAK,CAAE,CAAC;YAElED,WAAM,CAAC,OAAO,CAAC,MAAM,KAAK,MAAM,CAAC,CAAC;YAClCA,WAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;SAClC,CAAC,CAAC;QAEH,EAAE,CAAC,yFAAyF,EAAE;YAC5F,IAAM,KAAK,GAAGC,KAAG,sHAAA,2CAA2C,IAAA,CAAC;YAC7DA,KAAG,6GAAA,8BAA+B,EAAK,EAAE,KAAP,KAAK,EAAG;YAC1CA,KAAG,kJAAA,uEAAuE,KAAC;YAE3ED,WAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;SAClC,CAAC,CAAC;KACJ,CAAC,CAAC;IAEH,QAAQ,CAAC,kBAAkB,EAAE;QAC3B,UAAU,CAAC;YACTC,KAAG,CAAC,WAAW,EAAE,CAAC;SACnB,CAAC,CAAC;QAEH,EAAE,CAAC,8CAA8C,EAAE;YACjD,IAAM,KAAK,GAAGA,KAAG,oHAAA,yCAAyC,IAAA,CAAC;YAC3D,IAAM,MAAM,GAAGA,KAAG,4HAAA,wCAAyC,EAAK,GAAI,EAAK,EAAE,KAAhB,KAAK,EAAI,KAAK,CAAE,CAAC;YAC5E,IAAM,MAAM,GAAGA,KAAG,uHAAA,wCAAyC,EAAK,EAAE,KAAP,KAAK,CAAE,CAAC;YAEnED,WAAM,CAAC,KAAK,CAAC,MAAM,CAAC,WAAW,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;YAC3CA,WAAM,CAAC,KAAK,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,oBAAoB,CAAC,CAAC;YAG/DA,WAAM,CAAC,SAAS,CAAC,MAAM,CAAC,WAAW,EAAE,MAAM,CAAC,WAAW,CAAC,CAAC;SAC1D,CAAC,CAAC;QAEH,EAAE,CAAC,qFAAqF,EAAE;YAExF,IAAM,IAAI,GAAG,UACX,OAA8C,EAC9C,KAAa;gBAEb,IAAM,QAAQ,GAAG,MAAM,CAAC,IAAI,CAAC,EAAE,SAAS,iBAAK,EAAE,EAAE,KAAK,CAAC,CAAC;gBACxD,IAAM,MAAM,GAAG,EAAE,OAAO,EAAE,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC;gBAChD,QAAQ,CAAC,gBAAgB,EAAE,QAAQ,CAAC,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;gBACtD,OAAO,MAAM,CAAC,OAAO,CAAC;aACvB,CAAA;YAED,IAAM,YAAY,GAAG,UAAC,IAAY;gBAChC,QAAQ,IAAI;oBACZ,KAAK,mBAAmB;wBACtB,OAAO,IAAI,CAAC,YAAY,EAAE;4BACxB,4BAA4B;4BAC5B,oDAAoD;yBACrD,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;oBAChB,KAAK,mBAAmB;wBACtB,OAAO,IAAI,CAAC,YAAY,EAAE;4BACxB,4BAA4B;4BAC5B,oDAAoD;yBACrD,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;oBAChB,KAAK,kBAAkB;wBACrB,OAAO,IAAI,CAAC,YAAY,EAAE,sCAAsC,CAAC,CAAC;oBACpE;wBACE,OAAO,IAAI,CAAC;iBACb;aACF,CAAC;YAEF,IAAM,MAAM,GAAG,IAAI,CAAC,YAAY,EAAE;gBAChC,6BAA6B;gBAC7B,6BAA6B;gBAC7B,0CAA0C;aAC3C,CAAC,IAAI,CAAC,IAAI,CAAC,CAAE,CAAC;YAEfA,WAAM,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,EAAE,UAAU,CAAC,CAAC;YACtCA,WAAM,CAAC,KAAK,CAAC,MAAM,CAAC,WAAW,CAAC,MAAM,EAAE,CAAC,EAAE,qDAAqD,CAAC,CAAC;YAClGA,WAAM,CAAC,KAAK,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,qBAAqB,CAAC,CAAC;YAIhE,IAAM,SAAS,GAAG,MAAM,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC,CAA6B,CAAC;YAC1EA,WAAM,CAAC,SAAS,CAAC,KAAK,CAAC,UAAA,QAAQ,IAAI,OAAA,QAAQ,CAAC,IAAI,KAAK,oBAAoB,GAAA,CAAC,CAAC,CAAA;YAC3EA,WAAM,CAAC,SAAS,CAAC,IAAI,CAAC,UAAA,QAAQ,IAAI,OAAA,QAAQ,CAAC,IAAI,CAAC,KAAK,KAAK,SAAS,GAAA,CAAC,CAAC,CAAA;YACrEA,WAAM,CAAC,SAAS,CAAC,IAAI,CAAC,UAAA,QAAQ,IAAI,OAAA,QAAQ,CAAC,IAAI,CAAC,KAAK,KAAK,SAAS,GAAA,CAAC,CAAC,CAAA;YACrEA,WAAM,CAAC,SAAS,CAAC,IAAI,CAAC,UAAA,QAAQ,IAAI,OAAA,QAAQ,CAAC,IAAI,CAAC,KAAK,KAAK,QAAQ,GAAA,CAAC,CAAC,CAAA;SACrE,CAAC,CAAC;KACJ,CAAC,CAAC;AAwBL,CAAC,CAAC,CAAC;;;"}