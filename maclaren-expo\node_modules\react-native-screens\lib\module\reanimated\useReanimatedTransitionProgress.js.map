{"version": 3, "names": ["React", "ReanimatedTransitionProgressContext", "useReanimatedTransitionProgress", "progress", "useContext", "undefined", "Error"], "sources": ["useReanimatedTransitionProgress.tsx"], "sourcesContent": ["import * as React from 'react';\nimport ReanimatedTransitionProgressContext from './ReanimatedTransitionProgressContext';\n\nexport default function useReanimatedTransitionProgress() {\n  const progress = React.useContext(ReanimatedTransitionProgressContext);\n\n  if (progress === undefined) {\n    throw new Error(\n      \"Couldn't find values for reanimated transition progress. Are you inside a screen in Native Stack?\"\n    );\n  }\n\n  return progress;\n}\n"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAOC,mCAAmC,MAAM,uCAAuC;AAEvF,eAAe,SAASC,+BAA+BA,CAAA,EAAG;EACxD,MAAMC,QAAQ,GAAGH,KAAK,CAACI,UAAU,CAACH,mCAAmC,CAAC;EAEtE,IAAIE,QAAQ,KAAKE,SAAS,EAAE;IAC1B,MAAM,IAAIC,KAAK,CACb,mGACF,CAAC;EACH;EAEA,OAAOH,QAAQ;AACjB"}