{"version": 3, "names": ["_codegenNativeComponent", "_interopRequireDefault", "require", "obj", "__esModule", "default", "_default", "codegenNativeComponent", "exports"], "sources": ["ScreenStackHeaderSubviewNativeComponent.ts"], "sourcesContent": ["import codegenNativeComponent from 'react-native/Libraries/Utilities/codegenNativeComponent';\nimport type { ViewProps } from 'react-native';\nimport type { WithDefault } from 'react-native/Libraries/Types/CodegenTypes';\n\nexport type HeaderSubviewTypes =\n  | 'back'\n  | 'right'\n  | 'left'\n  | 'title'\n  | 'center'\n  | 'searchBar';\n\nexport interface NativeProps extends ViewProps {\n  type?: WithDefault<HeaderSubviewTypes, 'left'>;\n}\n\nexport default codegenNativeComponent<NativeProps>(\n  'RNSScreenStackHeaderSubview',\n  {}\n);\n"], "mappings": ";;;;;;AAAA,IAAAA,uBAAA,GAAAC,sBAAA,CAAAC,OAAA;AAA6F,SAAAD,uBAAAE,GAAA,WAAAA,GAAA,IAAAA,GAAA,CAAAC,UAAA,GAAAD,GAAA,KAAAE,OAAA,EAAAF,GAAA;AAAA,IAAAG,QAAA,GAgB9E,IAAAC,+BAAsB,EACnC,6BAA6B,EAC7B,CAAC,CACH,CAAC;AAAAC,OAAA,CAAAH,OAAA,GAAAC,QAAA"}