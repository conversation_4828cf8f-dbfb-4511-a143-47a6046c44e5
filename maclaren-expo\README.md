# 🏍️ MACLAREN - Expo Version

**La mejor app de transporte en moto optimizada para Expo Go**

## 🚀 Instalación y Ejecución

### 1. Instalar dependencias
```bash
cd maclaren-expo
npm install
```

### 2. Iniciar el servidor de desarrollo
```bash
npx expo start
```

### 3. Abrir en tu teléfono
1. **Instala Expo Go** en tu teléfono:
   - [Android](https://play.google.com/store/apps/details?id=host.exp.exponent)
   - [iOS](https://apps.apple.com/app/expo-go/id982107779)

2. **Escanea el QR** que aparece en la terminal o navegador

3. **¡Listo!** La app se abrirá en tu teléfono

## 🔧 Configuración del Backend

### 1. Iniciar el servidor backend
```bash
cd ../backend
npm install
node server.js
```

### 2. Actualizar la IP del servidor
En `src/services/SocketService.js`, cambia la línea 19:
```javascript
// Cambia 'localhost' por la IP de tu computadora
this.socket = io('http://TU_IP_LOCAL:3002', {
```

Para encontrar tu IP:
- **Windows**: `ipconfig`
- **Mac/Linux**: `ifconfig`

## 📱 Funcionalidades Implementadas

### ✅ Para Pasajeros
- 🏠 Pantalla de inicio con solicitud de viajes
- 📍 Selección de ubicación de recogida y destino
- 💰 Cálculo automático de tarifas
- 🗺️ Seguimiento en tiempo real del conductor
- 💬 Chat directo con el conductor
- 📋 Historial completo de viajes
- ⭐ Sistema de calificaciones
- 👤 Perfil personalizable

### ✅ Para Conductores
- 🏍️ Panel de control con estado online/offline
- 📨 Recepción de solicitudes de viaje en tiempo real
- ✅ Aceptar/rechazar viajes con un toque
- 💰 Seguimiento detallado de ganancias
- 📊 Estadísticas diarias, semanales y mensuales
- 🗺️ Navegación integrada
- 💬 Chat con pasajeros
- 👤 Perfil con información del vehículo

### ✅ Características Técnicas
- 🔄 Comunicación en tiempo real con Socket.IO
- 📍 GPS y geolocalización precisa
- 🔔 Notificaciones push nativas
- 🎨 Interfaz moderna y animada
- 📱 Optimizado para dispositivos móviles
- 🔐 Sistema de autenticación seguro

## 🎯 Cómo Probar la App

### Modo Pasajero
1. Abre la app y selecciona "Soy Pasajero"
2. Regístrate o inicia sesión
3. Permite permisos de ubicación
4. Solicita un viaje seleccionando destino
5. Espera a que un conductor acepte
6. Usa el chat y seguimiento en tiempo real

### Modo Conductor
1. Abre la app y selecciona "Soy Conductor"
2. Regístrate con información del vehículo
3. Activa el estado "En línea"
4. Recibe y acepta solicitudes de viaje
5. Completa viajes y ve tus ganancias

## 🔧 Estructura del Proyecto

```
maclaren-expo/
├── src/
│   ├── screens/           # Pantallas de la app
│   │   ├── WelcomeScreen.js
│   │   ├── LoginScreen.js
│   │   ├── RegisterScreen.js
│   │   ├── passenger/     # Pantallas de pasajero
│   │   ├── driver/        # Pantallas de conductor
│   │   └── shared/        # Pantallas compartidas
│   ├── navigation/        # Navegación
│   ├── services/          # Servicios (Socket, GPS, etc.)
│   └── components/        # Componentes reutilizables
├── assets/               # Imágenes e iconos
├── App.js               # Componente principal
└── package.json         # Dependencias
```

## 🌟 Próximas Funcionalidades

- 💳 Sistema de pagos integrado
- 🎵 Reproductor de música durante el viaje
- 🚨 Botón de emergencia
- 📸 Verificación de identidad con cámara
- 🏆 Sistema de recompensas y logros
- 📈 Analytics avanzados para conductores

## 🐛 Solución de Problemas

### La app no se conecta al servidor
- Verifica que el backend esté ejecutándose
- Asegúrate de usar la IP correcta en SocketService.js
- Verifica que tu teléfono y computadora estén en la misma red WiFi

### Problemas con permisos
- Ve a Configuración > Apps > Expo Go > Permisos
- Activa ubicación, cámara y notificaciones

### La app se cierra inesperadamente
- Revisa los logs en la terminal de Expo
- Asegúrate de tener todas las dependencias instaladas

## 📞 Soporte

¿Necesitas ayuda? Contacta al equipo de desarrollo:
- 📧 Email: <EMAIL>
- 💬 Chat: Disponible en la app

---

**¡Disfruta usando MACLAREN! 🏍️✨**
