/**
 * Pantalla Principal del Pasajero - MACLAREN Expo
 */

import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  ScrollView,
  Alert,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import * as Animatable from 'react-native-animatable';
import { StatusBar } from 'expo-status-bar';
import * as Location from 'expo-location';

const PassengerHomeScreen = () => {
  const [currentLocation, setCurrentLocation] = useState(null);
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    getCurrentLocation();
  }, []);

  const getCurrentLocation = async () => {
    try {
      const { status } = await Location.requestForegroundPermissionsAsync();
      if (status !== 'granted') {
        Alert.alert('Permiso requerido', 'Necesitamos acceso a tu ubicación');
        return;
      }

      const location = await Location.getCurrentPositionAsync({});
      setCurrentLocation(location.coords);
    } catch (error) {
      console.error('Error obteniendo ubicación:', error);
    }
  };

  const requestTrip = () => {
    setIsLoading(true);
    
    setTimeout(() => {
      setIsLoading(false);
      Alert.alert('¡Éxito!', 'Solicitud de viaje enviada. Buscando conductor...');
    }, 2000);
  };

  return (
    <View style={styles.container}>
      <StatusBar style="light" />
      
      {/* Header */}
      <LinearGradient
        colors={['#ff6b35', '#f7931e']}
        style={styles.header}>
        
        <Animatable.View 
          animation="fadeInDown" 
          duration={800}
          style={styles.headerContent}>
          
          <Text style={styles.greeting}>¡Hola! 👋</Text>
          <Text style={styles.subtitle}>¿A dónde quieres ir?</Text>
        </Animatable.View>
      </LinearGradient>

      {/* Contenido principal */}
      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        
        {/* Ubicación actual */}
        <Animatable.View 
          animation="fadeInUp" 
          duration={800}
          delay={200}
          style={styles.locationCard}>
          
          <Text style={styles.cardTitle}>📍 Tu ubicación</Text>
          <Text style={styles.locationText}>
            {currentLocation 
              ? `${currentLocation.latitude.toFixed(4)}, ${currentLocation.longitude.toFixed(4)}`
              : 'Obteniendo ubicación...'
            }
          </Text>
          
          <TouchableOpacity 
            style={styles.updateLocationButton}
            onPress={getCurrentLocation}>
            <Text style={styles.updateLocationText}>🔄 Actualizar</Text>
          </TouchableOpacity>
        </Animatable.View>

        {/* Destinos rápidos */}
        <Animatable.View 
          animation="fadeInUp" 
          duration={800}
          delay={400}
          style={styles.quickDestinations}>
          
          <Text style={styles.sectionTitle}>🎯 Destinos populares</Text>
          
          <TouchableOpacity style={styles.destinationButton}>
            <Text style={styles.destinationIcon}>🏢</Text>
            <Text style={styles.destinationText}>Centro Comercial</Text>
          </TouchableOpacity>
          
          <TouchableOpacity style={styles.destinationButton}>
            <Text style={styles.destinationIcon}>🏫</Text>
            <Text style={styles.destinationText}>Universidad</Text>
          </TouchableOpacity>
          
          <TouchableOpacity style={styles.destinationButton}>
            <Text style={styles.destinationIcon}>✈️</Text>
            <Text style={styles.destinationText}>Aeropuerto</Text>
          </TouchableOpacity>
        </Animatable.View>

        {/* Botón principal */}
        <Animatable.View 
          animation="fadeInUp" 
          duration={800}
          delay={600}
          style={styles.requestSection}>
          
          <TouchableOpacity
            style={[styles.requestButton, isLoading && styles.requestButtonDisabled]}
            onPress={requestTrip}
            disabled={isLoading}>
            <Text style={styles.requestButtonText}>
              {isLoading ? '⏳ Buscando conductor...' : '🚀 Solicitar Viaje'}
            </Text>
          </TouchableOpacity>
        </Animatable.View>

        {/* Información adicional */}
        <Animatable.View 
          animation="fadeInUp" 
          duration={800}
          delay={800}
          style={styles.infoSection}>
          
          <View style={styles.infoCard}>
            <Text style={styles.infoIcon}>⚡</Text>
            <Text style={styles.infoText}>Viajes rápidos y seguros</Text>
          </View>
          
          <View style={styles.infoCard}>
            <Text style={styles.infoIcon}>💰</Text>
            <Text style={styles.infoText}>Tarifas justas y transparentes</Text>
          </View>
          
          <View style={styles.infoCard}>
            <Text style={styles.infoIcon}>📍</Text>
            <Text style={styles.infoText}>Seguimiento en tiempo real</Text>
          </View>
        </Animatable.View>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  header: {
    paddingTop: 50,
    paddingBottom: 30,
    paddingHorizontal: 20,
  },
  headerContent: {
    alignItems: 'center',
  },
  greeting: {
    fontSize: 28,
    fontWeight: 'bold',
    color: 'white',
    marginBottom: 5,
  },
  subtitle: {
    fontSize: 18,
    color: 'rgba(255, 255, 255, 0.9)',
  },
  content: {
    flex: 1,
    paddingHorizontal: 20,
  },
  locationCard: {
    backgroundColor: 'white',
    borderRadius: 15,
    padding: 20,
    marginTop: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  cardTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 10,
  },
  locationText: {
    fontSize: 14,
    color: '#666',
    marginBottom: 15,
  },
  updateLocationButton: {
    backgroundColor: '#f8f9fa',
    borderRadius: 8,
    paddingVertical: 8,
    paddingHorizontal: 15,
    alignSelf: 'flex-start',
  },
  updateLocationText: {
    color: '#ff6b35',
    fontWeight: '600',
  },
  quickDestinations: {
    marginTop: 20,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 15,
  },
  destinationButton: {
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 15,
    marginBottom: 10,
    flexDirection: 'row',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  destinationIcon: {
    fontSize: 20,
    marginRight: 15,
  },
  destinationText: {
    fontSize: 16,
    color: '#333',
    fontWeight: '600',
  },
  requestSection: {
    marginTop: 30,
    marginBottom: 20,
  },
  requestButton: {
    backgroundColor: '#ff6b35',
    borderRadius: 15,
    paddingVertical: 18,
    alignItems: 'center',
    shadowColor: '#ff6b35',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 4.65,
    elevation: 8,
  },
  requestButtonDisabled: {
    backgroundColor: '#ccc',
    shadowOpacity: 0,
    elevation: 0,
  },
  requestButtonText: {
    color: 'white',
    fontSize: 18,
    fontWeight: 'bold',
  },
  infoSection: {
    marginBottom: 30,
  },
  infoCard: {
    backgroundColor: 'white',
    borderRadius: 10,
    padding: 15,
    marginBottom: 10,
    flexDirection: 'row',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
  },
  infoIcon: {
    fontSize: 18,
    marginRight: 15,
  },
  infoText: {
    fontSize: 14,
    color: '#666',
    flex: 1,
  },
});

export default PassengerHomeScreen;
