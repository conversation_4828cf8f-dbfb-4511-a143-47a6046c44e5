/**
 * Pantalla de Ganancias del Conductor - MACLAREN
 * Estadísticas y historial de ganancias
 */

import React, {useState, useEffect} from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  ScrollView,
  StatusBar,
  Dimensions,
} from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import * as Animatable from 'react-native-animatable';

const {width} = Dimensions.get('window');

interface EarningsData {
  today: number;
  week: number;
  month: number;
  total: number;
  tripsToday: number;
  tripsWeek: number;
  tripsMonth: number;
  averageRating: number;
  hoursOnline: number;
}

interface DailyEarning {
  date: string;
  earnings: number;
  trips: number;
  hours: number;
}

const EarningsScreen: React.FC = () => {
  const [selectedPeriod, setSelectedPeriod] = useState<'day' | 'week' | 'month'>('day');
  const [earningsData, setEarningsData] = useState<EarningsData>({
    today: 125000,
    week: 850000,
    month: 3200000,
    total: 15600000,
    tripsToday: 8,
    tripsWeek: 52,
    tripsMonth: 198,
    averageRating: 4.8,
    hoursOnline: 7.5,
  });

  const [dailyEarnings, setDailyEarnings] = useState<DailyEarning[]>([
    {date: 'Hoy', earnings: 125000, trips: 8, hours: 7.5},
    {date: 'Ayer', earnings: 98000, trips: 6, hours: 6.2},
    {date: 'Lun', earnings: 142000, trips: 9, hours: 8.1},
    {date: 'Dom', earnings: 89000, trips: 5, hours: 5.8},
    {date: 'Sáb', earnings: 156000, trips: 11, hours: 9.2},
    {date: 'Vie', earnings: 134000, trips: 8, hours: 7.8},
    {date: 'Jue', earnings: 106000, trips: 7, hours: 6.5},
  ]);

  const getCurrentPeriodData = () => {
    switch (selectedPeriod) {
      case 'day':
        return {
          earnings: earningsData.today,
          trips: earningsData.tripsToday,
          label: 'Hoy',
        };
      case 'week':
        return {
          earnings: earningsData.week,
          trips: earningsData.tripsWeek,
          label: 'Esta Semana',
        };
      case 'month':
        return {
          earnings: earningsData.month,
          trips: earningsData.tripsMonth,
          label: 'Este Mes',
        };
      default:
        return {
          earnings: earningsData.today,
          trips: earningsData.tripsToday,
          label: 'Hoy',
        };
    }
  };

  const currentData = getCurrentPeriodData();

  const renderDailyEarning = (item: DailyEarning, index: number) => (
    <Animatable.View
      key={index}
      animation="fadeInUp"
      duration={500}
      delay={index * 100}
      style={styles.dailyEarningCard}>
      
      <Text style={styles.dailyDate}>{item.date}</Text>
      <Text style={styles.dailyAmount}>${item.earnings.toLocaleString()}</Text>
      <Text style={styles.dailyTrips}>{item.trips} viajes</Text>
      <Text style={styles.dailyHours}>{item.hours}h</Text>
    </Animatable.View>
  );

  return (
    <View style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor="#ff6b35" />
      
      {/* Header */}
      <LinearGradient
        colors={['#ff6b35', '#f7931e']}
        style={styles.header}>
        
        <Animatable.View 
          animation="fadeInDown"
          duration={800}
          style={styles.headerContent}>
          
          <Text style={styles.headerTitle}>💰 Mis Ganancias</Text>
          <Text style={styles.headerSubtitle}>Resumen de ingresos</Text>
        </Animatable.View>
      </LinearGradient>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        
        {/* Selector de período */}
        <Animatable.View 
          animation="fadeInUp"
          duration={800}
          delay={200}
          style={styles.periodSelector}>
          
          <TouchableOpacity
            style={[
              styles.periodButton,
              selectedPeriod === 'day' && styles.periodButtonActive
            ]}
            onPress={() => setSelectedPeriod('day')}>
            <Text style={[
              styles.periodText,
              selectedPeriod === 'day' && styles.periodTextActive
            ]}>
              Hoy
            </Text>
          </TouchableOpacity>
          
          <TouchableOpacity
            style={[
              styles.periodButton,
              selectedPeriod === 'week' && styles.periodButtonActive
            ]}
            onPress={() => setSelectedPeriod('week')}>
            <Text style={[
              styles.periodText,
              selectedPeriod === 'week' && styles.periodTextActive
            ]}>
              Semana
            </Text>
          </TouchableOpacity>
          
          <TouchableOpacity
            style={[
              styles.periodButton,
              selectedPeriod === 'month' && styles.periodButtonActive
            ]}
            onPress={() => setSelectedPeriod('month')}>
            <Text style={[
              styles.periodText,
              selectedPeriod === 'month' && styles.periodTextActive
            ]}>
              Mes
            </Text>
          </TouchableOpacity>
        </Animatable.View>

        {/* Resumen principal */}
        <Animatable.View 
          animation="fadeInUp"
          duration={800}
          delay={400}
          style={styles.mainSummary}>
          
          <Text style={styles.summaryLabel}>{currentData.label}</Text>
          <Text style={styles.summaryAmount}>
            ${currentData.earnings.toLocaleString()}
          </Text>
          <Text style={styles.summaryTrips}>
            {currentData.trips} viajes completados
          </Text>
          
          <View style={styles.summaryStats}>
            <View style={styles.statItem}>
              <Text style={styles.statValue}>
                ${Math.round(currentData.earnings / currentData.trips).toLocaleString()}
              </Text>
              <Text style={styles.statLabel}>Promedio por viaje</Text>
            </View>
            <View style={styles.statDivider} />
            <View style={styles.statItem}>
              <Text style={styles.statValue}>⭐ {earningsData.averageRating}</Text>
              <Text style={styles.statLabel}>Rating promedio</Text>
            </View>
            <View style={styles.statDivider} />
            <View style={styles.statItem}>
              <Text style={styles.statValue}>{earningsData.hoursOnline}h</Text>
              <Text style={styles.statLabel}>Horas en línea</Text>
            </View>
          </View>
        </Animatable.View>

        {/* Gráfico de ganancias diarias */}
        <Animatable.View 
          animation="fadeInUp"
          duration={800}
          delay={600}
          style={styles.chartSection}>
          
          <Text style={styles.sectionTitle}>📊 Últimos 7 días</Text>
          
          <ScrollView 
            horizontal 
            showsHorizontalScrollIndicator={false}
            style={styles.chartContainer}>
            {dailyEarnings.map((item, index) => renderDailyEarning(item, index))}
          </ScrollView>
        </Animatable.View>

        {/* Estadísticas adicionales */}
        <Animatable.View 
          animation="fadeInUp"
          duration={800}
          delay={800}
          style={styles.additionalStats}>
          
          <Text style={styles.sectionTitle}>📈 Estadísticas Generales</Text>
          
          <View style={styles.statsGrid}>
            <View style={styles.statsCard}>
              <Text style={styles.statsCardValue}>
                ${earningsData.total.toLocaleString()}
              </Text>
              <Text style={styles.statsCardLabel}>Total Ganado</Text>
            </View>
            
            <View style={styles.statsCard}>
              <Text style={styles.statsCardValue}>
                ${Math.round(earningsData.month / 30).toLocaleString()}
              </Text>
              <Text style={styles.statsCardLabel}>Promedio Diario</Text>
            </View>
            
            <View style={styles.statsCard}>
              <Text style={styles.statsCardValue}>
                ${Math.round(earningsData.month / earningsData.hoursOnline / 30).toLocaleString()}
              </Text>
              <Text style={styles.statsCardLabel}>Por Hora</Text>
            </View>
            
            <View style={styles.statsCard}>
              <Text style={styles.statsCardValue}>
                {Math.round(earningsData.tripsMonth / 30)}
              </Text>
              <Text style={styles.statsCardLabel}>Viajes/Día</Text>
            </View>
          </View>
        </Animatable.View>

        {/* Consejos para mejorar ganancias */}
        <Animatable.View 
          animation="fadeInUp"
          duration={800}
          delay={1000}
          style={styles.tipsSection}>
          
          <Text style={styles.sectionTitle}>💡 Consejos para Ganar Más</Text>
          
          <View style={styles.tipCard}>
            <Text style={styles.tipIcon}>🕐</Text>
            <View style={styles.tipContent}>
              <Text style={styles.tipTitle}>Horarios Pico</Text>
              <Text style={styles.tipText}>
                Trabaja entre 7-9 AM y 5-8 PM para más solicitudes
              </Text>
            </View>
          </View>
          
          <View style={styles.tipCard}>
            <Text style={styles.tipIcon}>📍</Text>
            <View style={styles.tipContent}>
              <Text style={styles.tipTitle}>Zonas Estratégicas</Text>
              <Text style={styles.tipText}>
                Mantente cerca de centros comerciales y universidades
              </Text>
            </View>
          </View>
          
          <View style={styles.tipCard}>
            <Text style={styles.tipIcon}>⭐</Text>
            <View style={styles.tipContent}>
              <Text style={styles.tipTitle}>Mantén tu Rating</Text>
              <Text style={styles.tipText}>
                Un rating alto te da acceso a más viajes premium
              </Text>
            </View>
          </View>
        </Animatable.View>

        <View style={styles.bottomSpacing} />
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  header: {
    paddingTop: 50,
    paddingBottom: 20,
    paddingHorizontal: 20,
    alignItems: 'center',
  },
  headerContent: {
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: 'white',
    marginBottom: 5,
  },
  headerSubtitle: {
    fontSize: 16,
    color: 'rgba(255, 255, 255, 0.8)',
  },
  content: {
    flex: 1,
    paddingHorizontal: 20,
  },
  periodSelector: {
    flexDirection: 'row',
    backgroundColor: 'white',
    borderRadius: 15,
    padding: 5,
    marginTop: 20,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  periodButton: {
    flex: 1,
    paddingVertical: 12,
    alignItems: 'center',
    borderRadius: 10,
  },
  periodButtonActive: {
    backgroundColor: '#ff6b35',
  },
  periodText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#666',
  },
  periodTextActive: {
    color: 'white',
  },
  mainSummary: {
    backgroundColor: 'white',
    borderRadius: 20,
    padding: 25,
    marginTop: 20,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 4},
    shadowOpacity: 0.15,
    shadowRadius: 8,
    elevation: 5,
  },
  summaryLabel: {
    fontSize: 16,
    color: '#666',
    marginBottom: 10,
  },
  summaryAmount: {
    fontSize: 36,
    fontWeight: 'bold',
    color: '#2ecc71',
    marginBottom: 5,
  },
  summaryTrips: {
    fontSize: 16,
    color: '#666',
    marginBottom: 20,
  },
  summaryStats: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#f8f9fa',
    borderRadius: 15,
    paddingVertical: 15,
    paddingHorizontal: 20,
    width: '100%',
  },
  statItem: {
    alignItems: 'center',
    flex: 1,
  },
  statValue: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 2,
  },
  statLabel: {
    fontSize: 12,
    color: '#666',
    textAlign: 'center',
  },
  statDivider: {
    width: 1,
    height: 30,
    backgroundColor: '#ddd',
    marginHorizontal: 15,
  },
  chartSection: {
    backgroundColor: 'white',
    borderRadius: 15,
    padding: 20,
    marginTop: 20,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 15,
  },
  chartContainer: {
    flexDirection: 'row',
  },
  dailyEarningCard: {
    alignItems: 'center',
    backgroundColor: '#f8f9fa',
    borderRadius: 12,
    padding: 15,
    marginRight: 10,
    minWidth: 80,
  },
  dailyDate: {
    fontSize: 12,
    color: '#666',
    marginBottom: 8,
  },
  dailyAmount: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#2ecc71',
    marginBottom: 4,
  },
  dailyTrips: {
    fontSize: 11,
    color: '#666',
    marginBottom: 2,
  },
  dailyHours: {
    fontSize: 11,
    color: '#999',
  },
  additionalStats: {
    backgroundColor: 'white',
    borderRadius: 15,
    padding: 20,
    marginTop: 20,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  statsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  statsCard: {
    width: (width - 60) / 2 - 10,
    backgroundColor: '#f8f9fa',
    borderRadius: 12,
    padding: 15,
    alignItems: 'center',
    marginBottom: 15,
  },
  statsCardValue: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 5,
  },
  statsCardLabel: {
    fontSize: 12,
    color: '#666',
    textAlign: 'center',
  },
  tipsSection: {
    backgroundColor: 'white',
    borderRadius: 15,
    padding: 20,
    marginTop: 20,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  tipCard: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#f8f9fa',
    borderRadius: 12,
    padding: 15,
    marginBottom: 10,
  },
  tipIcon: {
    fontSize: 24,
    marginRight: 15,
  },
  tipContent: {
    flex: 1,
  },
  tipTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 4,
  },
  tipText: {
    fontSize: 14,
    color: '#666',
    lineHeight: 18,
  },
  bottomSpacing: {
    height: 30,
  },
});

export default EarningsScreen;
