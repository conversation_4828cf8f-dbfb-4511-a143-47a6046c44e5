// @flow strict
export { validate } from './validate';

export { ValidationContext } from './ValidationContext';
export type { ValidationRule } from './ValidationContext';

// All validation rules in the GraphQL Specification.
export { specifiedRules } from './specifiedRules';

// Spec Section: "Executable Definitions"
export { ExecutableDefinitionsRule } from './rules/ExecutableDefinitionsRule';

// Spec Section: "Field Selections on Objects, Interfaces, and Unions Types"
export { FieldsOnCorrectTypeRule } from './rules/FieldsOnCorrectTypeRule';

// Spec Section: "Fragments on Composite Types"
export { FragmentsOnCompositeTypesRule } from './rules/FragmentsOnCompositeTypesRule';

// Spec Section: "Argument Names"
export { KnownArgumentNamesRule } from './rules/KnownArgumentNamesRule';

// Spec Section: "Directives Are Defined"
export { KnownDirectivesRule } from './rules/KnownDirectivesRule';

// Spec Section: "Fragment spread target defined"
export { KnownFragmentNamesRule } from './rules/KnownFragmentNamesRule';

// Spec Section: "Fragment Spread Type Existence"
export { KnownTypeNamesRule } from './rules/KnownTypeNamesRule';

// Spec Section: "Lone Anonymous Operation"
export { LoneAnonymousOperationRule } from './rules/LoneAnonymousOperationRule';

// Spec Section: "Fragments must not form cycles"
export { NoFragmentCyclesRule } from './rules/NoFragmentCyclesRule';

// Spec Section: "All Variable Used Defined"
export { NoUndefinedVariablesRule } from './rules/NoUndefinedVariablesRule';

// Spec Section: "Fragments must be used"
export { NoUnusedFragmentsRule } from './rules/NoUnusedFragmentsRule';

// Spec Section: "All Variables Used"
export { NoUnusedVariablesRule } from './rules/NoUnusedVariablesRule';

// Spec Section: "Field Selection Merging"
export { OverlappingFieldsCanBeMergedRule } from './rules/OverlappingFieldsCanBeMergedRule';

// Spec Section: "Fragment spread is possible"
export { PossibleFragmentSpreadsRule } from './rules/PossibleFragmentSpreadsRule';

// Spec Section: "Argument Optionality"
export { ProvidedRequiredArgumentsRule } from './rules/ProvidedRequiredArgumentsRule';

// Spec Section: "Leaf Field Selections"
export { ScalarLeafsRule } from './rules/ScalarLeafsRule';

// Spec Section: "Subscriptions with Single Root Field"
export { SingleFieldSubscriptionsRule } from './rules/SingleFieldSubscriptionsRule';

// Spec Section: "Argument Uniqueness"
export { UniqueArgumentNamesRule } from './rules/UniqueArgumentNamesRule';

// Spec Section: "Directives Are Unique Per Location"
export { UniqueDirectivesPerLocationRule } from './rules/UniqueDirectivesPerLocationRule';

// Spec Section: "Fragment Name Uniqueness"
export { UniqueFragmentNamesRule } from './rules/UniqueFragmentNamesRule';

// Spec Section: "Input Object Field Uniqueness"
export { UniqueInputFieldNamesRule } from './rules/UniqueInputFieldNamesRule';

// Spec Section: "Operation Name Uniqueness"
export { UniqueOperationNamesRule } from './rules/UniqueOperationNamesRule';

// Spec Section: "Variable Uniqueness"
export { UniqueVariableNamesRule } from './rules/UniqueVariableNamesRule';

// Spec Section: "Values Type Correctness"
export { ValuesOfCorrectTypeRule } from './rules/ValuesOfCorrectTypeRule';

// Spec Section: "Variables are Input Types"
export { VariablesAreInputTypesRule } from './rules/VariablesAreInputTypesRule';

// Spec Section: "All Variable Usages Are Allowed"
export { VariablesInAllowedPositionRule } from './rules/VariablesInAllowedPositionRule';

// SDL-specific validation rules
export { LoneSchemaDefinitionRule } from './rules/LoneSchemaDefinitionRule';
export { UniqueOperationTypesRule } from './rules/UniqueOperationTypesRule';
export { UniqueTypeNamesRule } from './rules/UniqueTypeNamesRule';
export { UniqueEnumValueNamesRule } from './rules/UniqueEnumValueNamesRule';
export { UniqueFieldDefinitionNamesRule } from './rules/UniqueFieldDefinitionNamesRule';
export { UniqueDirectiveNamesRule } from './rules/UniqueDirectiveNamesRule';
export { PossibleTypeExtensionsRule } from './rules/PossibleTypeExtensionsRule';

// Optional rules not defined by the GraphQL Specification
export { NoDeprecatedCustomRule } from './rules/custom/NoDeprecatedCustomRule';
export { NoSchemaIntrospectionCustomRule } from './rules/custom/NoSchemaIntrospectionCustomRule';
