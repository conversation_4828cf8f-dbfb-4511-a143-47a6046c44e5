/**
 * Pantalla de Registro - MACLAREN
 * Registro de nuevos usuarios (Pasajeros y Conductores)
 */

import React, {useState, useEffect} from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  Alert,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
  StatusBar,
  Switch,
} from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import * as Animatable from 'react-native-animatable';
import {StackNavigationProp} from '@react-navigation/stack';
import {RouteProp} from '@react-navigation/native';

type RegisterScreenProps = {
  navigation: StackNavigationProp<any>;
  route: RouteProp<any>;
  onRegister: (userData: any, userType: string) => void;
};

const RegisterScreen: React.FC<RegisterScreenProps> = ({navigation, route, onRegister}) => {
  const [name, setName] = useState('');
  const [email, setEmail] = useState('');
  const [phone, setPhone] = useState('');
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [isDriver, setIsDriver] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  
  // Campos adicionales para conductores
  const [vehicleModel, setVehicleModel] = useState('');
  const [licensePlate, setLicensePlate] = useState('');
  const [licenseNumber, setLicenseNumber] = useState('');

  const userType = route.params?.userType || 'passenger';

  useEffect(() => {
    StatusBar.setBarStyle('light-content');
    StatusBar.setBackgroundColor('#ff6b35');
    setIsDriver(userType === 'driver');
  }, [userType]);

  const isValidEmail = (email: string) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  };

  const isValidPhone = (phone: string) => {
    const phoneRegex = /^[+]?[\d\s\-\(\)]{10,}$/;
    return phoneRegex.test(phone);
  };

  const handleRegister = async () => {
    // Validaciones básicas
    if (!name.trim() || !email.trim() || !phone.trim() || !password.trim()) {
      Alert.alert('Error', 'Por favor completa todos los campos obligatorios');
      return;
    }

    if (!isValidEmail(email)) {
      Alert.alert('Error', 'Por favor ingresa un email válido');
      return;
    }

    if (!isValidPhone(phone)) {
      Alert.alert('Error', 'Por favor ingresa un número de teléfono válido');
      return;
    }

    if (password.length < 6) {
      Alert.alert('Error', 'La contraseña debe tener al menos 6 caracteres');
      return;
    }

    if (password !== confirmPassword) {
      Alert.alert('Error', 'Las contraseñas no coinciden');
      return;
    }

    // Validaciones adicionales para conductores
    if (isDriver) {
      if (!vehicleModel.trim() || !licensePlate.trim() || !licenseNumber.trim()) {
        Alert.alert('Error', 'Los conductores deben completar todos los campos del vehículo');
        return;
      }
    }

    setIsLoading(true);

    try {
      // Simular registro (aquí conectarías con tu API)
      const userData = {
        id: Date.now().toString(),
        name: name.trim(),
        email: email.trim().toLowerCase(),
        phone: phone.trim(),
        userType: isDriver ? 'driver' : 'passenger',
        ...(isDriver && {
          vehicle: {
            model: vehicleModel.trim(),
            licensePlate: licensePlate.trim().toUpperCase(),
            licenseNumber: licenseNumber.trim(),
          }
        }),
        createdAt: new Date().toISOString(),
        rating: 5.0,
        isActive: true,
      };

      // Simular delay de red
      await new Promise(resolve => setTimeout(resolve, 2000));

      Alert.alert(
        '¡Registro Exitoso!', 
        `Bienvenido a MACLAREN, ${name}!`,
        [
          {
            text: 'Continuar',
            onPress: () => onRegister(userData, userData.userType)
          }
        ]
      );

    } catch (error) {
      console.error('Error en registro:', error);
      Alert.alert('Error', 'No se pudo completar el registro. Intenta nuevamente.');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <LinearGradient
      colors={['#ff6b35', '#f7931e']}
      style={styles.container}>
      
      <StatusBar barStyle="light-content" backgroundColor="#ff6b35" />
      
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={styles.keyboardContainer}>
        
        <ScrollView 
          contentContainerStyle={styles.scrollContainer}
          showsVerticalScrollIndicator={false}>

          {/* Header */}
          <Animatable.View 
            animation="fadeInDown" 
            duration={800}
            style={styles.header}>
            
            <TouchableOpacity 
              style={styles.backButton}
              onPress={() => navigation.goBack()}>
              <Text style={styles.backButtonText}>←</Text>
            </TouchableOpacity>

            <Text style={styles.title}>🏍️ MACLAREN</Text>
            <Text style={styles.subtitle}>
              {isDriver ? 'Registro de Conductor' : 'Registro de Pasajero'}
            </Text>
          </Animatable.View>

          {/* Formulario */}
          <Animatable.View 
            animation="fadeInUp" 
            duration={800}
            delay={200}
            style={styles.formContainer}>

            {/* Tipo de usuario */}
            <View style={styles.userTypeContainer}>
              <Text style={styles.userTypeLabel}>Tipo de cuenta:</Text>
              <View style={styles.switchContainer}>
                <Text style={[styles.switchLabel, !isDriver && styles.switchLabelActive]}>
                  👤 Pasajero
                </Text>
                <Switch
                  value={isDriver}
                  onValueChange={setIsDriver}
                  trackColor={{false: '#ccc', true: '#ff6b35'}}
                  thumbColor={isDriver ? '#fff' : '#f4f3f4'}
                />
                <Text style={[styles.switchLabel, isDriver && styles.switchLabelActive]}>
                  🏍️ Conductor
                </Text>
              </View>
            </View>

            {/* Campos básicos */}
            <View style={styles.inputContainer}>
              <Text style={styles.inputLabel}>Nombre completo *</Text>
              <TextInput
                style={styles.input}
                placeholder="Ingresa tu nombre completo"
                placeholderTextColor="#999"
                value={name}
                onChangeText={setName}
                autoCapitalize="words"
              />
            </View>

            <View style={styles.inputContainer}>
              <Text style={styles.inputLabel}>Email *</Text>
              <TextInput
                style={styles.input}
                placeholder="<EMAIL>"
                placeholderTextColor="#999"
                value={email}
                onChangeText={setEmail}
                keyboardType="email-address"
                autoCapitalize="none"
              />
            </View>

            <View style={styles.inputContainer}>
              <Text style={styles.inputLabel}>Teléfono *</Text>
              <TextInput
                style={styles.input}
                placeholder="+57 ************"
                placeholderTextColor="#999"
                value={phone}
                onChangeText={setPhone}
                keyboardType="phone-pad"
              />
            </View>

            <View style={styles.inputContainer}>
              <Text style={styles.inputLabel}>Contraseña *</Text>
              <TextInput
                style={styles.input}
                placeholder="Mínimo 6 caracteres"
                placeholderTextColor="#999"
                value={password}
                onChangeText={setPassword}
                secureTextEntry
              />
            </View>

            <View style={styles.inputContainer}>
              <Text style={styles.inputLabel}>Confirmar contraseña *</Text>
              <TextInput
                style={styles.input}
                placeholder="Repite tu contraseña"
                placeholderTextColor="#999"
                value={confirmPassword}
                onChangeText={setConfirmPassword}
                secureTextEntry
              />
            </View>

            {/* Campos adicionales para conductores */}
            {isDriver && (
              <Animatable.View 
                animation="fadeIn" 
                duration={500}
                style={styles.driverFields}>
                
                <Text style={styles.sectionTitle}>🏍️ Información del Vehículo</Text>
                
                <View style={styles.inputContainer}>
                  <Text style={styles.inputLabel}>Modelo de la moto *</Text>
                  <TextInput
                    style={styles.input}
                    placeholder="Ej: Yamaha FZ 150"
                    placeholderTextColor="#999"
                    value={vehicleModel}
                    onChangeText={setVehicleModel}
                  />
                </View>

                <View style={styles.inputContainer}>
                  <Text style={styles.inputLabel}>Placa *</Text>
                  <TextInput
                    style={styles.input}
                    placeholder="ABC123"
                    placeholderTextColor="#999"
                    value={licensePlate}
                    onChangeText={setLicensePlate}
                    autoCapitalize="characters"
                    maxLength={6}
                  />
                </View>

                <View style={styles.inputContainer}>
                  <Text style={styles.inputLabel}>Número de licencia *</Text>
                  <TextInput
                    style={styles.input}
                    placeholder="Número de licencia de conducir"
                    placeholderTextColor="#999"
                    value={licenseNumber}
                    onChangeText={setLicenseNumber}
                  />
                </View>
              </Animatable.View>
            )}

            {/* Botón de registro */}
            <TouchableOpacity
              style={[styles.registerButton, isLoading && styles.registerButtonDisabled]}
              onPress={handleRegister}
              disabled={isLoading}>
              <Text style={styles.registerButtonText}>
                {isLoading ? '⏳ Registrando...' : '🚀 Crear Cuenta'}
              </Text>
            </TouchableOpacity>

          </Animatable.View>

          {/* Footer */}
          <Animatable.View 
            animation="fadeIn" 
            duration={800}
            delay={600}
            style={styles.footer}>
            <Text style={styles.footerText}>¿Ya tienes cuenta?</Text>
            <TouchableOpacity
              onPress={() => navigation.navigate('Login', {userType: isDriver ? 'driver' : 'passenger'})}>
              <Text style={styles.loginText}>Inicia sesión aquí</Text>
            </TouchableOpacity>
          </Animatable.View>

        </ScrollView>
      </KeyboardAvoidingView>
    </LinearGradient>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  keyboardContainer: {
    flex: 1,
  },
  scrollContainer: {
    flexGrow: 1,
    paddingHorizontal: 20,
  },
  header: {
    alignItems: 'center',
    paddingTop: 60,
    paddingBottom: 30,
  },
  backButton: {
    position: 'absolute',
    left: 0,
    top: 60,
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  backButtonText: {
    color: 'white',
    fontSize: 24,
    fontWeight: 'bold',
  },
  title: {
    fontSize: 32,
    fontWeight: 'bold',
    color: 'white',
    marginBottom: 5,
  },
  subtitle: {
    fontSize: 18,
    color: 'rgba(255, 255, 255, 0.9)',
    textAlign: 'center',
  },
  formContainer: {
    backgroundColor: 'white',
    borderRadius: 20,
    padding: 25,
    marginBottom: 20,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.3,
    shadowRadius: 4.65,
    elevation: 8,
  },
  userTypeContainer: {
    marginBottom: 25,
    alignItems: 'center',
  },
  userTypeLabel: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginBottom: 10,
  },
  switchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#f8f9fa',
    borderRadius: 25,
    padding: 10,
  },
  switchLabel: {
    fontSize: 14,
    color: '#666',
    marginHorizontal: 10,
  },
  switchLabelActive: {
    color: '#ff6b35',
    fontWeight: '600',
  },
  inputContainer: {
    marginBottom: 20,
  },
  inputLabel: {
    fontSize: 14,
    fontWeight: '600',
    color: '#333',
    marginBottom: 8,
  },
  input: {
    borderWidth: 1,
    borderColor: '#e9ecef',
    borderRadius: 12,
    paddingHorizontal: 15,
    paddingVertical: 12,
    fontSize: 16,
    backgroundColor: '#f8f9fa',
    color: '#333',
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#ff6b35',
    marginBottom: 15,
    textAlign: 'center',
  },
  driverFields: {
    borderTopWidth: 1,
    borderTopColor: '#e9ecef',
    paddingTop: 20,
    marginTop: 10,
  },
  registerButton: {
    backgroundColor: '#ff6b35',
    borderRadius: 12,
    paddingVertical: 15,
    alignItems: 'center',
    marginTop: 10,
    shadowColor: '#ff6b35',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.3,
    shadowRadius: 4.65,
    elevation: 8,
  },
  registerButtonDisabled: {
    backgroundColor: '#ccc',
    shadowOpacity: 0,
    elevation: 0,
  },
  registerButtonText: {
    color: 'white',
    fontSize: 18,
    fontWeight: 'bold',
  },
  footer: {
    alignItems: 'center',
    paddingBottom: 30,
  },
  footerText: {
    color: 'rgba(255, 255, 255, 0.8)',
    fontSize: 16,
    marginBottom: 5,
  },
  loginText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
    textDecorationLine: 'underline',
  },
});

export default RegisterScreen;
