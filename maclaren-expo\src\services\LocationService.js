/**
 * Servicio de Geolocalización para MACLAREN - Expo
 * Maneja GPS, ubicación en tiempo real usando Expo Location
 */

import * as Location from 'expo-location';
import { Alert } from 'react-native';

class LocationServiceClass {
  constructor() {
    this.watchSubscription = null;
    this.currentLocation = null;
    this.isWatching = false;
    this.locationCallback = null;
  }

  async initialize() {
    try {
      // Solicitar permisos de ubicación
      const { status } = await Location.requestForegroundPermissionsAsync();
      if (status !== 'granted') {
        Alert.alert(
          'Permiso Requerido',
          'MACLAREN necesita acceso a tu ubicación para funcionar correctamente.'
        );
        return false;
      }
      
      console.log('✅ Servicio de ubicación inicializado');
      return true;
    } catch (error) {
      console.error('❌ Error inicializando servicio de ubicación:', error);
      return false;
    }
  }

  async getCurrentLocation() {
    try {
      const location = await Location.getCurrentPositionAsync({
        accuracy: Location.Accuracy.High,
        maximumAge: 10000,
      });

      const formattedLocation = {
        latitude: location.coords.latitude,
        longitude: location.coords.longitude,
        accuracy: location.coords.accuracy,
        altitude: location.coords.altitude,
        heading: location.coords.heading,
        speed: location.coords.speed,
        timestamp: location.timestamp,
      };

      this.currentLocation = formattedLocation;
      console.log('📍 Ubicación actual obtenida:', formattedLocation);
      return formattedLocation;
    } catch (error) {
      console.error('❌ Error obteniendo ubicación:', error);
      
      let errorMessage = 'No se pudo obtener tu ubicación';
      
      if (error.code === 'E_LOCATION_SERVICES_DISABLED') {
        errorMessage = 'Los servicios de ubicación están desactivados. Actívalos en configuración.';
      } else if (error.code === 'E_LOCATION_UNAVAILABLE') {
        errorMessage = 'Ubicación no disponible. Verifica que el GPS esté activado.';
      }

      throw new Error(errorMessage);
    }
  }

  async startWatching(callback) {
    if (this.isWatching) {
      console.log('⚠️ Ya se está monitoreando la ubicación');
      return false;
    }

    try {
      this.locationCallback = callback;

      this.watchSubscription = await Location.watchPositionAsync(
        {
          accuracy: Location.Accuracy.High,
          timeInterval: 5000, // Actualizar cada 5 segundos
          distanceInterval: 10, // Actualizar cada 10 metros
        },
        (location) => {
          const formattedLocation = {
            latitude: location.coords.latitude,
            longitude: location.coords.longitude,
            accuracy: location.coords.accuracy,
            altitude: location.coords.altitude,
            heading: location.coords.heading,
            speed: location.coords.speed,
            timestamp: location.timestamp,
          };

          this.currentLocation = formattedLocation;
          this.locationCallback?.(formattedLocation);
          
          console.log('📍 Ubicación actualizada:', formattedLocation);
        }
      );

      this.isWatching = true;
      console.log('🎯 Iniciado monitoreo de ubicación');
      return true;
    } catch (error) {
      console.error('❌ Error iniciando monitoreo de ubicación:', error);
      return false;
    }
  }

  stopWatching() {
    if (this.watchSubscription) {
      this.watchSubscription.remove();
      this.watchSubscription = null;
    }
    
    this.isWatching = false;
    this.locationCallback = null;
    console.log('🛑 Detenido monitoreo de ubicación');
  }

  // Calcular distancia entre dos puntos (fórmula de Haversine)
  calculateDistance(lat1, lon1, lat2, lon2) {
    const R = 6371; // Radio de la Tierra en kilómetros
    const dLat = this.toRadians(lat2 - lat1);
    const dLon = this.toRadians(lon2 - lon1);
    
    const a =
      Math.sin(dLat / 2) * Math.sin(dLat / 2) +
      Math.cos(this.toRadians(lat1)) *
        Math.cos(this.toRadians(lat2)) *
        Math.sin(dLon / 2) *
        Math.sin(dLon / 2);
    
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
    const distance = R * c;
    
    return Math.round(distance * 100) / 100; // Redondear a 2 decimales
  }

  toRadians(degrees) {
    return degrees * (Math.PI / 180);
  }

  // Calcular tiempo estimado de viaje (aproximado)
  calculateEstimatedTime(distanceKm, averageSpeedKmh = 25) {
    const timeHours = distanceKm / averageSpeedKmh;
    const timeMinutes = Math.ceil(timeHours * 60);
    return Math.max(timeMinutes, 5); // Mínimo 5 minutos
  }

  // Verificar si una ubicación está dentro de un radio
  isWithinRadius(centerLat, centerLon, pointLat, pointLon, radiusKm) {
    const distance = this.calculateDistance(centerLat, centerLon, pointLat, pointLon);
    return distance <= radiusKm;
  }

  // Formatear coordenadas para mostrar
  formatCoordinates(location) {
    return `${location.latitude.toFixed(6)}, ${location.longitude.toFixed(6)}`;
  }

  // Obtener dirección aproximada usando Expo Location
  async getAddressFromCoordinates(latitude, longitude) {
    try {
      const addresses = await Location.reverseGeocodeAsync({
        latitude,
        longitude,
      });

      if (addresses && addresses.length > 0) {
        const address = addresses[0];
        const formattedAddress = [
          address.street,
          address.streetNumber,
          address.district,
          address.city,
        ].filter(Boolean).join(', ');
        
        return formattedAddress || `${latitude.toFixed(4)}, ${longitude.toFixed(4)}`;
      }
      
      return `${latitude.toFixed(4)}, ${longitude.toFixed(4)}`;
    } catch (error) {
      console.error('Error obteniendo dirección:', error);
      return 'Ubicación desconocida';
    }
  }

  // Getters
  get current() {
    return this.currentLocation;
  }

  get watching() {
    return this.isWatching;
  }

  // Limpiar recursos
  cleanup() {
    this.stopWatching();
    this.currentLocation = null;
    this.locationCallback = null;
    console.log('🧹 Servicio de ubicación limpiado');
  }
}

export const LocationService = new LocationServiceClass();
