/**
 * Pantalla de Ganancias del Conductor - MACLAREN Expo
 */

import React, { useState } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  ScrollView,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import * as Animatable from 'react-native-animatable';
import { StatusBar } from 'expo-status-bar';

const EarningsScreen = () => {
  const [selectedPeriod, setSelectedPeriod] = useState('day');
  
  const earningsData = {
    today: 125000,
    week: 850000,
    month: 3200000,
    tripsToday: 8,
    tripsWeek: 52,
    tripsMonth: 198,
  };

  const getCurrentPeriodData = () => {
    switch (selectedPeriod) {
      case 'day':
        return {
          earnings: earningsData.today,
          trips: earningsData.tripsToday,
          label: 'Hoy',
        };
      case 'week':
        return {
          earnings: earningsData.week,
          trips: earningsData.tripsWeek,
          label: 'Esta Semana',
        };
      case 'month':
        return {
          earnings: earningsData.month,
          trips: earningsData.tripsMonth,
          label: 'Este Mes',
        };
      default:
        return {
          earnings: earningsData.today,
          trips: earningsData.tripsToday,
          label: 'Hoy',
        };
    }
  };

  const currentData = getCurrentPeriodData();

  return (
    <View style={styles.container}>
      <StatusBar style="light" />
      
      {/* Header */}
      <LinearGradient
        colors={['#ff6b35', '#f7931e']}
        style={styles.header}>
        
        <Animatable.View 
          animation="fadeInDown"
          duration={800}
          style={styles.headerContent}>
          
          <Text style={styles.headerTitle}>💰 Mis Ganancias</Text>
          <Text style={styles.headerSubtitle}>Resumen de ingresos</Text>
        </Animatable.View>
      </LinearGradient>

      <ScrollView style={styles.content}>
        
        {/* Selector de período */}
        <Animatable.View 
          animation="fadeInUp"
          duration={800}
          delay={200}
          style={styles.periodSelector}>
          
          <TouchableOpacity
            style={[
              styles.periodButton,
              selectedPeriod === 'day' && styles.periodButtonActive
            ]}
            onPress={() => setSelectedPeriod('day')}>
            <Text style={[
              styles.periodText,
              selectedPeriod === 'day' && styles.periodTextActive
            ]}>
              Hoy
            </Text>
          </TouchableOpacity>
          
          <TouchableOpacity
            style={[
              styles.periodButton,
              selectedPeriod === 'week' && styles.periodButtonActive
            ]}
            onPress={() => setSelectedPeriod('week')}>
            <Text style={[
              styles.periodText,
              selectedPeriod === 'week' && styles.periodTextActive
            ]}>
              Semana
            </Text>
          </TouchableOpacity>
          
          <TouchableOpacity
            style={[
              styles.periodButton,
              selectedPeriod === 'month' && styles.periodButtonActive
            ]}
            onPress={() => setSelectedPeriod('month')}>
            <Text style={[
              styles.periodText,
              selectedPeriod === 'month' && styles.periodTextActive
            ]}>
              Mes
            </Text>
          </TouchableOpacity>
        </Animatable.View>

        {/* Resumen principal */}
        <Animatable.View 
          animation="fadeInUp"
          duration={800}
          delay={400}
          style={styles.mainSummary}>
          
          <Text style={styles.summaryLabel}>{currentData.label}</Text>
          <Text style={styles.summaryAmount}>
            ${currentData.earnings.toLocaleString()}
          </Text>
          <Text style={styles.summaryTrips}>
            {currentData.trips} viajes completados
          </Text>
          
          <View style={styles.summaryStats}>
            <View style={styles.statItem}>
              <Text style={styles.statValue}>
                ${Math.round(currentData.earnings / currentData.trips).toLocaleString()}
              </Text>
              <Text style={styles.statLabel}>Promedio por viaje</Text>
            </View>
            <View style={styles.statDivider} />
            <View style={styles.statItem}>
              <Text style={styles.statValue}>⭐ 4.8</Text>
              <Text style={styles.statLabel}>Rating promedio</Text>
            </View>
          </View>
        </Animatable.View>

        {/* Estadísticas adicionales */}
        <Animatable.View 
          animation="fadeInUp"
          duration={800}
          delay={600}
          style={styles.additionalStats}>
          
          <Text style={styles.sectionTitle}>📈 Estadísticas</Text>
          
          <View style={styles.statsGrid}>
            <View style={styles.statsCard}>
              <Text style={styles.statsCardValue}>$15.6M</Text>
              <Text style={styles.statsCardLabel}>Total Ganado</Text>
            </View>
            
            <View style={styles.statsCard}>
              <Text style={styles.statsCardValue}>$107K</Text>
              <Text style={styles.statsCardLabel}>Promedio Diario</Text>
            </View>
            
            <View style={styles.statsCard}>
              <Text style={styles.statsCardValue}>$14K</Text>
              <Text style={styles.statsCardLabel}>Por Hora</Text>
            </View>
            
            <View style={styles.statsCard}>
              <Text style={styles.statsCardValue}>7</Text>
              <Text style={styles.statsCardLabel}>Viajes/Día</Text>
            </View>
          </View>
        </Animatable.View>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  header: {
    paddingTop: 50,
    paddingBottom: 20,
    paddingHorizontal: 20,
    alignItems: 'center',
  },
  headerContent: {
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: 'white',
    marginBottom: 5,
  },
  headerSubtitle: {
    fontSize: 16,
    color: 'rgba(255, 255, 255, 0.8)',
  },
  content: {
    flex: 1,
    paddingHorizontal: 20,
  },
  periodSelector: {
    flexDirection: 'row',
    backgroundColor: 'white',
    borderRadius: 15,
    padding: 5,
    marginTop: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  periodButton: {
    flex: 1,
    paddingVertical: 12,
    alignItems: 'center',
    borderRadius: 10,
  },
  periodButtonActive: {
    backgroundColor: '#ff6b35',
  },
  periodText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#666',
  },
  periodTextActive: {
    color: 'white',
  },
  mainSummary: {
    backgroundColor: 'white',
    borderRadius: 20,
    padding: 25,
    marginTop: 20,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.15,
    shadowRadius: 8,
    elevation: 5,
  },
  summaryLabel: {
    fontSize: 16,
    color: '#666',
    marginBottom: 10,
  },
  summaryAmount: {
    fontSize: 36,
    fontWeight: 'bold',
    color: '#2ecc71',
    marginBottom: 5,
  },
  summaryTrips: {
    fontSize: 16,
    color: '#666',
    marginBottom: 20,
  },
  summaryStats: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#f8f9fa',
    borderRadius: 15,
    paddingVertical: 15,
    paddingHorizontal: 20,
    width: '100%',
  },
  statItem: {
    alignItems: 'center',
    flex: 1,
  },
  statValue: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 2,
  },
  statLabel: {
    fontSize: 12,
    color: '#666',
    textAlign: 'center',
  },
  statDivider: {
    width: 1,
    height: 30,
    backgroundColor: '#ddd',
    marginHorizontal: 15,
  },
  additionalStats: {
    backgroundColor: 'white',
    borderRadius: 15,
    padding: 20,
    marginTop: 20,
    marginBottom: 30,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 15,
  },
  statsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  statsCard: {
    width: '48%',
    backgroundColor: '#f8f9fa',
    borderRadius: 12,
    padding: 15,
    alignItems: 'center',
    marginBottom: 15,
  },
  statsCardValue: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 5,
  },
  statsCardLabel: {
    fontSize: 12,
    color: '#666',
    textAlign: 'center',
  },
});

export default EarningsScreen;
