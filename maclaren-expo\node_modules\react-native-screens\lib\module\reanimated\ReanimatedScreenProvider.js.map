{"version": 3, "names": ["React", "ScreenContext", "ReanimatedNativeStackScreen", "AnimatedScreen", "ReanimatedScreenWrapper", "Component", "constructor", "arguments", "_defineProperty", "ref", "_this$props$onCompone", "_this$props", "props", "onComponentRef", "call", "setNativeProps", "_this$ref", "render", "ReanimatedScreen", "isNativeStack", "createElement", "_extends", "setRef", "ReanimatedScreenProvider", "Provider", "value", "children"], "sources": ["ReanimatedScreenProvider.tsx"], "sourcesContent": ["import React, { PropsWithChildren } from 'react';\nimport { View } from 'react-native';\nimport { ScreenProps, ScreenContext } from 'react-native-screens';\nimport ReanimatedNativeStackScreen from './ReanimatedNativeStackScreen';\nimport AnimatedScreen from './ReanimatedScreen';\n\nclass ReanimatedScreenWrapper extends React.Component<ScreenProps> {\n  private ref: React.ElementRef<typeof View> | null = null;\n\n  setNativeProps(props: ScreenProps): void {\n    this.ref?.setNativeProps(props);\n  }\n\n  setRef = (ref: React.ElementRef<typeof View> | null): void => {\n    this.ref = ref;\n    this.props.onComponentRef?.(ref);\n  };\n\n  render() {\n    const ReanimatedScreen = this.props.isNativeStack\n      ? ReanimatedNativeStackScreen\n      : AnimatedScreen;\n    return (\n      <ReanimatedScreen\n        {...this.props}\n        // @ts-ignore some problems with ref\n        ref={this.setRef}\n      />\n    );\n  }\n}\n\nexport default function ReanimatedScreenProvider(\n  props: PropsWithChildren<unknown>\n) {\n  return (\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    <ScreenContext.Provider value={ReanimatedScreenWrapper as any}>\n      {props.children}\n    </ScreenContext.Provider>\n  );\n}\n"], "mappings": ";;;;AAAA,OAAOA,KAAK,MAA6B,OAAO;AAEhD,SAAsBC,aAAa,QAAQ,sBAAsB;AACjE,OAAOC,2BAA2B,MAAM,+BAA+B;AACvE,OAAOC,cAAc,MAAM,oBAAoB;AAE/C,MAAMC,uBAAuB,SAASJ,KAAK,CAACK,SAAS,CAAc;EAAAC,YAAA;IAAA,SAAAC,SAAA;IAAAC,eAAA,cACb,IAAI;IAAAA,eAAA,iBAM9CC,GAAyC,IAAW;MAAA,IAAAC,qBAAA,EAAAC,WAAA;MAC5D,IAAI,CAACF,GAAG,GAAGA,GAAG;MACd,CAAAC,qBAAA,IAAAC,WAAA,OAAI,CAACC,KAAK,EAACC,cAAc,cAAAH,qBAAA,eAAzBA,qBAAA,CAAAI,IAAA,CAAAH,WAAA,EAA4BF,GAAG,CAAC;IAClC,CAAC;EAAA;EAPDM,cAAcA,CAACH,KAAkB,EAAQ;IAAA,IAAAI,SAAA;IACvC,CAAAA,SAAA,OAAI,CAACP,GAAG,cAAAO,SAAA,eAARA,SAAA,CAAUD,cAAc,CAACH,KAAK,CAAC;EACjC;EAOAK,MAAMA,CAAA,EAAG;IACP,MAAMC,gBAAgB,GAAG,IAAI,CAACN,KAAK,CAACO,aAAa,GAC7CjB,2BAA2B,GAC3BC,cAAc;IAClB,oBACEH,KAAA,CAAAoB,aAAA,CAACF,gBAAgB,EAAAG,QAAA,KACX,IAAI,CAACT,KAAK;MACd;MACAH,GAAG,EAAE,IAAI,CAACa;IAAO,EAClB,CAAC;EAEN;AACF;AAEA,eAAe,SAASC,wBAAwBA,CAC9CX,KAAiC,EACjC;EACA;IAAA;IACE;IACAZ,KAAA,CAAAoB,aAAA,CAACnB,aAAa,CAACuB,QAAQ;MAACC,KAAK,EAAErB;IAA+B,GAC3DQ,KAAK,CAACc,QACe;EAAC;AAE7B"}