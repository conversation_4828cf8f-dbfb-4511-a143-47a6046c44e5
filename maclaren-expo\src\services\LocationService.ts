/**
 * Servicio de Geolocalización para MACLAREN
 * Maneja GPS, ubicación en tiempo real y permisos
 */

import Geolocation from 'react-native-geolocation-service';
import {Platform, PermissionsAndroid, Alert} from 'react-native';

export interface Location {
  latitude: number;
  longitude: number;
  accuracy?: number;
  altitude?: number;
  heading?: number;
  speed?: number;
  timestamp?: number;
}

class LocationServiceClass {
  private watchId: number | null = null;
  private currentLocation: Location | null = null;
  private isWatching = false;
  private locationCallback?: (location: Location) => void;

  async initialize(): Promise<boolean> {
    try {
      const hasPermission = await this.requestLocationPermission();
      if (!hasPermission) {
        throw new Error('Permisos de ubicación denegados');
      }
      
      console.log('✅ Servicio de ubicación inicializado');
      return true;
    } catch (error) {
      console.error('❌ Error inicializando servicio de ubicación:', error);
      return false;
    }
  }

  private async requestLocationPermission(): Promise<boolean> {
    if (Platform.OS === 'ios') {
      // En iOS, los permisos se manejan automáticamente
      return true;
    }

    try {
      const granted = await PermissionsAndroid.request(
        PermissionsAndroid.PERMISSIONS.ACCESS_FINE_LOCATION,
        {
          title: 'Permiso de Ubicación',
          message: 'MACLAREN necesita acceso a tu ubicación para encontrar conductores cercanos y mostrar tu posición en tiempo real.',
          buttonNeutral: 'Preguntar después',
          buttonNegative: 'Cancelar',
          buttonPositive: 'Permitir',
        },
      );

      if (granted === PermissionsAndroid.RESULTS.GRANTED) {
        console.log('✅ Permiso de ubicación concedido');
        return true;
      } else {
        console.log('❌ Permiso de ubicación denegado');
        Alert.alert(
          'Permiso Requerido',
          'MACLAREN necesita acceso a tu ubicación para funcionar correctamente. Por favor activa los permisos en configuración.',
        );
        return false;
      }
    } catch (error) {
      console.error('Error solicitando permisos:', error);
      return false;
    }
  }

  async getCurrentPosition(): Promise<Location> {
    return new Promise((resolve, reject) => {
      Geolocation.getCurrentPosition(
        (position) => {
          const location: Location = {
            latitude: position.coords.latitude,
            longitude: position.coords.longitude,
            accuracy: position.coords.accuracy,
            altitude: position.coords.altitude || undefined,
            heading: position.coords.heading || undefined,
            speed: position.coords.speed || undefined,
            timestamp: position.timestamp,
          };

          this.currentLocation = location;
          console.log('📍 Ubicación actual obtenida:', location);
          resolve(location);
        },
        (error) => {
          console.error('❌ Error obteniendo ubicación:', error);
          
          // Manejar diferentes tipos de errores
          let errorMessage = 'No se pudo obtener tu ubicación';
          
          switch (error.code) {
            case 1: // PERMISSION_DENIED
              errorMessage = 'Permisos de ubicación denegados. Actívalos en configuración.';
              break;
            case 2: // POSITION_UNAVAILABLE
              errorMessage = 'Ubicación no disponible. Verifica que el GPS esté activado.';
              break;
            case 3: // TIMEOUT
              errorMessage = 'Tiempo de espera agotado. Intenta de nuevo.';
              break;
          }

          reject(new Error(errorMessage));
        },
        {
          enableHighAccuracy: true,
          timeout: 15000,
          maximumAge: 10000,
        },
      );
    });
  }

  startWatching(callback: (location: Location) => void): boolean {
    if (this.isWatching) {
      console.log('⚠️ Ya se está monitoreando la ubicación');
      return false;
    }

    this.locationCallback = callback;

    this.watchId = Geolocation.watchPosition(
      (position) => {
        const location: Location = {
          latitude: position.coords.latitude,
          longitude: position.coords.longitude,
          accuracy: position.coords.accuracy,
          altitude: position.coords.altitude || undefined,
          heading: position.coords.heading || undefined,
          speed: position.coords.speed || undefined,
          timestamp: position.timestamp,
        };

        this.currentLocation = location;
        this.locationCallback?.(location);
        
        console.log('📍 Ubicación actualizada:', location);
      },
      (error) => {
        console.error('❌ Error monitoreando ubicación:', error);
        
        // Intentar obtener ubicación una vez más
        this.getCurrentPosition()
          .then((location) => {
            this.locationCallback?.(location);
          })
          .catch((err) => {
            console.error('❌ Error en fallback de ubicación:', err);
          });
      },
      {
        enableHighAccuracy: true,
        distanceFilter: 10, // Actualizar cada 10 metros
        interval: 5000, // Actualizar cada 5 segundos
        fastestInterval: 2000, // Mínimo 2 segundos entre actualizaciones
      },
    );

    this.isWatching = true;
    console.log('🎯 Iniciado monitoreo de ubicación');
    return true;
  }

  stopWatching(): void {
    if (this.watchId !== null) {
      Geolocation.clearWatch(this.watchId);
      this.watchId = null;
    }
    
    this.isWatching = false;
    this.locationCallback = undefined;
    console.log('🛑 Detenido monitoreo de ubicación');
  }

  // Calcular distancia entre dos puntos (fórmula de Haversine)
  calculateDistance(
    lat1: number,
    lon1: number,
    lat2: number,
    lon2: number,
  ): number {
    const R = 6371; // Radio de la Tierra en kilómetros
    const dLat = this.toRadians(lat2 - lat1);
    const dLon = this.toRadians(lon2 - lon1);
    
    const a =
      Math.sin(dLat / 2) * Math.sin(dLat / 2) +
      Math.cos(this.toRadians(lat1)) *
        Math.cos(this.toRadians(lat2)) *
        Math.sin(dLon / 2) *
        Math.sin(dLon / 2);
    
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
    const distance = R * c;
    
    return Math.round(distance * 100) / 100; // Redondear a 2 decimales
  }

  private toRadians(degrees: number): number {
    return degrees * (Math.PI / 180);
  }

  // Calcular tiempo estimado de viaje (aproximado)
  calculateEstimatedTime(distanceKm: number, averageSpeedKmh: number = 25): number {
    const timeHours = distanceKm / averageSpeedKmh;
    const timeMinutes = Math.ceil(timeHours * 60);
    return Math.max(timeMinutes, 5); // Mínimo 5 minutos
  }

  // Verificar si una ubicación está dentro de un radio
  isWithinRadius(
    centerLat: number,
    centerLon: number,
    pointLat: number,
    pointLon: number,
    radiusKm: number,
  ): boolean {
    const distance = this.calculateDistance(centerLat, centerLon, pointLat, pointLon);
    return distance <= radiusKm;
  }

  // Formatear coordenadas para mostrar
  formatCoordinates(location: Location): string {
    return `${location.latitude.toFixed(6)}, ${location.longitude.toFixed(6)}`;
  }

  // Obtener dirección aproximada (simulada - en producción usar geocoding)
  async getAddressFromCoordinates(latitude: number, longitude: number): Promise<string> {
    try {
      // En producción, usar un servicio de geocoding como Google Maps API
      // Por ahora, retornar coordenadas formateadas
      return `${latitude.toFixed(4)}, ${longitude.toFixed(4)}`;
    } catch (error) {
      console.error('Error obteniendo dirección:', error);
      return 'Ubicación desconocida';
    }
  }

  // Getters
  get current(): Location | null {
    return this.currentLocation;
  }

  get watching(): boolean {
    return this.isWatching;
  }

  // Limpiar recursos
  cleanup(): void {
    this.stopWatching();
    this.currentLocation = null;
    this.locationCallback = undefined;
    console.log('🧹 Servicio de ubicación limpiado');
  }
}

export const LocationService = new LocationServiceClass();
