/**
 * Pantalla Principal del Conductor - MACLAREN Expo
 */

import React, { useState } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  ScrollView,
  Switch,
  Alert,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import * as Animatable from 'react-native-animatable';
import { StatusBar } from 'expo-status-bar';

const DriverHomeScreen = () => {
  const [isOnline, setIsOnline] = useState(false);
  const [todayEarnings, setTodayEarnings] = useState(125000);
  const [todayTrips, setTodayTrips] = useState(8);

  const toggleOnlineStatus = () => {
    const newStatus = !isOnline;
    setIsOnline(newStatus);
    
    if (newStatus) {
      Alert.alert('¡Conectado!', 'Ahora puedes recibir solicitudes de viaje');
    } else {
      Alert.alert('Desconectado', 'Ya no recibirás solicitudes de viaje');
    }
  };

  return (
    <View style={styles.container}>
      <StatusBar style="light" />
      
      {/* Header */}
      <LinearGradient
        colors={['#ff6b35', '#f7931e']}
        style={styles.header}>
        
        <View style={styles.headerContent}>
          <View style={styles.statusContainer}>
            <View style={[styles.statusIndicator, {backgroundColor: isOnline ? '#2ecc71' : '#e74c3c'}]} />
            <Text style={styles.statusText}>
              {isOnline ? 'En línea' : 'Fuera de línea'}
            </Text>
            <Switch
              value={isOnline}
              onValueChange={toggleOnlineStatus}
              trackColor={{false: '#ccc', true: '#2ecc71'}}
              thumbColor={isOnline ? '#fff' : '#f4f3f4'}
            />
          </View>
          
          <Text style={styles.locationText}>📍 Bogotá, Colombia</Text>
        </View>

        {/* Estadísticas */}
        <View style={styles.statsContainer}>
          <View style={styles.statItem}>
            <Text style={styles.statValue}>${todayEarnings.toLocaleString()}</Text>
            <Text style={styles.statLabel}>Ganancias hoy</Text>
          </View>
          <View style={styles.statItem}>
            <Text style={styles.statValue}>{todayTrips}</Text>
            <Text style={styles.statLabel}>Viajes</Text>
          </View>
          <View style={styles.statItem}>
            <Text style={styles.statValue}>⭐ 4.8</Text>
            <Text style={styles.statLabel}>Rating</Text>
          </View>
        </View>
      </LinearGradient>

      {/* Contenido principal */}
      <ScrollView style={styles.content}>
        
        {!isOnline ? (
          <Animatable.View 
            animation="fadeIn"
            style={styles.offlineContainer}>
            <Text style={styles.offlineIcon}>🏍️</Text>
            <Text style={styles.offlineTitle}>Fuera de línea</Text>
            <Text style={styles.offlineText}>Actívate para recibir solicitudes de viaje</Text>
          </Animatable.View>
        ) : (
          <Animatable.View 
            animation="fadeIn"
            style={styles.onlineContainer}>
            <Text style={styles.onlineIcon}>⏳</Text>
            <Text style={styles.onlineTitle}>Esperando solicitudes...</Text>
            <Text style={styles.onlineText}>Mantente atento a nuevas oportunidades</Text>
            
            {/* Simulación de solicitud */}
            <TouchableOpacity 
              style={styles.demoButton}
              onPress={() => Alert.alert('Demo', 'En la versión completa aquí aparecerían las solicitudes reales')}>
              <Text style={styles.demoButtonText}>🎮 Ver Demo de Solicitud</Text>
            </TouchableOpacity>
          </Animatable.View>
        )}

        {/* Consejos */}
        <Animatable.View 
          animation="fadeInUp"
          duration={800}
          delay={400}
          style={styles.tipsSection}>
          
          <Text style={styles.sectionTitle}>💡 Consejos para Ganar Más</Text>
          
          <View style={styles.tipCard}>
            <Text style={styles.tipIcon}>🕐</Text>
            <Text style={styles.tipText}>Trabaja en horarios pico (7-9 AM, 5-8 PM)</Text>
          </View>
          
          <View style={styles.tipCard}>
            <Text style={styles.tipIcon}>📍</Text>
            <Text style={styles.tipText}>Mantente cerca de centros comerciales</Text>
          </View>
          
          <View style={styles.tipCard}>
            <Text style={styles.tipIcon}>⭐</Text>
            <Text style={styles.tipText}>Un rating alto te da más viajes</Text>
          </View>
        </Animatable.View>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  header: {
    paddingTop: 50,
    paddingBottom: 20,
    paddingHorizontal: 20,
  },
  headerContent: {
    marginBottom: 20,
  },
  statusContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 10,
  },
  statusIndicator: {
    width: 12,
    height: 12,
    borderRadius: 6,
    marginRight: 8,
  },
  statusText: {
    color: 'white',
    fontSize: 18,
    fontWeight: 'bold',
    flex: 1,
  },
  locationText: {
    color: 'rgba(255, 255, 255, 0.9)',
    fontSize: 14,
  },
  statsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    borderRadius: 15,
    padding: 15,
  },
  statItem: {
    alignItems: 'center',
  },
  statValue: {
    color: 'white',
    fontSize: 18,
    fontWeight: 'bold',
  },
  statLabel: {
    color: 'rgba(255, 255, 255, 0.8)',
    fontSize: 12,
    marginTop: 2,
  },
  content: {
    flex: 1,
    paddingHorizontal: 20,
  },
  offlineContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 60,
  },
  offlineIcon: {
    fontSize: 60,
    marginBottom: 20,
  },
  offlineTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 10,
  },
  offlineText: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
  },
  onlineContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 40,
  },
  onlineIcon: {
    fontSize: 60,
    marginBottom: 20,
  },
  onlineTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 10,
  },
  onlineText: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    marginBottom: 20,
  },
  demoButton: {
    backgroundColor: '#3498db',
    borderRadius: 12,
    paddingVertical: 12,
    paddingHorizontal: 25,
  },
  demoButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
  },
  tipsSection: {
    backgroundColor: 'white',
    borderRadius: 15,
    padding: 20,
    marginTop: 20,
    marginBottom: 30,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 15,
  },
  tipCard: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#f8f9fa',
    borderRadius: 12,
    padding: 15,
    marginBottom: 10,
  },
  tipIcon: {
    fontSize: 24,
    marginRight: 15,
  },
  tipText: {
    fontSize: 14,
    color: '#666',
    flex: 1,
  },
});

export default DriverHomeScreen;
