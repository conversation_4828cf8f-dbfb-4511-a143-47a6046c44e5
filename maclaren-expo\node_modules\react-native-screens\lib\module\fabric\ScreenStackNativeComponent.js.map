{"version": 3, "names": ["codegenNativeComponent"], "sources": ["ScreenStackNativeComponent.ts"], "sourcesContent": ["import codegenNativeComponent from 'react-native/Libraries/Utilities/codegenNativeComponent';\nimport type { ViewProps } from 'react-native';\nimport type { DirectEventHandler } from 'react-native/Libraries/Types/CodegenTypes';\n\n// eslint-disable-next-line @typescript-eslint/ban-types\ntype FinishTransitioningEvent = Readonly<{}>;\n\ninterface NativeProps extends ViewProps {\n  onFinishTransitioning?: DirectEventHandler<FinishTransitioningEvent>;\n}\n\nexport default codegenNativeComponent<NativeProps>('RNSScreenStack', {});\n"], "mappings": "AAAA,OAAOA,sBAAsB,MAAM,yDAAyD;;AAI5F;;AAOA,eAAeA,sBAAsB,CAAc,gBAAgB,EAAE,CAAC,CAAC,CAAC"}