/**
 * Pantalla de Historial de Viajes - MACLAREN
 * Historial y detalles de viajes del pasajero
 */

import React, {useState, useEffect} from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  FlatList,
  RefreshControl,
  StatusBar,
  Modal,
} from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import * as Animatable from 'react-native-animatable';

interface Trip {
  id: string;
  driver: {
    name: string;
    rating: number;
    vehicle: string;
    licensePlate: string;
  };
  pickup: {
    address: string;
    coordinates: [number, number];
  };
  destination: {
    address: string;
    coordinates: [number, number];
  };
  fare: number;
  distance: string;
  duration: string;
  status: 'completed' | 'cancelled' | 'in_progress';
  date: string;
  rating?: number;
  paymentMethod: string;
}

const TripsScreen: React.FC = () => {
  const [trips, setTrips] = useState<Trip[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [selectedTrip, setSelectedTrip] = useState<Trip | null>(null);
  const [showTripModal, setShowTripModal] = useState(false);
  const [filter, setFilter] = useState<'all' | 'completed' | 'cancelled'>('all');

  useEffect(() => {
    loadTrips();
  }, []);

  const loadTrips = async () => {
    setIsLoading(true);
    
    try {
      // Simular carga de viajes desde API
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      const demoTrips: Trip[] = [
        {
          id: '1',
          driver: {
            name: 'Carlos Rodríguez',
            rating: 4.8,
            vehicle: 'Yamaha FZ 150',
            licensePlate: 'ABC123',
          },
          pickup: {
            address: 'Centro Comercial Santafé',
            coordinates: [4.6097, -74.0817],
          },
          destination: {
            address: 'Universidad Nacional',
            coordinates: [4.6367, -74.0834],
          },
          fare: 8500,
          distance: '3.2 km',
          duration: '12 min',
          status: 'completed',
          date: new Date(Date.now() - 86400000).toISOString(),
          rating: 5,
          paymentMethod: 'Efectivo',
        },
        {
          id: '2',
          driver: {
            name: 'María González',
            rating: 4.9,
            vehicle: 'Honda CB 125',
            licensePlate: 'DEF456',
          },
          pickup: {
            address: 'Zona Rosa',
            coordinates: [4.6629, -74.0645],
          },
          destination: {
            address: 'Aeropuerto El Dorado',
            coordinates: [4.7016, -74.1469],
          },
          fare: 25000,
          distance: '15.8 km',
          duration: '35 min',
          status: 'completed',
          date: new Date(Date.now() - 172800000).toISOString(),
          rating: 4,
          paymentMethod: 'Tarjeta',
        },
        {
          id: '3',
          driver: {
            name: 'Luis Martínez',
            rating: 4.6,
            vehicle: 'Suzuki GN 125',
            licensePlate: 'GHI789',
          },
          pickup: {
            address: 'Plaza de Bolívar',
            coordinates: [4.5981, -74.0758],
          },
          destination: {
            address: 'Chapinero',
            coordinates: [4.6533, -74.0633],
          },
          fare: 12000,
          distance: '6.5 km',
          duration: '18 min',
          status: 'cancelled',
          date: new Date(Date.now() - 259200000).toISOString(),
          paymentMethod: 'Efectivo',
        },
      ];
      
      setTrips(demoTrips);
    } catch (error) {
      console.error('Error cargando viajes:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const filteredTrips = trips.filter(trip => {
    if (filter === 'all') return true;
    return trip.status === filter;
  });

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    const today = new Date();
    const yesterday = new Date(today);
    yesterday.setDate(yesterday.getDate() - 1);

    if (date.toDateString() === today.toDateString()) {
      return 'Hoy';
    } else if (date.toDateString() === yesterday.toDateString()) {
      return 'Ayer';
    } else {
      return date.toLocaleDateString('es-CO', {
        day: 'numeric',
        month: 'short',
      });
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return '#2ecc71';
      case 'cancelled':
        return '#e74c3c';
      case 'in_progress':
        return '#f39c12';
      default:
        return '#95a5a6';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'completed':
        return 'Completado';
      case 'cancelled':
        return 'Cancelado';
      case 'in_progress':
        return 'En progreso';
      default:
        return 'Desconocido';
    }
  };

  const handleTripPress = (trip: Trip) => {
    setSelectedTrip(trip);
    setShowTripModal(true);
  };

  const renderTrip = ({item}: {item: Trip}) => (
    <Animatable.View
      animation="fadeInUp"
      duration={500}
      style={styles.tripCard}>
      
      <TouchableOpacity
        onPress={() => handleTripPress(item)}
        style={styles.tripContent}>
        
        <View style={styles.tripHeader}>
          <View style={styles.tripDate}>
            <Text style={styles.dateText}>{formatDate(item.date)}</Text>
            <View style={[
              styles.statusBadge,
              {backgroundColor: getStatusColor(item.status)}
            ]}>
              <Text style={styles.statusText}>{getStatusText(item.status)}</Text>
            </View>
          </View>
          <Text style={styles.fareText}>${item.fare.toLocaleString()}</Text>
        </View>

        <View style={styles.tripRoute}>
          <View style={styles.routeItem}>
            <Text style={styles.routeIcon}>📍</Text>
            <Text style={styles.routeText} numberOfLines={1}>
              {item.pickup.address}
            </Text>
          </View>
          <View style={styles.routeLine} />
          <View style={styles.routeItem}>
            <Text style={styles.routeIcon}>🎯</Text>
            <Text style={styles.routeText} numberOfLines={1}>
              {item.destination.address}
            </Text>
          </View>
        </View>

        <View style={styles.tripFooter}>
          <View style={styles.driverInfo}>
            <Text style={styles.driverName}>{item.driver.name}</Text>
            <Text style={styles.vehicleInfo}>
              {item.driver.vehicle} • {item.driver.licensePlate}
            </Text>
          </View>
          <View style={styles.tripStats}>
            <Text style={styles.statText}>{item.distance}</Text>
            <Text style={styles.statText}>⭐ {item.driver.rating}</Text>
          </View>
        </View>
      </TouchableOpacity>
    </Animatable.View>
  );

  return (
    <View style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor="#ff6b35" />
      
      {/* Header */}
      <LinearGradient
        colors={['#ff6b35', '#f7931e']}
        style={styles.header}>
        <Text style={styles.headerTitle}>Mis Viajes</Text>
        <Text style={styles.headerSubtitle}>
          {filteredTrips.length} viaje{filteredTrips.length !== 1 ? 's' : ''}
        </Text>
      </LinearGradient>

      {/* Filtros */}
      <View style={styles.filtersContainer}>
        <TouchableOpacity
          style={[styles.filterButton, filter === 'all' && styles.filterButtonActive]}
          onPress={() => setFilter('all')}>
          <Text style={[styles.filterText, filter === 'all' && styles.filterTextActive]}>
            Todos
          </Text>
        </TouchableOpacity>
        
        <TouchableOpacity
          style={[styles.filterButton, filter === 'completed' && styles.filterButtonActive]}
          onPress={() => setFilter('completed')}>
          <Text style={[styles.filterText, filter === 'completed' && styles.filterTextActive]}>
            Completados
          </Text>
        </TouchableOpacity>
        
        <TouchableOpacity
          style={[styles.filterButton, filter === 'cancelled' && styles.filterButtonActive]}
          onPress={() => setFilter('cancelled')}>
          <Text style={[styles.filterText, filter === 'cancelled' && styles.filterTextActive]}>
            Cancelados
          </Text>
        </TouchableOpacity>
      </View>

      {/* Lista de viajes */}
      <FlatList
        data={filteredTrips}
        keyExtractor={(item) => item.id}
        renderItem={renderTrip}
        style={styles.tripsList}
        contentContainerStyle={styles.tripsContainer}
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl
            refreshing={isLoading}
            onRefresh={loadTrips}
            colors={['#ff6b35']}
            tintColor="#ff6b35"
          />
        }
        ListEmptyComponent={
          <View style={styles.emptyContainer}>
            <Text style={styles.emptyIcon}>🏍️</Text>
            <Text style={styles.emptyTitle}>No hay viajes</Text>
            <Text style={styles.emptyText}>
              {filter === 'all' 
                ? 'Aún no has realizado ningún viaje'
                : `No tienes viajes ${filter === 'completed' ? 'completados' : 'cancelados'}`
              }
            </Text>
          </View>
        }
      />

      {/* Modal de detalles del viaje */}
      <Modal
        visible={showTripModal}
        animationType="slide"
        transparent={true}
        onRequestClose={() => setShowTripModal(false)}>
        
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            {selectedTrip && (
              <>
                <View style={styles.modalHeader}>
                  <Text style={styles.modalTitle}>Detalles del Viaje</Text>
                  <TouchableOpacity
                    style={styles.closeButton}
                    onPress={() => setShowTripModal(false)}>
                    <Text style={styles.closeButtonText}>✕</Text>
                  </TouchableOpacity>
                </View>

                <View style={styles.modalTripInfo}>
                  <Text style={styles.modalDate}>{formatDate(selectedTrip.date)}</Text>
                  <View style={[
                    styles.modalStatusBadge,
                    {backgroundColor: getStatusColor(selectedTrip.status)}
                  ]}>
                    <Text style={styles.modalStatusText}>
                      {getStatusText(selectedTrip.status)}
                    </Text>
                  </View>
                </View>

                <View style={styles.modalRoute}>
                  <View style={styles.modalRouteItem}>
                    <Text style={styles.modalRouteIcon}>📍</Text>
                    <Text style={styles.modalRouteText}>{selectedTrip.pickup.address}</Text>
                  </View>
                  <View style={styles.modalRouteLine} />
                  <View style={styles.modalRouteItem}>
                    <Text style={styles.modalRouteIcon}>🎯</Text>
                    <Text style={styles.modalRouteText}>{selectedTrip.destination.address}</Text>
                  </View>
                </View>

                <View style={styles.modalDetails}>
                  <View style={styles.modalDetailRow}>
                    <Text style={styles.modalDetailLabel}>Conductor:</Text>
                    <Text style={styles.modalDetailValue}>{selectedTrip.driver.name}</Text>
                  </View>
                  <View style={styles.modalDetailRow}>
                    <Text style={styles.modalDetailLabel}>Vehículo:</Text>
                    <Text style={styles.modalDetailValue}>
                      {selectedTrip.driver.vehicle} • {selectedTrip.driver.licensePlate}
                    </Text>
                  </View>
                  <View style={styles.modalDetailRow}>
                    <Text style={styles.modalDetailLabel}>Distancia:</Text>
                    <Text style={styles.modalDetailValue}>{selectedTrip.distance}</Text>
                  </View>
                  <View style={styles.modalDetailRow}>
                    <Text style={styles.modalDetailLabel}>Duración:</Text>
                    <Text style={styles.modalDetailValue}>{selectedTrip.duration}</Text>
                  </View>
                  <View style={styles.modalDetailRow}>
                    <Text style={styles.modalDetailLabel}>Pago:</Text>
                    <Text style={styles.modalDetailValue}>{selectedTrip.paymentMethod}</Text>
                  </View>
                  <View style={styles.modalDetailRow}>
                    <Text style={styles.modalDetailLabel}>Total:</Text>
                    <Text style={[styles.modalDetailValue, styles.modalFare]}>
                      ${selectedTrip.fare.toLocaleString()}
                    </Text>
                  </View>
                </View>

                {selectedTrip.status === 'completed' && (
                  <View style={styles.modalActions}>
                    <TouchableOpacity style={styles.rateButton}>
                      <Text style={styles.rateButtonText}>
                        ⭐ Calificar ({selectedTrip.rating || 'Sin calificar'})
                      </Text>
                    </TouchableOpacity>
                    
                    <TouchableOpacity style={styles.repeatButton}>
                      <Text style={styles.repeatButtonText}>🔄 Repetir Viaje</Text>
                    </TouchableOpacity>
                  </View>
                )}
              </>
            )}
          </View>
        </View>
      </Modal>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  header: {
    paddingTop: 50,
    paddingBottom: 20,
    paddingHorizontal: 20,
    alignItems: 'center',
  },
  headerTitle: {
    color: 'white',
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 5,
  },
  headerSubtitle: {
    color: 'rgba(255, 255, 255, 0.8)',
    fontSize: 16,
  },
  filtersContainer: {
    flexDirection: 'row',
    paddingHorizontal: 20,
    paddingVertical: 15,
    backgroundColor: 'white',
    borderBottomWidth: 1,
    borderBottomColor: '#e9ecef',
  },
  filterButton: {
    paddingHorizontal: 20,
    paddingVertical: 8,
    borderRadius: 20,
    marginRight: 10,
    backgroundColor: '#f8f9fa',
  },
  filterButtonActive: {
    backgroundColor: '#ff6b35',
  },
  filterText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#666',
  },
  filterTextActive: {
    color: 'white',
  },
  tripsList: {
    flex: 1,
  },
  tripsContainer: {
    padding: 20,
  },
  tripCard: {
    backgroundColor: 'white',
    borderRadius: 15,
    marginBottom: 15,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  tripContent: {
    padding: 20,
  },
  tripHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 15,
  },
  tripDate: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  dateText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    marginRight: 10,
  },
  statusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  statusText: {
    color: 'white',
    fontSize: 12,
    fontWeight: 'bold',
  },
  fareText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#2ecc71',
  },
  tripRoute: {
    marginBottom: 15,
  },
  routeItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 5,
  },
  routeIcon: {
    fontSize: 16,
    marginRight: 10,
    width: 20,
  },
  routeText: {
    fontSize: 14,
    color: '#333',
    flex: 1,
  },
  routeLine: {
    width: 2,
    height: 20,
    backgroundColor: '#ddd',
    marginLeft: 9,
    marginVertical: 2,
  },
  tripFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  driverInfo: {
    flex: 1,
  },
  driverName: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#333',
  },
  vehicleInfo: {
    fontSize: 12,
    color: '#666',
    marginTop: 2,
  },
  tripStats: {
    alignItems: 'flex-end',
  },
  statText: {
    fontSize: 12,
    color: '#666',
    marginBottom: 2,
  },
  emptyContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 60,
  },
  emptyIcon: {
    fontSize: 60,
    marginBottom: 20,
  },
  emptyTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 10,
  },
  emptyText: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    paddingHorizontal: 40,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    backgroundColor: 'white',
    borderRadius: 20,
    padding: 25,
    width: '90%',
    maxHeight: '80%',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20,
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
  },
  closeButton: {
    width: 30,
    height: 30,
    borderRadius: 15,
    backgroundColor: '#f8f9fa',
    justifyContent: 'center',
    alignItems: 'center',
  },
  closeButtonText: {
    fontSize: 16,
    color: '#666',
  },
  modalTripInfo: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20,
  },
  modalDate: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  modalStatusBadge: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 15,
  },
  modalStatusText: {
    color: 'white',
    fontSize: 14,
    fontWeight: 'bold',
  },
  modalRoute: {
    marginBottom: 25,
  },
  modalRouteItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  modalRouteIcon: {
    fontSize: 18,
    marginRight: 15,
    width: 25,
  },
  modalRouteText: {
    fontSize: 16,
    color: '#333',
    flex: 1,
  },
  modalRouteLine: {
    width: 3,
    height: 25,
    backgroundColor: '#ddd',
    marginLeft: 11,
    marginVertical: 5,
  },
  modalDetails: {
    marginBottom: 25,
  },
  modalDetailRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  modalDetailLabel: {
    fontSize: 14,
    color: '#666',
    fontWeight: '600',
  },
  modalDetailValue: {
    fontSize: 14,
    color: '#333',
    fontWeight: 'bold',
    textAlign: 'right',
    flex: 1,
    marginLeft: 10,
  },
  modalFare: {
    fontSize: 18,
    color: '#2ecc71',
  },
  modalActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  rateButton: {
    backgroundColor: '#f39c12',
    borderRadius: 12,
    paddingVertical: 12,
    paddingHorizontal: 20,
    flex: 1,
    marginRight: 10,
  },
  rateButtonText: {
    color: 'white',
    fontWeight: 'bold',
    textAlign: 'center',
  },
  repeatButton: {
    backgroundColor: '#ff6b35',
    borderRadius: 12,
    paddingVertical: 12,
    paddingHorizontal: 20,
    flex: 1,
  },
  repeatButtonText: {
    color: 'white',
    fontWeight: 'bold',
    textAlign: 'center',
  },
});

export default TripsScreen;
