{"version": 3, "names": ["React", "createContext", "undefined"], "sources": ["ReanimatedHeaderHeightContext.tsx"], "sourcesContent": ["import * as React from 'react';\n// @ts-ignore file to be used only if `react-native-reanimated` available in the project\nimport Animated from 'react-native-reanimated';\n\nexport default React.createContext<Animated.SharedValue<number> | undefined>(\n  undefined\n);\n"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B;;AAGA,4BAAeA,KAAK,CAACC,aAAa,CAChCC,SACF,CAAC"}