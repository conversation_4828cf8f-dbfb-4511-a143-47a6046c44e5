/**
 * MACLAREN - Aplicación de Transporte en Moto
 * App principal optimizada para Expo Go
 */

import React, { useEffect, useState } from 'react';
import { StatusBar } from 'expo-status-bar';
import { View, StyleSheet, Alert } from 'react-native';
import { NavigationContainer } from '@react-navigation/native';
import { createStackNavigator } from '@react-navigation/stack';
import * as SplashScreen from 'expo-splash-screen';
import * as Location from 'expo-location';
import * as Notifications from 'expo-notifications';
import AsyncStorage from '@react-native-async-storage/async-storage';

// Importar pantallas
import WelcomeScreen from './src/screens/WelcomeScreen';
import LoginScreen from './src/screens/LoginScreen';
import RegisterScreen from './src/screens/RegisterScreen';
import PassengerNavigator from './src/navigation/PassengerNavigator';
import DriverNavigator from './src/navigation/DriverNavigator';

// Importar servicios
import { SocketService } from './src/services/SocketService';
import { LocationService } from './src/services/LocationService';
import { NotificationService } from './src/services/NotificationService';

// Configurar notificaciones
Notifications.setNotificationHandler({
  handleNotification: async () => ({
    shouldShowAlert: true,
    shouldPlaySound: true,
    shouldSetBadge: false,
  }),
});

// Prevenir que el splash screen se oculte automáticamente
SplashScreen.preventAutoHideAsync();

const Stack = createStackNavigator();

export default function App() {
  const [isLoading, setIsLoading] = useState(true);
  const [userType, setUserType] = useState(null);
  const [isAuthenticated, setIsAuthenticated] = useState(false);

  useEffect(() => {
    initializeApp();
  }, []);

  const initializeApp = async () => {
    try {
      // Solicitar permisos necesarios
      await requestPermissions();

      // Inicializar servicios
      await initializeServices();

      // Verificar si el usuario ya está logueado
      await checkAuthStatus();

      // Ocultar splash screen
      await SplashScreen.hideAsync();
      
      setIsLoading(false);
    } catch (error) {
      console.error('Error inicializando app:', error);
      await SplashScreen.hideAsync();
      setIsLoading(false);
    }
  };

  const requestPermissions = async () => {
    try {
      // Permisos de ubicación
      const { status: locationStatus } = await Location.requestForegroundPermissionsAsync();
      if (locationStatus !== 'granted') {
        Alert.alert(
          'Permisos Requeridos',
          'MACLAREN necesita permisos de ubicación para funcionar correctamente.'
        );
      }

      // Permisos de notificaciones
      const { status: notificationStatus } = await Notifications.requestPermissionsAsync();
      if (notificationStatus !== 'granted') {
        Alert.alert(
          'Notificaciones',
          'Las notificaciones te ayudarán a recibir actualizaciones importantes.'
        );
      }

      console.log('✅ Permisos solicitados');
    } catch (error) {
      console.error('Error solicitando permisos:', error);
    }
  };

  const initializeServices = async () => {
    try {
      // Inicializar servicio de notificaciones
      await NotificationService.initialize();

      // Inicializar servicio de ubicación
      await LocationService.initialize();

      console.log('✅ Servicios inicializados correctamente');
    } catch (error) {
      console.error('❌ Error inicializando servicios:', error);
    }
  };

  const checkAuthStatus = async () => {
    try {
      const userData = await AsyncStorage.getItem('userData');
      const userTypeStored = await AsyncStorage.getItem('userType');

      if (userData && userTypeStored) {
        setIsAuthenticated(true);
        setUserType(userTypeStored);

        // Conectar socket con datos del usuario
        const user = JSON.parse(userData);
        SocketService.connect(user);
      }
    } catch (error) {
      console.error('Error verificando autenticación:', error);
    }
  };

  const handleLogin = async (userData, type) => {
    try {
      await AsyncStorage.setItem('userData', JSON.stringify(userData));
      await AsyncStorage.setItem('userType', type);

      setIsAuthenticated(true);
      setUserType(type);

      // Conectar socket
      SocketService.connect(userData);

      console.log(`✅ Usuario logueado como ${type}`);
    } catch (error) {
      console.error('Error guardando datos de usuario:', error);
    }
  };

  const handleLogout = async () => {
    try {
      await AsyncStorage.multiRemove(['userData', 'userType']);
      
      setIsAuthenticated(false);
      setUserType(null);

      // Desconectar socket
      SocketService.disconnect();

      console.log('✅ Usuario deslogueado');
    } catch (error) {
      console.error('Error cerrando sesión:', error);
    }
  };

  if (isLoading) {
    return (
      <View style={styles.loadingContainer}>
        <StatusBar style="light" backgroundColor="#ff6b35" />
      </View>
    );
  }

  return (
    <NavigationContainer>
      <StatusBar style="light" backgroundColor="#ff6b35" />
      <Stack.Navigator
        screenOptions={{
          headerShown: false,
          gestureEnabled: true,
          cardStyleInterpolator: ({ current, layouts }) => {
            return {
              cardStyle: {
                transform: [
                  {
                    translateX: current.progress.interpolate({
                      inputRange: [0, 1],
                      outputRange: [layouts.screen.width, 0],
                    }),
                  },
                ],
              },
            };
          },
        }}>
        
        {!isAuthenticated ? (
          // Pantallas de autenticación
          <>
            <Stack.Screen name="Welcome" component={WelcomeScreen} />
            <Stack.Screen name="Login">
              {props => <LoginScreen {...props} onLogin={handleLogin} />}
            </Stack.Screen>
            <Stack.Screen name="Register">
              {props => <RegisterScreen {...props} onRegister={handleLogin} />}
            </Stack.Screen>
          </>
        ) : (
          // Pantallas principales según tipo de usuario
          <>
            {userType === 'passenger' ? (
              <Stack.Screen name="PassengerApp">
                {props => <PassengerNavigator {...props} onLogout={handleLogout} />}
              </Stack.Screen>
            ) : (
              <Stack.Screen name="DriverApp">
                {props => <DriverNavigator {...props} onLogout={handleLogout} />}
              </Stack.Screen>
            )}
          </>
        )}
      </Stack.Navigator>
    </NavigationContainer>
  );
}

const styles = StyleSheet.create({
  loadingContainer: {
    flex: 1,
    backgroundColor: '#ff6b35',
    justifyContent: 'center',
    alignItems: 'center',
  },
});
