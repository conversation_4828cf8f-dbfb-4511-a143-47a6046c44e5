/**
 * MACLAREN - Aplicación de Transporte en Moto
 * App principal de React Native
 */

import React, {useEffect, useState} from 'react';
import {
  StatusBar,
  StyleSheet,
  View,
  Alert,
  PermissionsAndroid,
  Platform,
} from 'react-native';
import {NavigationContainer} from '@react-navigation/native';
import {createStackNavigator} from '@react-navigation/stack';
import AsyncStorage from '@react-native-async-storage/async-storage';
import SplashScreen from 'react-native-splash-screen';

// Importar pantallas
import WelcomeScreen from './src/screens/WelcomeScreen';
import LoginScreen from './src/screens/LoginScreen';
import RegisterScreen from './src/screens/RegisterScreen';
import PassengerNavigator from './src/navigation/PassengerNavigator';
import DriverNavigator from './src/navigation/DriverNavigator';

// Importar servicios
import {SocketService} from './src/services/SocketService';
import {LocationService} from './src/services/LocationService';
import {NotificationService} from './src/services/NotificationService';

// Tipos
type RootStackParamList = {
  Welcome: undefined;
  Login: undefined;
  Register: undefined;
  PassengerApp: undefined;
  DriverApp: undefined;
};

const Stack = createStackNavigator<RootStackParamList>();

const App: React.FC = () => {
  const [isLoading, setIsLoading] = useState(true);
  const [userType, setUserType] = useState<string | null>(null);
  const [isAuthenticated, setIsAuthenticated] = useState(false);

  useEffect(() => {
    initializeApp();
  }, []);

  const initializeApp = async () => {
    try {
      // Ocultar splash screen después de un momento
      setTimeout(() => {
        SplashScreen.hide();
      }, 2000);

      // Solicitar permisos necesarios
      await requestPermissions();

      // Inicializar servicios
      await initializeServices();

      // Verificar si el usuario ya está logueado
      await checkAuthStatus();

      setIsLoading(false);
    } catch (error) {
      console.error('Error inicializando app:', error);
      setIsLoading(false);
    }
  };

  const requestPermissions = async () => {
    if (Platform.OS === 'android') {
      try {
        const granted = await PermissionsAndroid.requestMultiple([
          PermissionsAndroid.PERMISSIONS.ACCESS_FINE_LOCATION,
          PermissionsAndroid.PERMISSIONS.ACCESS_COARSE_LOCATION,
          PermissionsAndroid.PERMISSIONS.CAMERA,
          PermissionsAndroid.PERMISSIONS.READ_EXTERNAL_STORAGE,
          PermissionsAndroid.PERMISSIONS.WRITE_EXTERNAL_STORAGE,
        ]);

        const allPermissionsGranted = Object.values(granted).every(
          permission => permission === PermissionsAndroid.RESULTS.GRANTED,
        );

        if (!allPermissionsGranted) {
          Alert.alert(
            'Permisos Requeridos',
            'MACLAREN necesita permisos de ubicación y cámara para funcionar correctamente.',
          );
        }
      } catch (error) {
        console.error('Error solicitando permisos:', error);
      }
    }
  };

  const initializeServices = async () => {
    try {
      // Inicializar servicio de notificaciones
      await NotificationService.initialize();

      // Inicializar servicio de ubicación
      await LocationService.initialize();

      console.log('✅ Servicios inicializados correctamente');
    } catch (error) {
      console.error('❌ Error inicializando servicios:', error);
    }
  };

  const checkAuthStatus = async () => {
    try {
      const userData = await AsyncStorage.getItem('userData');
      const userTypeStored = await AsyncStorage.getItem('userType');

      if (userData && userTypeStored) {
        setIsAuthenticated(true);
        setUserType(userTypeStored);

        // Conectar socket con datos del usuario
        const user = JSON.parse(userData);
        SocketService.connect(user);
      }
    } catch (error) {
      console.error('Error verificando autenticación:', error);
    }
  };

  const handleLogin = async (userData: any, type: string) => {
    try {
      await AsyncStorage.setItem('userData', JSON.stringify(userData));
      await AsyncStorage.setItem('userType', type);

      setIsAuthenticated(true);
      setUserType(type);

      // Conectar socket
      SocketService.connect(userData);

      console.log(`✅ Usuario logueado como ${type}`);
    } catch (error) {
      console.error('Error guardando datos de usuario:', error);
    }
  };

  const handleLogout = async () => {
    try {
      await AsyncStorage.multiRemove(['userData', 'userType']);
      
      setIsAuthenticated(false);
      setUserType(null);

      // Desconectar socket
      SocketService.disconnect();

      console.log('✅ Usuario deslogueado');
    } catch (error) {
      console.error('Error cerrando sesión:', error);
    }
  };

  if (isLoading) {
    return (
      <View style={styles.loadingContainer}>
        <StatusBar barStyle="light-content" backgroundColor="#ff6b35" />
      </View>
    );
  }

  return (
    <NavigationContainer>
      <StatusBar barStyle="light-content" backgroundColor="#ff6b35" />
      <Stack.Navigator
        screenOptions={{
          headerShown: false,
          gestureEnabled: true,
          cardStyleInterpolator: ({current, layouts}) => {
            return {
              cardStyle: {
                transform: [
                  {
                    translateX: current.progress.interpolate({
                      inputRange: [0, 1],
                      outputRange: [layouts.screen.width, 0],
                    }),
                  },
                ],
              },
            };
          },
        }}>
        
        {!isAuthenticated ? (
          // Pantallas de autenticación
          <>
            <Stack.Screen name="Welcome" component={WelcomeScreen} />
            <Stack.Screen name="Login">
              {props => <LoginScreen {...props} onLogin={handleLogin} />}
            </Stack.Screen>
            <Stack.Screen name="Register">
              {props => <RegisterScreen {...props} onRegister={handleLogin} />}
            </Stack.Screen>
          </>
        ) : (
          // Pantallas principales según tipo de usuario
          <>
            {userType === 'passenger' ? (
              <Stack.Screen name="PassengerApp">
                {props => <PassengerNavigator {...props} onLogout={handleLogout} />}
              </Stack.Screen>
            ) : (
              <Stack.Screen name="DriverApp">
                {props => <DriverNavigator {...props} onLogout={handleLogout} />}
              </Stack.Screen>
            )}
          </>
        )}
      </Stack.Navigator>
    </NavigationContainer>
  );
};

const styles = StyleSheet.create({
  loadingContainer: {
    flex: 1,
    backgroundColor: '#ff6b35',
    justifyContent: 'center',
    alignItems: 'center',
  },
});

export default App;
