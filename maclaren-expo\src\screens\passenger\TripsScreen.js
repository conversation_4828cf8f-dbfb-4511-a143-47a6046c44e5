/**
 * Pantalla de Historial de Viajes - MACLAREN Expo
 */

import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import * as Animatable from 'react-native-animatable';
import { StatusBar } from 'expo-status-bar';

const TripsScreen = () => {
  const demoTrips = [
    {
      id: '1',
      date: 'Hoy',
      from: 'Centro Comercial',
      to: 'Universidad',
      fare: 8500,
      status: 'completed',
    },
    {
      id: '2',
      date: 'Ayer',
      from: 'Casa',
      to: 'Aeropuerto',
      fare: 25000,
      status: 'completed',
    },
  ];

  return (
    <View style={styles.container}>
      <StatusBar style="light" />
      
      {/* Header */}
      <LinearGradient
        colors={['#ff6b35', '#f7931e']}
        style={styles.header}>
        <Text style={styles.headerTitle}>📋 Mis Viajes</Text>
        <Text style={styles.headerSubtitle}>{demoTrips.length} viajes realizados</Text>
      </LinearGradient>

      {/* Lista de viajes */}
      <ScrollView style={styles.content}>
        {demoTrips.map((trip, index) => (
          <Animatable.View
            key={trip.id}
            animation="fadeInUp"
            duration={500}
            delay={index * 200}
            style={styles.tripCard}>
            
            <View style={styles.tripHeader}>
              <Text style={styles.tripDate}>{trip.date}</Text>
              <Text style={styles.tripFare}>${trip.fare.toLocaleString()}</Text>
            </View>
            
            <View style={styles.tripRoute}>
              <Text style={styles.routeText}>📍 {trip.from}</Text>
              <Text style={styles.routeArrow}>↓</Text>
              <Text style={styles.routeText}>🎯 {trip.to}</Text>
            </View>
            
            <View style={styles.tripStatus}>
              <Text style={styles.statusText}>✅ Completado</Text>
            </View>
          </Animatable.View>
        ))}
        
        {demoTrips.length === 0 && (
          <View style={styles.emptyState}>
            <Text style={styles.emptyIcon}>🏍️</Text>
            <Text style={styles.emptyTitle}>No hay viajes</Text>
            <Text style={styles.emptyText}>Tus viajes aparecerán aquí</Text>
          </View>
        )}
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  header: {
    paddingTop: 50,
    paddingBottom: 20,
    paddingHorizontal: 20,
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: 'white',
    marginBottom: 5,
  },
  headerSubtitle: {
    fontSize: 16,
    color: 'rgba(255, 255, 255, 0.8)',
  },
  content: {
    flex: 1,
    paddingHorizontal: 20,
    paddingTop: 20,
  },
  tripCard: {
    backgroundColor: 'white',
    borderRadius: 15,
    padding: 20,
    marginBottom: 15,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  tripHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 15,
  },
  tripDate: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
  },
  tripFare: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#2ecc71',
  },
  tripRoute: {
    marginBottom: 15,
  },
  routeText: {
    fontSize: 14,
    color: '#666',
    marginBottom: 5,
  },
  routeArrow: {
    fontSize: 16,
    color: '#999',
    textAlign: 'center',
    marginVertical: 5,
  },
  tripStatus: {
    alignItems: 'flex-end',
  },
  statusText: {
    fontSize: 12,
    color: '#2ecc71',
    fontWeight: '600',
  },
  emptyState: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 60,
  },
  emptyIcon: {
    fontSize: 60,
    marginBottom: 20,
  },
  emptyTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 10,
  },
  emptyText: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
  },
});

export default TripsScreen;
