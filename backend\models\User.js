const mongoose = require('mongoose');

const userSchema = new mongoose.Schema({
  name: {
    type: String,
    required: true
  },
  email: {
    type: String,
    required: true,
    unique: true
  },
  phone: {
    type: String,
    required: true
  },
  userType: {
    type: String,
    enum: ['motero', 'pasajero'],
    required: true
  },
  balance: {
    type: Number,
    default: function() {
      return this.userType === 'motero' ? 10 : 0;
    }
  },
  isActive: {
    type: Boolean,
    default: true
  },
  location: {
    latitude: Number,
    longitude: Number
  },
  createdAt: {
    type: Date,
    default: Date.now
  }
});

module.exports = mongoose.model('User', userSchema);