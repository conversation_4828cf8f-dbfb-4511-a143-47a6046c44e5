/**
 * Pantal<PERSON> de Chat - MACLAREN
 * Chat en tiempo real entre pasajero y conductor
 */

import React, {useState, useEffect, useRef} from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  FlatList,
  KeyboardAvoidingView,
  Platform,
  StatusBar,
} from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import * as Animatable from 'react-native-animatable';
import {StackNavigationProp} from '@react-navigation/stack';
import {RouteProp} from '@react-navigation/native';
import {SocketService} from '../../services/SocketService';

interface Message {
  id: string;
  text: string;
  sender: 'passenger' | 'driver';
  timestamp: string;
  delivered: boolean;
}

interface ChatScreenProps {
  navigation: StackNavigationProp<any>;
  route: RouteProp<any>;
}

const ChatScreen: React.FC<ChatScreenProps> = ({navigation, route}) => {
  const [messages, setMessages] = useState<Message[]>([]);
  const [inputText, setInputText] = useState('');
  const [isTyping, setIsTyping] = useState(false);
  const [userType, setUserType] = useState<'passenger' | 'driver'>('passenger');
  const [otherUserName, setOtherUserName] = useState('Usuario');
  
  const flatListRef = useRef<FlatList>(null);
  const typingTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  const tripId = route.params?.tripId || 'demo-trip';
  const currentUserId = route.params?.userId || 'demo-user';

  useEffect(() => {
    // Configurar datos iniciales
    const routeUserType = route.params?.userType || 'passenger';
    const routeOtherName = route.params?.otherUserName || 
      (routeUserType === 'passenger' ? 'Conductor' : 'Pasajero');
    
    setUserType(routeUserType);
    setOtherUserName(routeOtherName);

    // Cargar mensajes existentes
    loadExistingMessages();
    
    // Configurar listeners de socket
    setupSocketListeners();

    return () => {
      if (typingTimeoutRef.current) {
        clearTimeout(typingTimeoutRef.current);
      }
    };
  }, []);

  const loadExistingMessages = () => {
    // Simular mensajes existentes
    const demoMessages: Message[] = [
      {
        id: '1',
        text: '¡Hola! Ya voy en camino hacia ti',
        sender: 'driver',
        timestamp: new Date(Date.now() - 300000).toISOString(),
        delivered: true,
      },
      {
        id: '2',
        text: 'Perfecto, te estaré esperando en la entrada',
        sender: 'passenger',
        timestamp: new Date(Date.now() - 240000).toISOString(),
        delivered: true,
      },
      {
        id: '3',
        text: 'Llego en aproximadamente 5 minutos',
        sender: 'driver',
        timestamp: new Date(Date.now() - 120000).toISOString(),
        delivered: true,
      },
    ];
    
    setMessages(demoMessages);
  };

  const setupSocketListeners = () => {
    // Escuchar nuevos mensajes
    SocketService.on('newMessage', (message: Message) => {
      setMessages(prev => [...prev, message]);
      scrollToBottom();
    });

    // Escuchar indicador de escritura
    SocketService.on('userTyping', (data: {userId: string, isTyping: boolean}) => {
      if (data.userId !== currentUserId) {
        setIsTyping(data.isTyping);
      }
    });

    // Escuchar confirmación de entrega
    SocketService.on('messageDelivered', (messageId: string) => {
      setMessages(prev => 
        prev.map(msg => 
          msg.id === messageId ? {...msg, delivered: true} : msg
        )
      );
    });
  };

  const sendMessage = () => {
    if (!inputText.trim()) return;

    const newMessage: Message = {
      id: Date.now().toString(),
      text: inputText.trim(),
      sender: userType,
      timestamp: new Date().toISOString(),
      delivered: false,
    };

    // Agregar mensaje localmente
    setMessages(prev => [...prev, newMessage]);
    
    // Enviar a través de socket
    SocketService.emit('sendMessage', {
      tripId,
      message: newMessage,
    });

    // Limpiar input y scroll
    setInputText('');
    scrollToBottom();
  };

  const handleInputChange = (text: string) => {
    setInputText(text);
    
    // Enviar indicador de escritura
    SocketService.emit('typing', {
      tripId,
      userId: currentUserId,
      isTyping: text.length > 0,
    });

    // Limpiar timeout anterior
    if (typingTimeoutRef.current) {
      clearTimeout(typingTimeoutRef.current);
    }

    // Detener indicador después de 2 segundos de inactividad
    typingTimeoutRef.current = setTimeout(() => {
      SocketService.emit('typing', {
        tripId,
        userId: currentUserId,
        isTyping: false,
      });
    }, 2000);
  };

  const scrollToBottom = () => {
    setTimeout(() => {
      flatListRef.current?.scrollToEnd({animated: true});
    }, 100);
  };

  const formatTime = (timestamp: string) => {
    const date = new Date(timestamp);
    return date.toLocaleTimeString('es-CO', {
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const renderMessage = ({item}: {item: Message}) => {
    const isOwnMessage = item.sender === userType;
    
    return (
      <Animatable.View
        animation="fadeInUp"
        duration={300}
        style={[
          styles.messageContainer,
          isOwnMessage ? styles.ownMessage : styles.otherMessage,
        ]}>
        
        <View style={[
          styles.messageBubble,
          isOwnMessage ? styles.ownBubble : styles.otherBubble,
        ]}>
          <Text style={[
            styles.messageText,
            isOwnMessage ? styles.ownMessageText : styles.otherMessageText,
          ]}>
            {item.text}
          </Text>
        </View>
        
        <View style={styles.messageInfo}>
          <Text style={styles.messageTime}>
            {formatTime(item.timestamp)}
          </Text>
          {isOwnMessage && (
            <Text style={styles.deliveryStatus}>
              {item.delivered ? '✓✓' : '✓'}
            </Text>
          )}
        </View>
      </Animatable.View>
    );
  };

  return (
    <View style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor="#ff6b35" />
      
      {/* Header */}
      <LinearGradient
        colors={['#ff6b35', '#f7931e']}
        style={styles.header}>
        
        <TouchableOpacity 
          style={styles.backButton}
          onPress={() => navigation.goBack()}>
          <Text style={styles.backButtonText}>←</Text>
        </TouchableOpacity>
        
        <View style={styles.headerInfo}>
          <Text style={styles.headerTitle}>{otherUserName}</Text>
          <Text style={styles.headerSubtitle}>
            {isTyping ? 'Escribiendo...' : 'En línea'}
          </Text>
        </View>
        
        <TouchableOpacity style={styles.callButton}>
          <Text style={styles.callButtonText}>📞</Text>
        </TouchableOpacity>
      </LinearGradient>

      {/* Lista de mensajes */}
      <FlatList
        ref={flatListRef}
        data={messages}
        keyExtractor={(item) => item.id}
        renderItem={renderMessage}
        style={styles.messagesList}
        contentContainerStyle={styles.messagesContainer}
        showsVerticalScrollIndicator={false}
        onContentSizeChange={scrollToBottom}
      />

      {/* Indicador de escritura */}
      {isTyping && (
        <Animatable.View
          animation="fadeIn"
          style={styles.typingIndicator}>
          <Text style={styles.typingText}>{otherUserName} está escribiendo...</Text>
        </Animatable.View>
      )}

      {/* Input de mensaje */}
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={styles.inputContainer}>
        
        <View style={styles.inputRow}>
          <TextInput
            style={styles.textInput}
            placeholder="Escribe un mensaje..."
            placeholderTextColor="#999"
            value={inputText}
            onChangeText={handleInputChange}
            multiline
            maxLength={500}
          />
          
          <TouchableOpacity
            style={[
              styles.sendButton,
              inputText.trim() ? styles.sendButtonActive : styles.sendButtonInactive
            ]}
            onPress={sendMessage}
            disabled={!inputText.trim()}>
            <Text style={styles.sendButtonText}>➤</Text>
          </TouchableOpacity>
        </View>
      </KeyboardAvoidingView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingTop: 50,
    paddingBottom: 15,
    paddingHorizontal: 20,
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 15,
  },
  backButtonText: {
    color: 'white',
    fontSize: 24,
    fontWeight: 'bold',
  },
  headerInfo: {
    flex: 1,
  },
  headerTitle: {
    color: 'white',
    fontSize: 18,
    fontWeight: 'bold',
  },
  headerSubtitle: {
    color: 'rgba(255, 255, 255, 0.8)',
    fontSize: 14,
    marginTop: 2,
  },
  callButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  callButtonText: {
    fontSize: 20,
  },
  messagesList: {
    flex: 1,
  },
  messagesContainer: {
    padding: 20,
    paddingBottom: 10,
  },
  messageContainer: {
    marginBottom: 15,
  },
  ownMessage: {
    alignItems: 'flex-end',
  },
  otherMessage: {
    alignItems: 'flex-start',
  },
  messageBubble: {
    maxWidth: '80%',
    paddingHorizontal: 15,
    paddingVertical: 10,
    borderRadius: 20,
  },
  ownBubble: {
    backgroundColor: '#ff6b35',
    borderBottomRightRadius: 5,
  },
  otherBubble: {
    backgroundColor: 'white',
    borderBottomLeftRadius: 5,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 1},
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  messageText: {
    fontSize: 16,
    lineHeight: 20,
  },
  ownMessageText: {
    color: 'white',
  },
  otherMessageText: {
    color: '#333',
  },
  messageInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 5,
    paddingHorizontal: 5,
  },
  messageTime: {
    fontSize: 12,
    color: '#999',
    marginRight: 5,
  },
  deliveryStatus: {
    fontSize: 12,
    color: '#2ecc71',
  },
  typingIndicator: {
    paddingHorizontal: 20,
    paddingVertical: 10,
  },
  typingText: {
    fontSize: 14,
    color: '#666',
    fontStyle: 'italic',
  },
  inputContainer: {
    backgroundColor: 'white',
    borderTopWidth: 1,
    borderTopColor: '#e9ecef',
  },
  inputRow: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    padding: 15,
  },
  textInput: {
    flex: 1,
    borderWidth: 1,
    borderColor: '#e9ecef',
    borderRadius: 25,
    paddingHorizontal: 15,
    paddingVertical: 10,
    fontSize: 16,
    maxHeight: 100,
    marginRight: 10,
    backgroundColor: '#f8f9fa',
  },
  sendButton: {
    width: 45,
    height: 45,
    borderRadius: 22.5,
    justifyContent: 'center',
    alignItems: 'center',
  },
  sendButtonActive: {
    backgroundColor: '#ff6b35',
  },
  sendButtonInactive: {
    backgroundColor: '#ccc',
  },
  sendButtonText: {
    color: 'white',
    fontSize: 20,
    fontWeight: 'bold',
  },
});

export default ChatScreen;
