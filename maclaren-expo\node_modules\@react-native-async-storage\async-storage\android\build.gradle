configurations {
    compileClasspath
}

buildscript {
    apply from: "config.gradle"
    def kotlinVersion = ext.AsyncStorageConfig.kotlinVersion
    def kspVersion = ext.AsyncStorageConfig.kspVersion

    repositories {
        mavenCentral()
        google()
    }
    dependencies {
        classpath "org.jetbrains.kotlin:kotlin-gradle-plugin:$kotlinVersion"
        classpath "com.google.devtools.ksp:symbol-processing-gradle-plugin:$kspVersion"
    }
}


apply plugin: 'com.android.library'
apply from: 'config.gradle'

boolean isNewArchitectureEnabled = ext.AsyncStorageConfig.isNewArchitectureEnabled
boolean useNextStorage = ext.AsyncStorageConfig.useNextStorage

logger.info("[AsyncStorage] Config used: {}", ext.AsyncStorageConfig)

if (useNextStorage) {
    apply plugin: 'com.google.devtools.ksp'
    apply plugin: 'kotlin-android'
    apply from: './testresults.gradle'
}

if (isNewArchitectureEnabled) {
    apply plugin: "com.facebook.react"
}

android {
    def agpVersion = com.android.Version.ANDROID_GRADLE_PLUGIN_VERSION
    if (agpVersion.tokenize('.')[0].toInteger() >= 7) {
        namespace "com.reactnativecommunity.asyncstorage"
        buildFeatures {
            buildConfig true
        }
    }

    compileSdkVersion project.ext.AsyncStorageConfig.compileSdkVersion
    // Used to override the NDK path/version by allowing users to customize
    // the NDK path/version from their root project (e.g. for M1 support)
    if (rootProject.hasProperty("ndkPath")) {
        ndkPath rootProject.ext.ndkPath
    }
    if (rootProject.hasProperty("ndkVersion")) {
        ndkVersion rootProject.ext.ndkVersion
    }


    defaultConfig {
        minSdkVersion project.ext.AsyncStorageConfig.minSdkVersion
        targetSdkVersion project.ext.AsyncStorageConfig.targetSdkVersion
        buildConfigField "Long", "AsyncStorage_db_size", "${project.ext.AsyncStorageConfig.databaseSizeMB}L"
        buildConfigField "boolean", "AsyncStorage_useDedicatedExecutor", "${project.ext.AsyncStorageConfig.useDedicatedExecutor}"
        buildConfigField "boolean", "AsyncStorage_useNextStorage", "${useNextStorage}"
        buildConfigField "boolean", "IS_NEW_ARCHITECTURE_ENABLED", "${isNewArchitectureEnabled}"
    }
    lintOptions {
        abortOnError false
    }

    if (useNextStorage) {
        testOptions {
            unitTests {
                returnDefaultValues = true
                includeAndroidResources = true
            }
        }
    }

    sourceSets.main {
        java {
            if (useNextStorage) {
                srcDirs += 'src/kotlinPackage/java'
            } else {
                srcDirs += 'src/javaPackage/java'
            }

            if (!isNewArchitectureEnabled) {
                srcDirs += 'src/oldarch/java'
            }
        }
    }
}

repositories {
    maven {
        // All of React Native (JS, Obj-C sources, Android binaries) is installed from npm
        url "${project.ext.resolveModulePath("react-native")}/android"
    }
    google()
    mavenCentral()
}

dependencies {
    if (useNextStorage) {
        def room_version = project.ext.AsyncStorageConfig.roomVersion

        implementation "androidx.room:room-runtime:$room_version"
        implementation "androidx.room:room-ktx:$room_version"
        ksp "androidx.room:room-compiler:$room_version"

        implementation project.ext.AsyncStorageLibs.coroutines

        testImplementation project.ext.AsyncStorageLibs.testCoroutines
        testImplementation project.ext.AsyncStorageLibs.testJunit
        testImplementation project.ext.AsyncStorageLibs.testExtJunit
        testImplementation project.ext.AsyncStorageLibs.testRunner
        testImplementation project.ext.AsyncStorageLibs.testRules
        testImplementation project.ext.AsyncStorageLibs.testRobolectric
        testImplementation project.ext.AsyncStorageLibs.testTruth
    }

    implementation 'com.facebook.react:react-native:+' // from node_modules
}