const express = require('express');
const cors = require('cors');
const mongoose = require('mongoose');

console.log('🚀 Iniciando servidor Maclaren...');

const app = express();

// Middleware
app.use(cors());
app.use(express.json());

// Conectar a MongoDB
mongoose.connect('mongodb+srv://macleanjhon8:<EMAIL>/maclaren?retryWrites=true&w=majority&appName=MotorRouter')
  .then(() => {
    console.log('✅ MongoDB conectado');
  })
  .catch(err => {
    console.error('❌ Error MongoDB:', err.message);
  });

// Esquema de usuario
const UserSchema = new mongoose.Schema({
  name: String,
  email: String,
  phone: String,
  userType: String,
  balance: { type: Number, default: 10 },
  createdAt: { type: Date, default: Date.now }
});

const User = mongoose.model('User', UserSchema);

// Rutas
app.get('/', (req, res) => {
  res.json({ message: 'Maclaren Backend OK! 🏍️' });
});

app.post('/register', async (req, res) => {
  try {
    const { name, email, phone, userType } = req.body;
    console.log('Registrando:', { name, email, phone, userType });
    
    const user = new User({ name, email, phone, userType });
    await user.save();
    
    console.log('✅ Usuario guardado');
    res.json({ success: true, user });
  } catch (error) {
    console.error('❌ Error:', error.message);
    res.json({ success: false, error: error.message });
  }
});

// Iniciar servidor
app.listen(3001, () => {
  console.log('🔥 Servidor en puerto 3001');
});