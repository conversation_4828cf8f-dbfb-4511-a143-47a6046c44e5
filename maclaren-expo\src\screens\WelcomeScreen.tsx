/**
 * Pantalla de Bienvenida - MACLAREN
 * Permite al usuario elegir entre ser Pasajero o Conductor
 */

import React, {useEffect} from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Dimensions,
  StatusBar,
  Image,
} from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import * as Animatable from 'react-native-animatable';
import {StackNavigationProp} from '@react-navigation/stack';

const {width, height} = Dimensions.get('window');

type WelcomeScreenProps = {
  navigation: StackNavigationProp<any>;
};

const WelcomeScreen: React.FC<WelcomeScreenProps> = ({navigation}) => {
  useEffect(() => {
    StatusBar.setBarStyle('light-content');
    StatusBar.setBackgroundColor('#ff6b35');
  }, []);

  const navigateToLogin = (userType: string) => {
    navigation.navigate('Login', {userType});
  };

  return (
    <LinearGradient
      colors={['#ff6b35', '#f7931e']}
      style={styles.container}>
      
      <StatusBar barStyle="light-content" backgroundColor="#ff6b35" />
      
      {/* Header con Logo */}
      <Animatable.View 
        animation="fadeInDown" 
        duration={1000}
        style={styles.header}>
        <Text style={styles.logo}>🏍️</Text>
        <Text style={styles.appName}>MACLAREN</Text>
        <Text style={styles.tagline}>Tu transporte rápido y seguro</Text>
      </Animatable.View>

      {/* Opciones de Usuario */}
      <View style={styles.optionsContainer}>
        
        {/* Opción Pasajero */}
        <Animatable.View 
          animation="slideInLeft" 
          duration={800}
          delay={300}>
          <TouchableOpacity
            style={styles.userTypeCard}
            onPress={() => navigateToLogin('passenger')}
            activeOpacity={0.8}>
            <View style={styles.cardContent}>
              <Text style={styles.cardIcon}>🧑‍💼</Text>
              <Text style={styles.cardTitle}>Soy Pasajero</Text>
              <Text style={styles.cardDescription}>
                Solicita viajes rápidos y seguros en moto
              </Text>
              <View style={styles.featuresContainer}>
                <Text style={styles.feature}>✓ Viajes rápidos</Text>
                <Text style={styles.feature}>✓ Tarifas justas</Text>
                <Text style={styles.feature}>✓ Seguimiento GPS</Text>
              </View>
            </View>
          </TouchableOpacity>
        </Animatable.View>

        {/* Opción Conductor */}
        <Animatable.View 
          animation="slideInRight" 
          duration={800}
          delay={500}>
          <TouchableOpacity
            style={styles.userTypeCard}
            onPress={() => navigateToLogin('driver')}
            activeOpacity={0.8}>
            <View style={styles.cardContent}>
              <Text style={styles.cardIcon}>🏍️</Text>
              <Text style={styles.cardTitle}>Soy Conductor</Text>
              <Text style={styles.cardDescription}>
                Genera ingresos transportando pasajeros
              </Text>
              <View style={styles.featuresContainer}>
                <Text style={styles.feature}>✓ Gana dinero extra</Text>
                <Text style={styles.feature}>✓ Horarios flexibles</Text>
                <Text style={styles.feature}>✓ Pagos inmediatos</Text>
              </View>
            </View>
          </TouchableOpacity>
        </Animatable.View>
      </View>

      {/* Footer */}
      <Animatable.View 
        animation="fadeInUp" 
        duration={1000}
        delay={700}
        style={styles.footer}>
        <View style={styles.featuresRow}>
          <View style={styles.featureItem}>
            <Text style={styles.featureIcon}>⚡</Text>
            <Text style={styles.featureText}>Rápido</Text>
          </View>
          <View style={styles.featureItem}>
            <Text style={styles.featureIcon}>🛡️</Text>
            <Text style={styles.featureText}>Seguro</Text>
          </View>
          <View style={styles.featureItem}>
            <Text style={styles.featureIcon}>💰</Text>
            <Text style={styles.featureText}>Económico</Text>
          </View>
          <View style={styles.featureItem}>
            <Text style={styles.featureIcon}>📱</Text>
            <Text style={styles.featureText}>Fácil</Text>
          </View>
        </View>
      </Animatable.View>
    </LinearGradient>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingHorizontal: 20,
  },
  header: {
    alignItems: 'center',
    paddingTop: 60,
    paddingBottom: 40,
  },
  logo: {
    fontSize: 80,
    marginBottom: 10,
  },
  appName: {
    fontSize: 32,
    fontWeight: 'bold',
    color: 'white',
    marginBottom: 8,
    textShadowColor: 'rgba(0,0,0,0.3)',
    textShadowOffset: {width: 2, height: 2},
    textShadowRadius: 4,
  },
  tagline: {
    fontSize: 18,
    color: 'white',
    opacity: 0.9,
    textAlign: 'center',
  },
  optionsContainer: {
    flex: 1,
    justifyContent: 'center',
    gap: 20,
  },
  userTypeCard: {
    backgroundColor: 'rgba(255,255,255,0.15)',
    borderRadius: 20,
    padding: 25,
    marginVertical: 10,
    borderWidth: 2,
    borderColor: 'rgba(255,255,255,0.2)',
  },
  cardContent: {
    alignItems: 'center',
  },
  cardIcon: {
    fontSize: 60,
    marginBottom: 15,
  },
  cardTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: 'white',
    marginBottom: 10,
  },
  cardDescription: {
    fontSize: 16,
    color: 'white',
    textAlign: 'center',
    marginBottom: 15,
    opacity: 0.9,
    lineHeight: 22,
  },
  featuresContainer: {
    alignItems: 'center',
  },
  feature: {
    fontSize: 14,
    color: 'white',
    opacity: 0.8,
    marginVertical: 2,
  },
  footer: {
    paddingBottom: 40,
  },
  featuresRow: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    paddingVertical: 20,
  },
  featureItem: {
    alignItems: 'center',
  },
  featureIcon: {
    fontSize: 24,
    marginBottom: 8,
  },
  featureText: {
    fontSize: 12,
    color: 'white',
    opacity: 0.9,
    fontWeight: '600',
  },
});

export default WelcomeScreen;
