@echo off
echo.
echo ========================================
echo    🏍️ MACLAREN - Iniciando Aplicacion
echo ========================================
echo.

echo 🔥 Iniciando backend...
start "MACLAREN Backend" cmd /k "cd backend && node server.js"

timeout /t 3 /nobreak > nul

echo 📱 Iniciando app Expo...
start "MACLAREN App" cmd /k "cd maclaren-expo && npx expo start"

echo.
echo ✅ ¡MACLAREN iniciado!
echo.
echo 📱 Escanea el QR con Expo Go en tu telefono
echo 🌐 Backend corriendo en: http://localhost:3002
echo.
pause
