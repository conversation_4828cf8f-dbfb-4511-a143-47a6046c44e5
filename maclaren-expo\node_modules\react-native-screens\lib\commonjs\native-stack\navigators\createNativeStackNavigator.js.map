{"version": 3, "names": ["_native", "require", "React", "_interopRequireWildcard", "_NativeStackView", "_interopRequireDefault", "obj", "__esModule", "default", "_getRequireWildcardCache", "nodeInterop", "WeakMap", "cacheBabelInterop", "cacheNodeInterop", "cache", "has", "get", "newObj", "hasPropertyDescriptor", "Object", "defineProperty", "getOwnPropertyDescriptor", "key", "prototype", "hasOwnProperty", "call", "desc", "set", "_extends", "assign", "bind", "target", "i", "arguments", "length", "source", "apply", "NativeStackNavigator", "_ref", "initialRouteName", "children", "screenOptions", "rest", "state", "descriptors", "navigation", "useNavigationBuilder", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "useEffect", "dangerouslyGetParent", "undefined", "console", "warn", "_addListener", "_ref2", "addListener", "e", "isFocused", "requestAnimationFrame", "index", "defaultPrevented", "dispatch", "StackActions", "popToTop", "createElement", "_default", "createNavigatorFactory", "exports"], "sources": ["createNativeStackNavigator.tsx"], "sourcesContent": ["import {\n  create<PERSON><PERSON><PERSON>or<PERSON><PERSON>y,\n  EventArg,\n  StackActions,\n  StackActionHelpers,\n  StackNavigationState,\n  StackRouter,\n  StackRouterOptions,\n  ParamListBase,\n  useNavigationBuilder,\n} from '@react-navigation/native';\nimport * as React from 'react';\nimport {\n  NativeStackNavigationEventMap,\n  NativeStackNavigationOptions,\n  NativeStackNavigatorProps,\n} from '../types';\nimport NativeStackView from '../views/NativeStackView';\n\nfunction NativeStackNavigator({\n  initialRouteName,\n  children,\n  screenOptions,\n  ...rest\n}: NativeStackNavigatorProps) {\n  const { state, descriptors, navigation } = useNavigationBuilder<\n    StackNavigationState<ParamListBase>,\n    StackRouterOptions,\n    StackActionHelpers<ParamListBase>,\n    NativeStackNavigationOptions,\n    NativeStackNavigationEventMap\n  >(StackRouter, {\n    initialRouteName,\n    children,\n    screenOptions,\n  });\n\n  // Starting from React Navigation v6, `native-stack` should be imported from\n  // `@react-navigation/native-stack` rather than `react-native-screens/native-stack`\n  React.useEffect(() => {\n    // @ts-ignore navigation.dangerouslyGetParent was removed in v6\n    if (navigation?.dangerouslyGetParent === undefined) {\n      console.warn(\n        'Looks like you are importing `native-stack` from `react-native-screens/native-stack`. Since version 6 of `react-navigation`, it should be imported from `@react-navigation/native-stack`.'\n      );\n    }\n  }, [navigation]);\n\n  React.useEffect(\n    () =>\n      // eslint-disable-next-line @typescript-eslint/no-explicit-any\n      (navigation as typeof navigation & { addListener: any })?.addListener?.(\n        'tabPress',\n        (e: any) => {\n          const isFocused = navigation.isFocused();\n\n          // Run the operation in the next frame so we're sure all listeners have been run\n          // This is necessary to know if preventDefault() has been called\n          requestAnimationFrame(() => {\n            if (\n              state.index > 0 &&\n              isFocused &&\n              !(e as EventArg<'tabPress', true>).defaultPrevented\n            ) {\n              // When user taps on already focused tab and we're inside the tab,\n              // reset the stack to replicate native behaviour\n              navigation.dispatch({\n                ...StackActions.popToTop(),\n                target: state.key,\n              });\n            }\n          });\n        }\n      ),\n    [navigation, state.index, state.key]\n  );\n\n  return (\n    <NativeStackView\n      {...rest}\n      state={state}\n      navigation={navigation}\n      descriptors={descriptors}\n    />\n  );\n}\n\nexport default createNavigatorFactory<\n  StackNavigationState<ParamListBase>,\n  NativeStackNavigationOptions,\n  NativeStackNavigationEventMap,\n  typeof NativeStackNavigator\n>(NativeStackNavigator);\n"], "mappings": ";;;;;;AAAA,IAAAA,OAAA,GAAAC,OAAA;AAWA,IAAAC,KAAA,GAAAC,uBAAA,CAAAF,OAAA;AAMA,IAAAG,gBAAA,GAAAC,sBAAA,CAAAJ,OAAA;AAAuD,SAAAI,uBAAAC,GAAA,WAAAA,GAAA,IAAAA,GAAA,CAAAC,UAAA,GAAAD,GAAA,KAAAE,OAAA,EAAAF,GAAA;AAAA,SAAAG,yBAAAC,WAAA,eAAAC,OAAA,kCAAAC,iBAAA,OAAAD,OAAA,QAAAE,gBAAA,OAAAF,OAAA,YAAAF,wBAAA,YAAAA,CAAAC,WAAA,WAAAA,WAAA,GAAAG,gBAAA,GAAAD,iBAAA,KAAAF,WAAA;AAAA,SAAAP,wBAAAG,GAAA,EAAAI,WAAA,SAAAA,WAAA,IAAAJ,GAAA,IAAAA,GAAA,CAAAC,UAAA,WAAAD,GAAA,QAAAA,GAAA,oBAAAA,GAAA,wBAAAA,GAAA,4BAAAE,OAAA,EAAAF,GAAA,UAAAQ,KAAA,GAAAL,wBAAA,CAAAC,WAAA,OAAAI,KAAA,IAAAA,KAAA,CAAAC,GAAA,CAAAT,GAAA,YAAAQ,KAAA,CAAAE,GAAA,CAAAV,GAAA,SAAAW,MAAA,WAAAC,qBAAA,GAAAC,MAAA,CAAAC,cAAA,IAAAD,MAAA,CAAAE,wBAAA,WAAAC,GAAA,IAAAhB,GAAA,QAAAgB,GAAA,kBAAAH,MAAA,CAAAI,SAAA,CAAAC,cAAA,CAAAC,IAAA,CAAAnB,GAAA,EAAAgB,GAAA,SAAAI,IAAA,GAAAR,qBAAA,GAAAC,MAAA,CAAAE,wBAAA,CAAAf,GAAA,EAAAgB,GAAA,cAAAI,IAAA,KAAAA,IAAA,CAAAV,GAAA,IAAAU,IAAA,CAAAC,GAAA,KAAAR,MAAA,CAAAC,cAAA,CAAAH,MAAA,EAAAK,GAAA,EAAAI,IAAA,YAAAT,MAAA,CAAAK,GAAA,IAAAhB,GAAA,CAAAgB,GAAA,SAAAL,MAAA,CAAAT,OAAA,GAAAF,GAAA,MAAAQ,KAAA,IAAAA,KAAA,CAAAa,GAAA,CAAArB,GAAA,EAAAW,MAAA,YAAAA,MAAA;AAAA,SAAAW,SAAA,IAAAA,QAAA,GAAAT,MAAA,CAAAU,MAAA,GAAAV,MAAA,CAAAU,MAAA,CAAAC,IAAA,eAAAC,MAAA,aAAAC,CAAA,MAAAA,CAAA,GAAAC,SAAA,CAAAC,MAAA,EAAAF,CAAA,UAAAG,MAAA,GAAAF,SAAA,CAAAD,CAAA,YAAAV,GAAA,IAAAa,MAAA,QAAAhB,MAAA,CAAAI,SAAA,CAAAC,cAAA,CAAAC,IAAA,CAAAU,MAAA,EAAAb,GAAA,KAAAS,MAAA,CAAAT,GAAA,IAAAa,MAAA,CAAAb,GAAA,gBAAAS,MAAA,YAAAH,QAAA,CAAAQ,KAAA,OAAAH,SAAA;AAEvD,SAASI,oBAAoBA,CAAAC,IAAA,EAKC;EAAA,IALA;IAC5BC,gBAAgB;IAChBC,QAAQ;IACRC,aAAa;IACb,GAAGC;EACsB,CAAC,GAAAJ,IAAA;EAC1B,MAAM;IAAEK,KAAK;IAAEC,WAAW;IAAEC;EAAW,CAAC,GAAG,IAAAC,4BAAoB,EAM7DC,mBAAW,EAAE;IACbR,gBAAgB;IAChBC,QAAQ;IACRC;EACF,CAAC,CAAC;;EAEF;EACA;EACAvC,KAAK,CAAC8C,SAAS,CAAC,MAAM;IACpB;IACA,IAAI,CAAAH,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAEI,oBAAoB,MAAKC,SAAS,EAAE;MAClDC,OAAO,CAACC,IAAI,CACV,2LACF,CAAC;IACH;EACF,CAAC,EAAE,CAACP,UAAU,CAAC,CAAC;EAEhB3C,KAAK,CAAC8C,SAAS,CACb;IAAA,IAAAK,YAAA,EAAAC,KAAA;IAAA,OACE;MACCT,UAAU,aAAVA,UAAU,gBAAAQ,YAAA,GAAX,CAAAC,KAAA,GAACT,UAAU,EAA+CU,WAAW,cAAAF,YAAA,uBAArEA,YAAA,CAAA5B,IAAA,CAAA6B,KAAA,EACE,UAAU,EACTE,CAAM,IAAK;QACV,MAAMC,SAAS,GAAGZ,UAAU,CAACY,SAAS,CAAC,CAAC;;QAExC;QACA;QACAC,qBAAqB,CAAC,MAAM;UAC1B,IACEf,KAAK,CAACgB,KAAK,GAAG,CAAC,IACfF,SAAS,IACT,CAAED,CAAC,CAAgCI,gBAAgB,EACnD;YACA;YACA;YACAf,UAAU,CAACgB,QAAQ,CAAC;cAClB,GAAGC,oBAAY,CAACC,QAAQ,CAAC,CAAC;cAC1BhC,MAAM,EAAEY,KAAK,CAACrB;YAChB,CAAC,CAAC;UACJ;QACF,CAAC,CAAC;MACJ,CACF;IAAC;EAAA,GACH,CAACuB,UAAU,EAAEF,KAAK,CAACgB,KAAK,EAAEhB,KAAK,CAACrB,GAAG,CACrC,CAAC;EAED,oBACEpB,KAAA,CAAA8D,aAAA,CAAC5D,gBAAA,CAAAI,OAAe,EAAAoB,QAAA,KACVc,IAAI;IACRC,KAAK,EAAEA,KAAM;IACbE,UAAU,EAAEA,UAAW;IACvBD,WAAW,EAAEA;EAAY,EAC1B,CAAC;AAEN;AAAC,IAAAqB,QAAA,GAEc,IAAAC,8BAAsB,EAKnC7B,oBAAoB,CAAC;AAAA8B,OAAA,CAAA3D,OAAA,GAAAyD,QAAA"}