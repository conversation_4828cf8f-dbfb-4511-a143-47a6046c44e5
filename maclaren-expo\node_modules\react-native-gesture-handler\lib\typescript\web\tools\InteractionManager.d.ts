import GestureHandler from '../handlers/GestureHandler';
import { Config } from '../interfaces';
export default class InteractionManager {
    private static instance;
    private readonly waitForRelations;
    private readonly simultaneousRelations;
    private readonly blocksHandlersRelations;
    private constructor();
    configureInteractions(handler: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, config: Config): void;
    shouldWaitForHandlerFailure(handler: GestureHandler, otherHandler: GestureHandler): boolean;
    shouldRecognizeSimultaneously(handler: <PERSON><PERSON>ure<PERSON><PERSON><PERSON>, otherHandler: GestureHandler): boolean;
    shouldRequireHandlerToWaitForFailure(handler: GestureHandler, otherHandler: GestureHandler): boolean;
    shouldHandlerBeCancelledBy(_handler: GestureHandler, _otherHandler: GestureHandler): boolean;
    dropRelationsForHandlerWithTag(handlerTag: number): void;
    reset(): void;
    static getInstance(): InteractionManager;
}
