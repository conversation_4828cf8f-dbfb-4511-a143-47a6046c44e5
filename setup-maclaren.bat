@echo off
echo.
echo ========================================
echo    🏍️ MACLAREN - Setup Automatico
echo ========================================
echo.

echo 📦 Instalando dependencias de Expo...
cd maclaren-expo
call npm install

echo.
echo 📦 Instalando dependencias del backend...
cd ../backend
call npm install

echo.
echo ✅ ¡Instalacion completada!
echo.
echo 🚀 Para ejecutar la app:
echo    1. Backend: cd backend && node server.js
echo    2. App: cd maclaren-expo && npx expo start
echo.
echo 📱 Luego escanea el QR con Expo Go en tu telefono
echo.
pause
