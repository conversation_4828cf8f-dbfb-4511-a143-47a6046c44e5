/**
 * Pantalla Principal del Conductor - MACLAREN
 * Recibir y gestionar solicitudes de viaje
 */

import React, {useState, useEffect, useRef} from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Alert,
  ScrollView,
  Dimensions,
  StatusBar,
  Modal,
  Switch,
} from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import * as Animatable from 'react-native-animatable';
import {SocketService, TripRequest} from '../../services/SocketService';
import {LocationService} from '../../services/LocationService';
import {NotificationService} from '../../services/NotificationService';

const {width, height} = Dimensions.get('window');

interface TripRequestData {
  id: string;
  passenger: {
    name: string;
    phone: string;
    rating: number;
  };
  pickup: {
    address: string;
    coordinates: [number, number];
  };
  destination: {
    address: string;
    coordinates: [number, number];
  };
  fare: number;
  distance: string;
  estimatedTime: string;
  timestamp: string;
}

const DriverHomeScreen: React.FC = () => {
  const [isOnline, setIsOnline] = useState(false);
  const [currentLocation, setCurrentLocation] = useState<string>('Obteniendo ubicación...');
  const [tripRequests, setTripRequests] = useState<TripRequestData[]>([]);
  const [activeTrip, setActiveTrip] = useState<TripRequestData | null>(null);
  const [todayEarnings, setTodayEarnings] = useState(0);
  const [todayTrips, setTodayTrips] = useState(0);
  const [driverRating, setDriverRating] = useState(4.8);
  const [showTripModal, setShowTripModal] = useState(false);
  const [selectedTrip, setSelectedTrip] = useState<TripRequestData | null>(null);

  const requestTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  useEffect(() => {
    initializeDriver();
    setupSocketListeners();
    
    return () => {
      if (requestTimeoutRef.current) {
        clearTimeout(requestTimeoutRef.current);
      }
    };
  }, []);

  const initializeDriver = async () => {
    try {
      // Obtener ubicación actual
      const location = await LocationService.getCurrentLocation();
      setCurrentLocation(`${location.latitude.toFixed(4)}, ${location.longitude.toFixed(4)}`);
      
      // Cargar datos del conductor desde AsyncStorage
      loadDriverData();
      
    } catch (error) {
      console.error('Error inicializando conductor:', error);
      Alert.alert('Error', 'No se pudo obtener la ubicación');
    }
  };

  const loadDriverData = () => {
    // Simular carga de datos del conductor
    setTodayEarnings(125000);
    setTodayTrips(8);
    setDriverRating(4.8);
  };

  const setupSocketListeners = () => {
    // Escuchar nuevas solicitudes de viaje
    SocketService.on('newTripRequest', (tripData: TripRequestData) => {
      if (isOnline) {
        handleNewTripRequest(tripData);
      }
    });

    // Escuchar cancelaciones
    SocketService.on('tripCancelled', (tripId: string) => {
      setTripRequests(prev => prev.filter(trip => trip.id !== tripId));
      if (selectedTrip?.id === tripId) {
        setShowTripModal(false);
        setSelectedTrip(null);
      }
    });
  };

  const handleNewTripRequest = (tripData: TripRequestData) => {
    // Agregar nueva solicitud
    setTripRequests(prev => [...prev, tripData]);
    
    // Mostrar notificación
    NotificationService.showLocalNotification(
      'Nueva Solicitud de Viaje',
      `${tripData.passenger.name} necesita un viaje a ${tripData.destination.address}`
    );

    // Auto-remover después de 30 segundos si no se acepta
    requestTimeoutRef.current = setTimeout(() => {
      setTripRequests(prev => prev.filter(trip => trip.id !== tripData.id));
    }, 30000);
  };

  const toggleOnlineStatus = () => {
    const newStatus = !isOnline;
    setIsOnline(newStatus);
    
    if (newStatus) {
      // Conectar conductor
      SocketService.emit('driverOnline', {
        location: currentLocation,
        available: true
      });
      
      Alert.alert('¡Conectado!', 'Ahora puedes recibir solicitudes de viaje');
    } else {
      // Desconectar conductor
      SocketService.emit('driverOffline');
      setTripRequests([]);
      
      Alert.alert('Desconectado', 'Ya no recibirás solicitudes de viaje');
    }
  };

  const handleTripPress = (trip: TripRequestData) => {
    setSelectedTrip(trip);
    setShowTripModal(true);
  };

  const acceptTrip = () => {
    if (!selectedTrip) return;

    // Enviar aceptación al servidor
    SocketService.emit('acceptTrip', {
      tripId: selectedTrip.id,
      driverId: 'current-driver-id', // Obtener del contexto
    });

    // Actualizar estado local
    setActiveTrip(selectedTrip);
    setTripRequests(prev => prev.filter(trip => trip.id !== selectedTrip.id));
    setShowTripModal(false);
    setSelectedTrip(null);

    Alert.alert('¡Viaje Aceptado!', 'Dirígete al punto de recogida');
  };

  const rejectTrip = () => {
    if (!selectedTrip) return;

    // Enviar rechazo al servidor
    SocketService.emit('rejectTrip', {
      tripId: selectedTrip.id,
      driverId: 'current-driver-id',
    });

    // Remover de la lista
    setTripRequests(prev => prev.filter(trip => trip.id !== selectedTrip.id));
    setShowTripModal(false);
    setSelectedTrip(null);
  };

  const completeTrip = () => {
    if (!activeTrip) return;

    Alert.alert(
      'Completar Viaje',
      '¿Has llegado al destino?',
      [
        {text: 'No', style: 'cancel'},
        {
          text: 'Sí, completar',
          onPress: () => {
            // Enviar completación al servidor
            SocketService.emit('completeTrip', {
              tripId: activeTrip.id,
              fare: activeTrip.fare,
            });

            // Actualizar estadísticas
            setTodayEarnings(prev => prev + activeTrip.fare);
            setTodayTrips(prev => prev + 1);
            setActiveTrip(null);

            Alert.alert('¡Viaje Completado!', `Has ganado $${activeTrip.fare.toLocaleString()}`);
          }
        }
      ]
    );
  };

  return (
    <View style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor="#ff6b35" />
      
      {/* Header con estado */}
      <LinearGradient
        colors={['#ff6b35', '#f7931e']}
        style={styles.header}>
        
        <View style={styles.headerContent}>
          <View style={styles.statusContainer}>
            <View style={[styles.statusIndicator, {backgroundColor: isOnline ? '#2ecc71' : '#e74c3c'}]} />
            <Text style={styles.statusText}>
              {isOnline ? 'En línea' : 'Fuera de línea'}
            </Text>
            <Switch
              value={isOnline}
              onValueChange={toggleOnlineStatus}
              trackColor={{false: '#ccc', true: '#2ecc71'}}
              thumbColor={isOnline ? '#fff' : '#f4f3f4'}
            />
          </View>
          
          <Text style={styles.locationText}>📍 {currentLocation}</Text>
        </View>

        {/* Estadísticas del día */}
        <View style={styles.statsContainer}>
          <View style={styles.statItem}>
            <Text style={styles.statValue}>${todayEarnings.toLocaleString()}</Text>
            <Text style={styles.statLabel}>Ganancias hoy</Text>
          </View>
          <View style={styles.statItem}>
            <Text style={styles.statValue}>{todayTrips}</Text>
            <Text style={styles.statLabel}>Viajes</Text>
          </View>
          <View style={styles.statItem}>
            <Text style={styles.statValue}>⭐ {driverRating}</Text>
            <Text style={styles.statLabel}>Rating</Text>
          </View>
        </View>
      </LinearGradient>

      {/* Contenido principal */}
      <View style={styles.mainContent}>
        
        {/* Viaje activo */}
        {activeTrip && (
          <Animatable.View 
            animation="slideInDown"
            style={styles.activeTripContainer}>
            <Text style={styles.activeTripTitle}>🚗 Viaje en Curso</Text>
            
            <View style={styles.passengerInfo}>
              <Text style={styles.passengerName}>{activeTrip.passenger.name}</Text>
              <Text style={styles.passengerPhone}>📱 {activeTrip.passenger.phone}</Text>
              <Text style={styles.passengerRating}>⭐ {activeTrip.passenger.rating}</Text>
            </View>

            <View style={styles.tripRoute}>
              <View style={styles.routeItem}>
                <Text style={styles.routeIcon}>📍</Text>
                <Text style={styles.routeText}>{activeTrip.pickup.address}</Text>
              </View>
              <View style={styles.routeItem}>
                <Text style={styles.routeIcon}>🎯</Text>
                <Text style={styles.routeText}>{activeTrip.destination.address}</Text>
              </View>
            </View>

            <View style={styles.tripActions}>
              <TouchableOpacity 
                style={styles.chatButton}
                onPress={() => {/* Navegar a chat */}}>
                <Text style={styles.chatButtonText}>💬 Chat</Text>
              </TouchableOpacity>
              
              <TouchableOpacity 
                style={styles.completeButton}
                onPress={completeTrip}>
                <Text style={styles.completeButtonText}>✅ Completar</Text>
              </TouchableOpacity>
            </View>
          </Animatable.View>
        )}

        {/* Lista de solicitudes */}
        <ScrollView style={styles.requestsList}>
          {!isOnline ? (
            <View style={styles.offlineContainer}>
              <Text style={styles.offlineIcon}>🏍️</Text>
              <Text style={styles.offlineTitle}>Fuera de línea</Text>
              <Text style={styles.offlineText}>Actívate para recibir solicitudes de viaje</Text>
            </View>
          ) : tripRequests.length === 0 ? (
            <View style={styles.noRequestsContainer}>
              <Text style={styles.noRequestsIcon}>⏳</Text>
              <Text style={styles.noRequestsTitle}>Esperando solicitudes...</Text>
              <Text style={styles.noRequestsText}>Mantente atento a nuevas oportunidades</Text>
            </View>
          ) : (
            tripRequests.map((trip) => (
              <Animatable.View
                key={trip.id}
                animation="slideInRight"
                style={styles.tripRequestCard}>
                
                <TouchableOpacity
                  onPress={() => handleTripPress(trip)}
                  style={styles.tripRequestContent}>
                  
                  <View style={styles.tripRequestHeader}>
                    <Text style={styles.passengerName}>{trip.passenger.name}</Text>
                    <Text style={styles.fareAmount}>${trip.fare.toLocaleString()}</Text>
                  </View>

                  <View style={styles.tripRequestDetails}>
                    <Text style={styles.tripDistance}>{trip.distance} • {trip.estimatedTime}</Text>
                    <Text style={styles.passengerRating}>⭐ {trip.passenger.rating}</Text>
                  </View>

                  <View style={styles.tripRoute}>
                    <View style={styles.routeItem}>
                      <Text style={styles.routeIcon}>📍</Text>
                      <Text style={styles.routeText} numberOfLines={1}>
                        {trip.pickup.address}
                      </Text>
                    </View>
                    <View style={styles.routeItem}>
                      <Text style={styles.routeIcon}>🎯</Text>
                      <Text style={styles.routeText} numberOfLines={1}>
                        {trip.destination.address}
                      </Text>
                    </View>
                  </View>

                  <View style={styles.requestActions}>
                    <TouchableOpacity 
                      style={styles.rejectButton}
                      onPress={(e) => {
                        e.stopPropagation();
                        setSelectedTrip(trip);
                        rejectTrip();
                      }}>
                      <Text style={styles.rejectButtonText}>❌</Text>
                    </TouchableOpacity>
                    
                    <TouchableOpacity 
                      style={styles.acceptButton}
                      onPress={(e) => {
                        e.stopPropagation();
                        setSelectedTrip(trip);
                        acceptTrip();
                      }}>
                      <Text style={styles.acceptButtonText}>✅ Aceptar</Text>
                    </TouchableOpacity>
                  </View>

                </TouchableOpacity>
              </Animatable.View>
            ))
          )}
        </ScrollView>
      </View>

      {/* Modal de detalles del viaje */}
      <Modal
        visible={showTripModal}
        animationType="slide"
        transparent={true}
        onRequestClose={() => setShowTripModal(false)}>
        
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            {selectedTrip && (
              <>
                <Text style={styles.modalTitle}>Detalles del Viaje</Text>
                
                <View style={styles.modalPassengerInfo}>
                  <Text style={styles.modalPassengerName}>{selectedTrip.passenger.name}</Text>
                  <Text style={styles.modalPassengerDetails}>
                    📱 {selectedTrip.passenger.phone} • ⭐ {selectedTrip.passenger.rating}
                  </Text>
                </View>

                <View style={styles.modalTripInfo}>
                  <Text style={styles.modalFare}>${selectedTrip.fare.toLocaleString()}</Text>
                  <Text style={styles.modalDistance}>{selectedTrip.distance} • {selectedTrip.estimatedTime}</Text>
                </View>

                <View style={styles.modalRoute}>
                  <View style={styles.modalRouteItem}>
                    <Text style={styles.modalRouteIcon}>📍</Text>
                    <Text style={styles.modalRouteText}>{selectedTrip.pickup.address}</Text>
                  </View>
                  <View style={styles.modalRouteItem}>
                    <Text style={styles.modalRouteIcon}>🎯</Text>
                    <Text style={styles.modalRouteText}>{selectedTrip.destination.address}</Text>
                  </View>
                </View>

                <View style={styles.modalActions}>
                  <TouchableOpacity 
                    style={styles.modalRejectButton}
                    onPress={rejectTrip}>
                    <Text style={styles.modalRejectButtonText}>❌ Rechazar</Text>
                  </TouchableOpacity>
                  
                  <TouchableOpacity 
                    style={styles.modalAcceptButton}
                    onPress={acceptTrip}>
                    <Text style={styles.modalAcceptButtonText}>✅ Aceptar</Text>
                  </TouchableOpacity>
                </View>
              </>
            )}
          </View>
        </View>
      </Modal>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  header: {
    paddingTop: 50,
    paddingBottom: 20,
    paddingHorizontal: 20,
  },
  headerContent: {
    marginBottom: 20,
  },
  statusContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 10,
  },
  statusIndicator: {
    width: 12,
    height: 12,
    borderRadius: 6,
    marginRight: 8,
  },
  statusText: {
    color: 'white',
    fontSize: 18,
    fontWeight: 'bold',
    flex: 1,
  },
  locationText: {
    color: 'rgba(255, 255, 255, 0.9)',
    fontSize: 14,
  },
  statsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    borderRadius: 15,
    padding: 15,
  },
  statItem: {
    alignItems: 'center',
  },
  statValue: {
    color: 'white',
    fontSize: 18,
    fontWeight: 'bold',
  },
  statLabel: {
    color: 'rgba(255, 255, 255, 0.8)',
    fontSize: 12,
    marginTop: 2,
  },
  mainContent: {
    flex: 1,
    padding: 20,
  },
  activeTripContainer: {
    backgroundColor: 'white',
    borderRadius: 15,
    padding: 20,
    marginBottom: 20,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
    borderLeftWidth: 4,
    borderLeftColor: '#2ecc71',
  },
  activeTripTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#2ecc71',
    marginBottom: 15,
  },
  passengerInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 15,
  },
  passengerName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    flex: 1,
  },
  passengerPhone: {
    fontSize: 14,
    color: '#666',
    marginRight: 10,
  },
  passengerRating: {
    fontSize: 14,
    color: '#f39c12',
  },
  tripRoute: {
    marginBottom: 15,
  },
  routeItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  routeIcon: {
    fontSize: 16,
    marginRight: 10,
    width: 20,
  },
  routeText: {
    fontSize: 14,
    color: '#333',
    flex: 1,
  },
  tripActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  chatButton: {
    backgroundColor: '#3498db',
    borderRadius: 8,
    paddingVertical: 10,
    paddingHorizontal: 20,
    flex: 1,
    marginRight: 10,
  },
  chatButtonText: {
    color: 'white',
    fontWeight: 'bold',
    textAlign: 'center',
  },
  completeButton: {
    backgroundColor: '#2ecc71',
    borderRadius: 8,
    paddingVertical: 10,
    paddingHorizontal: 20,
    flex: 1,
  },
  completeButtonText: {
    color: 'white',
    fontWeight: 'bold',
    textAlign: 'center',
  },
  requestsList: {
    flex: 1,
  },
  offlineContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 60,
  },
  offlineIcon: {
    fontSize: 60,
    marginBottom: 20,
  },
  offlineTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 10,
  },
  offlineText: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
  },
  noRequestsContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 60,
  },
  noRequestsIcon: {
    fontSize: 60,
    marginBottom: 20,
  },
  noRequestsTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 10,
  },
  noRequestsText: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
  },
  tripRequestCard: {
    backgroundColor: 'white',
    borderRadius: 15,
    marginBottom: 15,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  tripRequestContent: {
    padding: 20,
  },
  tripRequestHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 10,
  },
  fareAmount: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#2ecc71',
  },
  tripRequestDetails: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 15,
  },
  tripDistance: {
    fontSize: 14,
    color: '#666',
  },
  requestActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 15,
  },
  rejectButton: {
    backgroundColor: '#e74c3c',
    borderRadius: 8,
    paddingVertical: 10,
    paddingHorizontal: 15,
  },
  rejectButtonText: {
    color: 'white',
    fontWeight: 'bold',
  },
  acceptButton: {
    backgroundColor: '#2ecc71',
    borderRadius: 8,
    paddingVertical: 10,
    paddingHorizontal: 20,
    flex: 1,
    marginLeft: 10,
  },
  acceptButtonText: {
    color: 'white',
    fontWeight: 'bold',
    textAlign: 'center',
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    backgroundColor: 'white',
    borderRadius: 20,
    padding: 25,
    width: width * 0.9,
    maxHeight: height * 0.8,
  },
  modalTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
    textAlign: 'center',
    marginBottom: 20,
  },
  modalPassengerInfo: {
    alignItems: 'center',
    marginBottom: 20,
  },
  modalPassengerName: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 5,
  },
  modalPassengerDetails: {
    fontSize: 14,
    color: '#666',
  },
  modalTripInfo: {
    alignItems: 'center',
    marginBottom: 20,
  },
  modalFare: {
    fontSize: 32,
    fontWeight: 'bold',
    color: '#2ecc71',
    marginBottom: 5,
  },
  modalDistance: {
    fontSize: 16,
    color: '#666',
  },
  modalRoute: {
    marginBottom: 30,
  },
  modalRouteItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 10,
  },
  modalRouteIcon: {
    fontSize: 18,
    marginRight: 15,
    width: 25,
  },
  modalRouteText: {
    fontSize: 16,
    color: '#333',
    flex: 1,
  },
  modalActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  modalRejectButton: {
    backgroundColor: '#e74c3c',
    borderRadius: 12,
    paddingVertical: 15,
    paddingHorizontal: 25,
    flex: 1,
    marginRight: 10,
  },
  modalRejectButtonText: {
    color: 'white',
    fontWeight: 'bold',
    textAlign: 'center',
  },
  modalAcceptButton: {
    backgroundColor: '#2ecc71',
    borderRadius: 12,
    paddingVertical: 15,
    paddingHorizontal: 25,
    flex: 1,
  },
  modalAcceptButtonText: {
    color: 'white',
    fontWeight: 'bold',
    textAlign: 'center',
  },
});

export default DriverHomeScreen;
