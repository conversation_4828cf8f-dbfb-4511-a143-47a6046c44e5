{"name": "internal-ip", "version": "4.3.0", "description": "Get your internal IP address", "license": "MIT", "repository": "sindresorhus/internal-ip", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=6"}, "scripts": {"test": "xo && ava && tsd"}, "files": ["index.js", "index.d.ts"], "keywords": ["ip", "ipv6", "ipv4", "address", "internal", "local", "machine", "system", "net", "gateway"], "dependencies": {"default-gateway": "^4.2.0", "ipaddr.js": "^1.9.0"}, "devDependencies": {"ava": "^1.4.1", "tsd": "^0.7.2", "xo": "^0.24.0"}}