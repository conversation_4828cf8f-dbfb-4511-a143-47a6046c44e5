buildscript {
    // The Android Gradle plugin is only required when opening the android folder stand-alone.
    // This avoids unnecessary downloads and potential conflicts when the library is included as a
    // module dependency in an application project.
    if (project == rootProject) {
        repositories {
            mavenCentral()
            google()
        }

        dependencies {
            classpath("com.android.tools.build:gradle:7.4.2")
            classpath "com.diffplug.spotless:spotless-plugin-gradle:6.17.0"
        }
    }
}

def isNewArchitectureEnabled() {
    // To opt-in for the New Architecture, you can either:
    // - Set `newArchEnabled` to true inside the `gradle.properties` file
    // - Invoke gradle with `-newArchEnabled=true`
    // - Set an environment variable `ORG_GRADLE_PROJECT_newArchEnabled=true`
    return project.hasProperty("newArchEnabled") && project.newArchEnabled == "true"
}

if (isNewArchitectureEnabled()) {
    apply plugin: "com.facebook.react"
}

if (project == rootProject) {
    apply from: 'spotless.gradle'
}

apply plugin: 'com.android.library'

def safeExtGet(prop, fallback) {
    rootProject.ext.has(prop) ? rootProject.ext.get(prop) : fallback
}

android {
    compileSdkVersion safeExtGet('compileSdkVersion', 28)
    def agpVersion = com.android.Version.ANDROID_GRADLE_PLUGIN_VERSION
    if (agpVersion.tokenize('.')[0].toInteger() >= 7) {
        namespace "com.horcrux.svg"
    }
    if (agpVersion.tokenize('.')[0].toInteger() >= 8) {
        buildFeatures {
            buildConfig = true
        }
    }
    // Used to override the NDK path/version on internal CI or by allowing
    // users to customize the NDK path/version from their root project (e.g. for M1 support)
    if (rootProject.hasProperty("ndkPath")) {
        ndkPath rootProject.ext.ndkPath
    }
    if (rootProject.hasProperty("ndkVersion")) {
        ndkVersion rootProject.ext.ndkVersion
    }

    defaultConfig {
        minSdkVersion safeExtGet('minSdkVersion', 16)
        //noinspection OldTargetApi
        targetSdkVersion safeExtGet('targetSdkVersion', 28)
        buildConfigField "boolean", "IS_NEW_ARCHITECTURE_ENABLED", isNewArchitectureEnabled().toString()

        consumerProguardFiles 'proguard-rules.pro'
    }
    lintOptions {
        abortOnError false
    }

    sourceSets.main {
        java {
            if (!isNewArchitectureEnabled()) {
                srcDirs += [
                    "src/paper/java",
                    "build/generated/source/codegen/java"
                ]
            }
        }
    }
}

repositories {
    mavenCentral()
    mavenLocal()
    google()
    maven {
        // All of React Native (JS, Obj-C sources, Android binaries) is installed from npm
        url "$rootDir/../node_modules/react-native/android"
    }
}

dependencies {
    implementation 'com.facebook.react:react-native:+'
}
