/**
 * Pantalla de Login - MACLAREN
 * Autenticación de usuarios (Pasajeros y Conductores)
 */

import React, {useState, useEffect} from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  Alert,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
  StatusBar,
} from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import * as Animatable from 'react-native-animatable';
import {StackNavigationProp} from '@react-navigation/stack';
import {RouteProp} from '@react-navigation/native';

type LoginScreenProps = {
  navigation: StackNavigationProp<any>;
  route: RouteProp<any>;
  onLogin: (userData: any, userType: string) => void;
};

const LoginScreen: React.FC<LoginScreenProps> = ({navigation, route, onLogin}) => {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const userType = route.params?.userType || 'passenger';

  useEffect(() => {
    StatusBar.setBarStyle('light-content');
    StatusBar.setBackgroundColor('#ff6b35');
  }, []);

  const handleLogin = async () => {
    if (!email.trim() || !password.trim()) {
      Alert.alert('Error', 'Por favor completa todos los campos');
      return;
    }

    if (!isValidEmail(email)) {
      Alert.alert('Error', 'Por favor ingresa un email válido');
      return;
    }

    setIsLoading(true);

    try {
      // Simular llamada a API (reemplazar con API real)
      const response = await fetch('http://localhost:3002/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email: email.toLowerCase().trim(),
          password,
          userType,
        }),
      });

      const result = await response.json();

      if (result.success) {
        const userData = {
          id: result.user.id,
          name: result.user.name,
          email: result.user.email,
          phone: result.user.phone,
          userType,
        };

        onLogin(userData, userType);
      } else {
        Alert.alert('Error', result.message || 'Credenciales incorrectas');
      }
    } catch (error) {
      console.error('Error en login:', error);
      
      // Para desarrollo, permitir login sin backend
      if (__DEV__) {
        const mockUserData = {
          id: `${userType}_${Date.now()}`,
          name: userType === 'passenger' ? 'María González' : 'Juan Pérez',
          email,
          phone: '+57 ************',
          userType,
        };
        onLogin(mockUserData, userType);
      } else {
        Alert.alert('Error', 'No se pudo conectar al servidor');
      }
    } finally {
      setIsLoading(false);
    }
  };

  const isValidEmail = (email: string) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  };

  const getUserTypeInfo = () => {
    if (userType === 'driver') {
      return {
        icon: '🏍️',
        title: 'Conductor',
        subtitle: 'Inicia sesión para recibir viajes',
        color: '#2c3e50',
      };
    }
    return {
      icon: '🧑‍💼',
      title: 'Pasajero',
      subtitle: 'Inicia sesión para solicitar viajes',
      color: '#ff6b35',
    };
  };

  const userInfo = getUserTypeInfo();

  return (
    <LinearGradient
      colors={userType === 'driver' ? ['#2c3e50', '#3498db'] : ['#ff6b35', '#f7931e']}
      style={styles.container}>
      
      <StatusBar barStyle="light-content" backgroundColor={userInfo.color} />
      
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={styles.keyboardContainer}>
        
        <ScrollView contentContainerStyle={styles.scrollContainer}>
          
          {/* Header */}
          <Animatable.View 
            animation="fadeInDown" 
            duration={800}
            style={styles.header}>
            <TouchableOpacity
              style={styles.backButton}
              onPress={() => navigation.goBack()}>
              <Text style={styles.backButtonText}>←</Text>
            </TouchableOpacity>
            <Text style={styles.userIcon}>{userInfo.icon}</Text>
            <Text style={styles.title}>{userInfo.title}</Text>
            <Text style={styles.subtitle}>{userInfo.subtitle}</Text>
          </Animatable.View>

          {/* Formulario */}
          <Animatable.View 
            animation="fadeInUp" 
            duration={800}
            delay={300}
            style={styles.formContainer}>
            
            <View style={styles.inputContainer}>
              <Text style={styles.inputLabel}>Email</Text>
              <TextInput
                style={styles.input}
                placeholder="<EMAIL>"
                placeholderTextColor="rgba(255,255,255,0.7)"
                value={email}
                onChangeText={setEmail}
                keyboardType="email-address"
                autoCapitalize="none"
                autoCorrect={false}
              />
            </View>

            <View style={styles.inputContainer}>
              <Text style={styles.inputLabel}>Contraseña</Text>
              <TextInput
                style={styles.input}
                placeholder="Tu contraseña"
                placeholderTextColor="rgba(255,255,255,0.7)"
                value={password}
                onChangeText={setPassword}
                secureTextEntry
                autoCapitalize="none"
              />
            </View>

            <TouchableOpacity
              style={[styles.loginButton, isLoading && styles.loginButtonDisabled]}
              onPress={handleLogin}
              disabled={isLoading}>
              <Text style={styles.loginButtonText}>
                {isLoading ? 'Iniciando sesión...' : 'Iniciar Sesión'}
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.forgotPasswordButton}
              onPress={() => Alert.alert('Info', 'Función en desarrollo')}>
              <Text style={styles.forgotPasswordText}>
                ¿Olvidaste tu contraseña?
              </Text>
            </TouchableOpacity>

          </Animatable.View>

          {/* Footer */}
          <Animatable.View 
            animation="fadeIn" 
            duration={800}
            delay={600}
            style={styles.footer}>
            <Text style={styles.footerText}>¿No tienes cuenta?</Text>
            <TouchableOpacity
              onPress={() => navigation.navigate('Register', {userType})}>
              <Text style={styles.registerText}>Regístrate aquí</Text>
            </TouchableOpacity>
          </Animatable.View>

        </ScrollView>
      </KeyboardAvoidingView>
    </LinearGradient>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  keyboardContainer: {
    flex: 1,
  },
  scrollContainer: {
    flexGrow: 1,
    paddingHorizontal: 20,
  },
  header: {
    alignItems: 'center',
    paddingTop: 60,
    paddingBottom: 40,
  },
  backButton: {
    position: 'absolute',
    top: 60,
    left: 0,
    padding: 10,
  },
  backButtonText: {
    fontSize: 24,
    color: 'white',
    fontWeight: 'bold',
  },
  userIcon: {
    fontSize: 80,
    marginBottom: 20,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: 'white',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    color: 'white',
    opacity: 0.9,
    textAlign: 'center',
  },
  formContainer: {
    flex: 1,
    justifyContent: 'center',
  },
  inputContainer: {
    marginBottom: 20,
  },
  inputLabel: {
    fontSize: 16,
    color: 'white',
    marginBottom: 8,
    fontWeight: '600',
  },
  input: {
    backgroundColor: 'rgba(255,255,255,0.2)',
    borderRadius: 12,
    padding: 16,
    fontSize: 16,
    color: 'white',
    borderWidth: 1,
    borderColor: 'rgba(255,255,255,0.3)',
  },
  loginButton: {
    backgroundColor: 'rgba(255,255,255,0.2)',
    borderRadius: 12,
    padding: 18,
    alignItems: 'center',
    marginTop: 20,
    borderWidth: 2,
    borderColor: 'rgba(255,255,255,0.3)',
  },
  loginButtonDisabled: {
    opacity: 0.6,
  },
  loginButtonText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: 'white',
  },
  forgotPasswordButton: {
    alignItems: 'center',
    marginTop: 20,
  },
  forgotPasswordText: {
    fontSize: 14,
    color: 'white',
    opacity: 0.8,
  },
  footer: {
    alignItems: 'center',
    paddingBottom: 40,
    paddingTop: 20,
  },
  footerText: {
    fontSize: 14,
    color: 'white',
    opacity: 0.8,
    marginBottom: 8,
  },
  registerText: {
    fontSize: 16,
    color: 'white',
    fontWeight: 'bold',
    textDecorationLine: 'underline',
  },
});

export default LoginScreen;
