"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.default = void 0;
//@ts-ignore
var UnimplementedView_1 = require("react-native-web/dist/modules/UnimplementedView");
Object.defineProperty(exports, "default", { enumerable: true, get: function () { return __importDefault(UnimplementedView_1).default; } });
