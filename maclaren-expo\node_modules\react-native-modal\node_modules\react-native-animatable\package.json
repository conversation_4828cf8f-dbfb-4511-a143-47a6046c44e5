{"name": "react-native-animatable", "version": "1.3.3", "description": "Easy to use declarative transitions and animations for React Native", "typings": "typings/react-native-animatable.d.ts", "main": "index.js", "scripts": {"jest": "./node_modules/.bin/jest", "jest:watch": "npm run jest -- --watch", "lint": "./node_modules/.bin/eslint ./*.js", "test": "npm run lint && npm run jest", "format": "./node_modules/.bin/prettier --write {,definitions/,__tests__/}*.js"}, "keywords": ["react-native", "react-component", "react-native-component", "react", "mobile", "ios", "android", "ui", "fade", "bounce", "slide", "animatable", "transition", "animation"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "homepage": "https://github.com/oblador/react-native-animatable", "bugs": {"url": "https://github.com/oblador/react-native-animatable/issues"}, "repository": {"type": "git", "url": "git://github.com/oblador/react-native-animatable.git"}, "license": "MIT", "jest": {"preset": "react-native", "modulePathIgnorePatterns": ["<rootDir>/Examples/"], "testPathIgnorePatterns": ["<rootDir>/Examples/"]}, "devDependencies": {"@babel/core": "^7.6.4", "@babel/runtime": "^7.6.3", "babel-eslint": "^10.0.3", "babel-jest": "^24.9.0", "babel-preset-react-native": "4.0.0", "eslint": "6.1.0", "eslint-config-airbnb": "18.0.1", "eslint-config-prettier": "^6.4.0", "eslint-plugin-import": "^2.18.2", "eslint-plugin-jsx-a11y": "^6.2.3", "eslint-plugin-prettier": "^2.6.0", "eslint-plugin-react": "^7.14.3", "eslint-plugin-react-hooks": "^1.7.0", "jest": "^24.9.0", "prettier": "^1.18.2", "react": "16.9.0", "react-native": "0.61.2", "react-test-renderer": "16.9.0"}, "dependencies": {"prop-types": "^15.7.2"}}