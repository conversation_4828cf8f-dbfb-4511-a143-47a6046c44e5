{"name": "maclaren-expo", "version": "1.0.0", "main": "node_modules/expo/AppEntry.js", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web"}, "dependencies": {"@expo/vector-icons": "^14.0.0", "@react-navigation/bottom-tabs": "^6.5.11", "@react-navigation/native": "^6.1.9", "@react-navigation/stack": "^6.3.20", "expo": "~50.0.0", "expo-av": "~13.10.5", "expo-camera": "~14.1.3", "expo-constants": "~15.4.5", "expo-device": "~5.9.3", "expo-font": "~11.10.3", "expo-haptics": "~12.8.1", "expo-image-picker": "~14.7.1", "expo-linear-gradient": "~12.7.2", "expo-location": "~16.5.5", "expo-notifications": "~0.27.6", "expo-secure-store": "~12.8.1", "expo-splash-screen": "~0.26.4", "expo-status-bar": "~1.11.1", "react": "18.2.0", "react-native": "0.73.6", "react-native-animatable": "^1.4.0", "react-native-gesture-handler": "~2.14.1", "react-native-maps": "1.10.0", "react-native-modal": "^13.0.1", "react-native-safe-area-context": "4.8.2", "react-native-screens": "~3.29.0", "react-native-svg": "14.1.0", "socket.io-client": "^4.7.4", "@react-native-async-storage/async-storage": "1.21.0", "typescript": "^5.3.0", "@types/react": "~18.2.45"}, "devDependencies": {"@babel/core": "^7.20.0"}, "private": true}