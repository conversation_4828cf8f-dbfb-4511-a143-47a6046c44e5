/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 * @format
 * @flow strict
 */

'use strict';

// NOTE: Hmm... I don't think declaring variables within this module actually
// accomplishes anything besides documenting that these globals are expected to
// exist. So I think the correct "fix" to this lint warning is to delete this
// entire file. But in lieu of doing that... no harm for now in keeping this
// file around, even if it is only for documentation purposes. ¯\_(ツ)_/¯

/* eslint-disable no-unused-vars */

declare var __DEV__: boolean;

declare var __REACT_DEVTOOLS_GLOBAL_HOOK__: any; /*?{
  inject: ?((stuff: Object) => void)
};*/
