{"version": 3, "sources": ["EnableNewWebImplementation.ts"], "names": ["Platform", "useNewWebImplementation", "getWasCalled", "enableExperimentalWebImplementation", "_shouldEnable", "enableLegacyWebImplementation", "shouldUseLegacyImplementation", "OS", "console", "error", "isNewWebImplementationEnabled"], "mappings": "AAAA,SAASA,QAAT,QAAyB,cAAzB;AAEA,IAAIC,uBAAuB,GAAG,IAA9B;AACA,IAAIC,YAAY,GAAG,KAAnB;AAEA,OAAO,SAASC,mCAAT,CACLC,aAAa,GAAG,IADX,EAEC,CACN;AACD;AAED,OAAO,SAASC,6BAAT,CACLC,6BAA6B,GAAG,IAD3B,EAEC;AACN,MACEN,QAAQ,CAACO,EAAT,KAAgB,KAAhB,IACAN,uBAAuB,KAAK,CAACK,6BAF/B,EAGE;AACA;AACD;;AAED,MAAIJ,YAAJ,EAAkB;AAChBM,IAAAA,OAAO,CAACC,KAAR,CACE,mLADF;AAGA;AACD;;AAEDR,EAAAA,uBAAuB,GAAG,CAACK,6BAA3B;AACD;AAED,OAAO,SAASI,6BAAT,GAAkD;AACvDR,EAAAA,YAAY,GAAG,IAAf;AACA,SAAOD,uBAAP;AACD", "sourcesContent": ["import { Platform } from 'react-native';\n\nlet useNewWebImplementation = true;\nlet getWasCalled = false;\n\nexport function enableExperimentalWebImplementation(\n  _shouldEnable = true\n): void {\n  // NO-OP since the new implementation is now the default\n}\n\nexport function enableLegacyWebImplementation(\n  shouldUseLegacyImplementation = true\n): void {\n  if (\n    Platform.OS !== 'web' ||\n    useNewWebImplementation === !shouldUseLegacyImplementation\n  ) {\n    return;\n  }\n\n  if (getWasCalled) {\n    console.error(\n      'Some parts of this application have already started using the new gesture handler implementation. No changes will be applied. You can try enabling legacy implementation earlier.'\n    );\n    return;\n  }\n\n  useNewWebImplementation = !shouldUseLegacyImplementation;\n}\n\nexport function isNewWebImplementationEnabled(): boolean {\n  getWasCalled = true;\n  return useNewWebImplementation;\n}\n"]}