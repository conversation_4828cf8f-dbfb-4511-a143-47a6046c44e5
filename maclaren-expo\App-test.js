import React from 'react';
import { View, Text, TouchableOpacity, StyleSheet, Alert } from 'react-native';
import { StatusBar } from 'expo-status-bar';

export default function App() {
  return (
    <View style={styles.container}>
      <StatusBar style="auto" />
      
      <Text style={styles.title}>🏍️ MACLAREN</Text>
      <Text style={styles.subtitle}>Tu app de transporte en moto</Text>
      
      <TouchableOpacity 
        style={styles.button}
        onPress={() => Alert.alert('¡Funciona!', 'MACLAREN está funcionando correctamente')}>
        <Text style={styles.buttonText}>🚀 Probar App</Text>
      </TouchableOpacity>
      
      <Text style={styles.footer}>Versión de prueba - Expo Go</Text>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#ff6b35',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 20,
  },
  title: {
    fontSize: 48,
    fontWeight: 'bold',
    color: 'white',
    marginBottom: 10,
  },
  subtitle: {
    fontSize: 18,
    color: 'white',
    marginBottom: 50,
    textAlign: 'center',
  },
  button: {
    backgroundColor: 'white',
    paddingHorizontal: 30,
    paddingVertical: 15,
    borderRadius: 25,
    marginBottom: 30,
  },
  buttonText: {
    color: '#ff6b35',
    fontSize: 18,
    fontWeight: 'bold',
  },
  footer: {
    color: 'rgba(255, 255, 255, 0.8)',
    fontSize: 14,
  },
});
