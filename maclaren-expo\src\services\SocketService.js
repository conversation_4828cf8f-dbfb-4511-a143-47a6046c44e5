/**
 * Servicio de Socket.IO para MACLAREN - Expo
 * Maneja la comunicación en tiempo real entre conductores y pasajeros
 */

import io from 'socket.io-client';
import { Alert } from 'react-native';

class SocketServiceClass {
  constructor() {
    this.socket = null;
    this.currentUser = null;
    this.isConnected = false;
    this.reconnectAttempts = 0;
    this.maxReconnectAttempts = 5;
    this.callbacks = {};
  }

  connect(user) {
    if (this.socket && this.isConnected) {
      console.log('Socket ya está conectado');
      return;
    }

    this.currentUser = user;
    
    // Conectar al servidor (cambiar URL por tu servidor)
    this.socket = io('http://192.168.1.100:3002', {
      transports: ['websocket'],
      timeout: 10000,
      forceNew: true,
    });

    this.setupEventListeners();
    
    console.log(`🔌 Conectando socket para ${user.name} (${user.userType})`);
  }

  setupEventListeners() {
    if (!this.socket) return;

    // Eventos de conexión
    this.socket.on('connect', () => {
      console.log('✅ Socket conectado');
      this.isConnected = true;
      this.reconnectAttempts = 0;
      
      // Unirse con datos del usuario
      if (this.currentUser) {
        this.socket.emit('join', {
          userId: this.currentUser.id,
          userType: this.currentUser.userType,
          name: this.currentUser.name,
        });
      }

      this.emit('connectionChange', true);
    });

    this.socket.on('disconnect', (reason) => {
      console.log('❌ Socket desconectado:', reason);
      this.isConnected = false;
      this.emit('connectionChange', false);
      
      // Intentar reconectar automáticamente
      if (reason === 'io server disconnect') {
        this.reconnect();
      }
    });

    this.socket.on('connect_error', (error) => {
      console.error('❌ Error de conexión:', error);
      this.isConnected = false;
      this.emit('connectionChange', false);
      this.reconnect();
    });

    // Eventos de la aplicación
    this.socket.on('newTripRequest', (tripData) => {
      console.log('🚗 Nueva solicitud de viaje:', tripData);
      this.emit('newTripRequest', tripData);
    });

    this.socket.on('tripAccepted', (data) => {
      console.log('✅ Viaje aceptado:', data);
      this.emit('tripAccepted', data);
    });

    this.socket.on('newMessage', (messageData) => {
      console.log('💬 Nuevo mensaje:', messageData);
      this.emit('newMessage', messageData);
    });

    this.socket.on('locationUpdated', (locationData) => {
      console.log('📍 Ubicación actualizada:', locationData);
      this.emit('locationUpdated', locationData);
    });

    this.socket.on('tripStatusUpdated', (statusData) => {
      console.log('🔄 Estado del viaje actualizado:', statusData);
      this.emit('tripStatusUpdated', statusData);
    });

    this.socket.on('tripCancelled', (tripId) => {
      console.log('❌ Viaje cancelado:', tripId);
      this.emit('tripCancelled', tripId);
    });

    this.socket.on('driverOnline', (driverData) => {
      console.log('🟢 Conductor en línea:', driverData);
      this.emit('driverOnline', driverData);
    });

    this.socket.on('driverOffline', (driverId) => {
      console.log('🔴 Conductor fuera de línea:', driverId);
      this.emit('driverOffline', driverId);
    });
  }

  reconnect() {
    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      console.log('❌ Máximo de intentos de reconexión alcanzado');
      Alert.alert(
        'Error de Conexión',
        'No se pudo conectar al servidor. Verifica tu conexión a internet.',
      );
      return;
    }

    this.reconnectAttempts++;
    console.log(`🔄 Reintentando conexión (${this.reconnectAttempts}/${this.maxReconnectAttempts})`);
    
    setTimeout(() => {
      if (this.currentUser) {
        this.connect(this.currentUser);
      }
    }, 2000 * this.reconnectAttempts);
  }

  disconnect() {
    if (this.socket) {
      this.socket.disconnect();
      this.socket = null;
    }
    this.isConnected = false;
    this.currentUser = null;
    this.reconnectAttempts = 0;
    this.callbacks = {};
    console.log('🔌 Socket desconectado manualmente');
  }

  // Métodos para enviar eventos
  emit(event, data) {
    if (this.callbacks[event]) {
      this.callbacks[event].forEach(callback => callback(data));
    }
  }

  // Métodos para registrar callbacks
  on(event, callback) {
    if (!this.callbacks[event]) {
      this.callbacks[event] = [];
    }
    this.callbacks[event].push(callback);
  }

  off(event, callback) {
    if (this.callbacks[event]) {
      this.callbacks[event] = this.callbacks[event].filter(cb => cb !== callback);
    }
  }

  // Métodos específicos de la aplicación
  requestTrip(tripData) {
    if (!this.socket || !this.isConnected) {
      Alert.alert('Error', 'No hay conexión al servidor');
      return;
    }

    this.socket.emit('requestTrip', {
      id: `trip_${Date.now()}`,
      passengerId: this.currentUser?.id,
      passengerName: this.currentUser?.name,
      ...tripData,
    });
  }

  acceptTrip(tripId) {
    if (!this.socket || !this.isConnected) {
      Alert.alert('Error', 'No hay conexión al servidor');
      return;
    }

    this.socket.emit('acceptTrip', {
      tripId,
      driverId: this.currentUser?.id,
      driverName: this.currentUser?.name,
    });
  }

  rejectTrip(tripId) {
    if (!this.socket || !this.isConnected) {
      return;
    }

    this.socket.emit('rejectTrip', {
      tripId,
      driverId: this.currentUser?.id,
    });
  }

  sendMessage(tripId, message) {
    if (!this.socket || !this.isConnected || !this.currentUser) {
      Alert.alert('Error', 'No hay conexión al servidor');
      return;
    }

    const messageData = {
      tripId,
      senderId: this.currentUser.id,
      senderType: this.currentUser.userType,
      senderName: this.currentUser.name,
      message,
      timestamp: new Date().toISOString(),
    };

    this.socket.emit('sendMessage', messageData);
  }

  updateLocation(tripId, latitude, longitude) {
    if (!this.socket || !this.isConnected || !this.currentUser) {
      return;
    }

    const locationData = {
      tripId,
      userId: this.currentUser.id,
      userType: this.currentUser.userType,
      latitude,
      longitude,
      timestamp: new Date().toISOString(),
    };

    this.socket.emit('updateLocation', locationData);
  }

  updateTripStatus(tripId, status) {
    if (!this.socket || !this.isConnected || !this.currentUser) {
      Alert.alert('Error', 'No hay conexión al servidor');
      return;
    }

    this.socket.emit('updateTripStatus', {
      tripId,
      status,
      userId: this.currentUser.id,
    });
  }

  setDriverOnline(location) {
    if (!this.socket || !this.isConnected || !this.currentUser) {
      return;
    }

    this.socket.emit('driverOnline', {
      driverId: this.currentUser.id,
      driverName: this.currentUser.name,
      location,
      available: true,
    });
  }

  setDriverOffline() {
    if (!this.socket || !this.isConnected || !this.currentUser) {
      return;
    }

    this.socket.emit('driverOffline', {
      driverId: this.currentUser.id,
    });
  }

  completeTrip(tripId, fare) {
    if (!this.socket || !this.isConnected || !this.currentUser) {
      return;
    }

    this.socket.emit('completeTrip', {
      tripId,
      driverId: this.currentUser.id,
      fare,
      completedAt: new Date().toISOString(),
    });
  }

  cancelTrip(tripId, reason) {
    if (!this.socket || !this.isConnected || !this.currentUser) {
      return;
    }

    this.socket.emit('cancelTrip', {
      tripId,
      userId: this.currentUser.id,
      userType: this.currentUser.userType,
      reason,
      cancelledAt: new Date().toISOString(),
    });
  }

  // Getters
  get connected() {
    return this.isConnected;
  }

  get user() {
    return this.currentUser;
  }
}

export const SocketService = new SocketServiceClass();
