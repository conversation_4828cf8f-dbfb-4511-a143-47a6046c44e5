/**
 * Navegador para Pasajeros - MACLAREN Expo
 */

import React from 'react';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import { createStackNavigator } from '@react-navigation/stack';
import { Text, StyleSheet } from 'react-native';

// Pantallas temporales simples
import HomeScreen from '../screens/passenger/HomeScreen';
import TripsScreen from '../screens/passenger/TripsScreen';
import ProfileScreen from '../screens/passenger/ProfileScreen';

const Tab = createBottomTabNavigator();
const Stack = createStackNavigator();

const PassengerNavigator = ({ onLogout }) => {
  return (
    <Tab.Navigator
      screenOptions={({ route }) => ({
        headerShown: false,
        tabBarStyle: styles.tabBar,
        tabBarActiveTintColor: '#ff6b35',
        tabBarInactiveTintColor: '#999',
        tabBarLabelStyle: styles.tabBarLabel,
        tabBarIcon: ({ focused, color }) => {
          let iconText = '';
          
          switch (route.name) {
            case 'Home':
              iconText = '🏠';
              break;
            case 'Trips':
              iconText = '📋';
              break;
            case 'Profile':
              iconText = '👤';
              break;
          }
          
          return (
            <Text style={[styles.tabIcon, { color: focused ? '#ff6b35' : '#999' }]}>
              {iconText}
            </Text>
          );
        },
      })}>
      
      <Tab.Screen 
        name="Home" 
        component={HomeScreen}
        options={{
          tabBarLabel: 'Inicio',
        }}
      />
      
      <Tab.Screen 
        name="Trips" 
        component={TripsScreen}
        options={{
          tabBarLabel: 'Viajes',
        }}
      />
      
      <Tab.Screen 
        name="Profile" 
        options={{
          tabBarLabel: 'Perfil',
        }}>
        {() => <ProfileScreen onLogout={onLogout} />}
      </Tab.Screen>
      
    </Tab.Navigator>
  );
};

const styles = StyleSheet.create({
  tabBar: {
    backgroundColor: 'white',
    borderTopWidth: 1,
    borderTopColor: '#e9ecef',
    paddingBottom: 5,
    paddingTop: 5,
    height: 60,
  },
  tabBarLabel: {
    fontSize: 12,
    fontWeight: '600',
    marginTop: 2,
  },
  tabIcon: {
    fontSize: 24,
    marginBottom: 2,
  },
});

export default PassengerNavigator;
