{"version": 3, "sources": ["getShadowNodeFromRef.ts"], "names": ["findHostInstance_DEPRECATED", "getShadowNodeFromRef", "ref", "undefined", "require", "e", "_ref", "_internalInstanceHandle", "stateNode", "node"], "mappings": "AAAA;AACA;AACA;AACA;AACA,IAAIA,2BAAJ;AAEA,OAAO,SAASC,oBAAT,CAA8BC,GAA9B,EAAwC;AAC7C;AACA,MAAIF,2BAA2B,KAAKG,SAApC,EAA+C;AAC7C,QAAI;AACFH,MAAAA,2BAA2B,GACzB;AACAI,MAAAA,OAAO,CAAC,mDAAD,CAAP,CAA6DJ,2BAF/D;AAGD,KAJD,CAIE,OAAOK,CAAP,EAAU;AACVL,MAAAA,2BAA2B,GAAIM,IAAD,IAAe,IAA7C;AACD;AACF,GAV4C,CAY7C;;;AACA,SAAON,2BAA2B,CAACE,GAAD,CAA3B,CAAiCK,uBAAjC,CAAyDC,SAAzD,CACJC,IADH;AAED", "sourcesContent": ["// Used by G<PERSON>ure<PERSON>ete<PERSON> (unsupported on web at the moment) to check whether the\n// attached view may get flattened on <PERSON>abric. This implementation causes errors\n// on web due to the static resolution of `require` statements by webpack breaking\n// the conditional importing. Solved by making .web file.\nlet findHostInstance_DEPRECATED: (ref: any) => void;\n\nexport function getShadowNodeFromRef(ref: any) {\n  // load findHostInstance_DEPRECATED lazily because it may not be available before render\n  if (findHostInstance_DEPRECATED === undefined) {\n    try {\n      findHostInstance_DEPRECATED =\n        // eslint-disable-next-line @typescript-eslint/no-var-requires\n        require('react-native/Libraries/Renderer/shims/ReactFabric').findHostInstance_DEPRECATED;\n    } catch (e) {\n      findHostInstance_DEPRECATED = (_ref: any) => null;\n    }\n  }\n\n  // @ts-ignore Fabric\n  return findHostInstance_DEPRECATED(ref)._internalInstanceHandle.stateNode\n    .node;\n}\n"]}