{"version": 3, "sources": ["NodeManager.ts"], "names": ["gestures", "<PERSON><PERSON><PERSON><PERSON>", "tag", "Error", "createGestureHandler", "handlerTag", "handler", "dropGestureHandler", "destroy", "getNodes"], "mappings": "AAGA,MAAMA,QAGL,GAAG,EAHJ;AAKA,OAAO,SAASC,UAAT,CAAoBC,GAApB,EAAiC;AACtC,MAAIA,GAAG,IAAIF,QAAX,EAAqB;AACnB,WAAOA,QAAQ,CAACE,GAAD,CAAf;AACD;;AAED,QAAM,IAAIC,KAAJ,CAAW,sBAAqBD,GAAI,EAApC,CAAN;AACD;AAED,OAAO,SAASE,oBAAT,CACLC,UADK,EAELC,OAFK,EAGL;AACA,MAAID,UAAU,IAAIL,QAAlB,EAA4B;AAC1B,UAAM,IAAIG,KAAJ,CAAW,oBAAmBE,UAAW,iBAAzC,CAAN;AACD;;AACDL,EAAAA,QAAQ,CAACK,UAAD,CAAR,GAAuBC,OAAvB,CAJA,CAKA;;AACAN,EAAAA,QAAQ,CAACK,UAAD,CAAR,CAAqBA,UAArB,GAAkCA,UAAlC;AACD;AAED,OAAO,SAASE,kBAAT,CAA4BF,UAA5B,EAAgD;AACrD;AACA;AACA,MAAI,EAAEA,UAAU,IAAIL,QAAhB,CAAJ,EAA+B;AAC7B;AACD;;AACDC,EAAAA,UAAU,CAACI,UAAD,CAAV,CAAuBG,OAAvB,GANqD,CAOrD;;AACA,SAAOR,QAAQ,CAACK,UAAD,CAAf;AACD;AAED,OAAO,SAASI,QAAT,GAAoB;AACzB,SAAO,EAAE,GAAGT;AAAL,GAAP;AACD", "sourcesContent": ["import { ValueOf } from '../typeUtils';\nimport { HammerGestures } from '../RNGestureHandlerModule.web';\n\nconst gestures: Record<\n  number,\n  InstanceType<ValueOf<typeof HammerGestures>>\n> = {};\n\nexport function getHandler(tag: number) {\n  if (tag in gestures) {\n    return gestures[tag];\n  }\n\n  throw new Error(`No handler for tag ${tag}`);\n}\n\nexport function createGestureHandler(\n  handlerTag: number,\n  handler: InstanceType<ValueOf<typeof HammerGestures>>\n) {\n  if (handlerTag in gestures) {\n    throw new Error(`Handler with tag ${handlerTag} already exists`);\n  }\n  gestures[handlerTag] = handler;\n  // @ts-ignore no types for web handlers yet\n  gestures[handlerTag].handlerTag = handlerTag;\n}\n\nexport function dropGestureHandler(handlerTag: number) {\n  // Since React 18, there are cases where componentWillUnmount gets called twice in a row\n  // so skip this if the tag was already removed.\n  if (!(handlerTag in gestures)) {\n    return;\n  }\n  getHandler(handlerTag).destroy();\n  // eslint-disable-next-line @typescript-eslint/no-dynamic-delete\n  delete gestures[handlerTag];\n}\n\nexport function getNodes() {\n  return { ...gestures };\n}\n"]}