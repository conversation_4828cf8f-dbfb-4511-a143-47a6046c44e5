{"version": 3, "names": ["_codegenNativeComponent", "_interopRequireDefault", "require", "obj", "__esModule", "default", "_default", "codegenNativeComponent", "interfaceOnly", "exports"], "sources": ["ScreenNativeComponent.ts"], "sourcesContent": ["import codegenNativeComponent from 'react-native/Libraries/Utilities/codegenNativeComponent';\nimport type { ViewProps, ColorValue } from 'react-native';\nimport type {\n  BubblingEventHandler,\n  WithDefault,\n  Int32,\n  Float,\n  Double,\n} from 'react-native/Libraries/Types/CodegenTypes';\n\n// eslint-disable-next-line @typescript-eslint/ban-types\ntype ScreenEvent = Readonly<{}>;\n\ntype ScreenDismissedEvent = Readonly<{\n  dismissCount: Int32;\n}>;\n\ntype TransitionProgressEvent = Readonly<{\n  progress: Double;\n  closing: Int32;\n  goingForward: Int32;\n}>;\n\ntype HeaderHeightChangeEvent = Readonly<{\n  headerHeight: Double;\n}>;\n\ntype GestureResponseDistanceType = Readonly<{\n  start: Float;\n  end: Float;\n  top: Float;\n  bottom: Float;\n}>;\n\ntype StackPresentation =\n  | 'push'\n  | 'modal'\n  | 'transparentModal'\n  | 'fullScreenModal'\n  | 'formSheet'\n  | 'containedModal'\n  | 'containedTransparentModal';\n\ntype StackAnimation =\n  | 'default'\n  | 'flip'\n  | 'simple_push'\n  | 'none'\n  | 'fade'\n  | 'slide_from_right'\n  | 'slide_from_left'\n  | 'slide_from_bottom'\n  | 'fade_from_bottom'\n  | 'ios';\n\ntype SwipeDirection = 'vertical' | 'horizontal';\n\ntype ReplaceAnimation = 'pop' | 'push';\n\ntype SheetDetentTypes = 'large' | 'medium' | 'all';\n\nexport interface NativeProps extends ViewProps {\n  onAppear?: BubblingEventHandler<ScreenEvent>;\n  onDisappear?: BubblingEventHandler<ScreenEvent>;\n  onDismissed?: BubblingEventHandler<ScreenDismissedEvent>;\n  onNativeDismissCancelled?: BubblingEventHandler<ScreenDismissedEvent>;\n  onWillAppear?: BubblingEventHandler<ScreenEvent>;\n  onWillDisappear?: BubblingEventHandler<ScreenEvent>;\n  onHeaderHeightChange?: BubblingEventHandler<HeaderHeightChangeEvent>;\n  onTransitionProgress?: BubblingEventHandler<TransitionProgressEvent>;\n  onGestureCancel?: BubblingEventHandler<ScreenEvent>;\n  sheetAllowedDetents?: WithDefault<SheetDetentTypes, 'large'>;\n  sheetLargestUndimmedDetent?: WithDefault<SheetDetentTypes, 'all'>;\n  sheetGrabberVisible?: WithDefault<boolean, false>;\n  sheetCornerRadius?: WithDefault<Float, -1.0>;\n  sheetExpandsWhenScrolledToEdge?: WithDefault<boolean, false>;\n  customAnimationOnSwipe?: boolean;\n  fullScreenSwipeEnabled?: boolean;\n  homeIndicatorHidden?: boolean;\n  preventNativeDismiss?: boolean;\n  gestureEnabled?: WithDefault<boolean, true>;\n  statusBarColor?: ColorValue;\n  statusBarHidden?: boolean;\n  screenOrientation?: string;\n  statusBarAnimation?: string;\n  statusBarStyle?: string;\n  statusBarTranslucent?: boolean;\n  gestureResponseDistance?: GestureResponseDistanceType;\n  stackPresentation?: WithDefault<StackPresentation, 'push'>;\n  stackAnimation?: WithDefault<StackAnimation, 'default'>;\n  transitionDuration?: WithDefault<Int32, 350>;\n  replaceAnimation?: WithDefault<ReplaceAnimation, 'pop'>;\n  swipeDirection?: WithDefault<SwipeDirection, 'horizontal'>;\n  hideKeyboardOnSwipe?: boolean;\n  activityState?: WithDefault<Float, -1.0>;\n  navigationBarColor?: ColorValue;\n  navigationBarHidden?: boolean;\n  nativeBackButtonDismissalEnabled?: boolean;\n  onHeaderBackButtonClicked?: BubblingEventHandler<ScreenEvent>;\n}\n\nexport default codegenNativeComponent<NativeProps>('RNSScreen', {\n  interfaceOnly: true,\n});\n"], "mappings": ";;;;;;AAAA,IAAAA,uBAAA,GAAAC,sBAAA,CAAAC,OAAA;AAA6F,SAAAD,uBAAAE,GAAA,WAAAA,GAAA,IAAAA,GAAA,CAAAC,UAAA,GAAAD,GAAA,KAAAE,OAAA,EAAAF,GAAA;AAU7F;AAAA,IAAAG,QAAA,GA2Fe,IAAAC,+BAAsB,EAAc,WAAW,EAAE;EAC9DC,aAAa,EAAE;AACjB,CAAC,CAAC;AAAAC,OAAA,CAAAJ,OAAA,GAAAC,QAAA"}