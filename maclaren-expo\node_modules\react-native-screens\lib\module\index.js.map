{"version": 3, "names": ["React", "Animated", "View", "Image", "default", "useTransitionProgress", "isSearchBarAvailableForCurrentPlatform", "isNewBackTitleImplementation", "executeNativeBackPress", "ENABLE_SCREENS", "enableScreens", "shouldEnableScreens", "arguments", "length", "undefined", "screensEnabled", "enableFreeze", "shouldEnableReactFreeze", "NativeScreen", "Component", "render", "active", "activityState", "style", "enabled", "rest", "props", "createElement", "_extends", "hidden", "display", "Screen", "createAnimatedComponent", "InnerScreen", "ScreenContext", "createContext", "ScreenContainer", "NativeScreenContainer", "NativeScreenNavigationContainer", "ScreenStack", "FullWindowOverlay", "ScreenStackHeaderBackButtonImage", "resizeMode", "fadeDuration", "ScreenStackHeaderRightView", "ScreenStackHeaderLeftView", "ScreenStackHeaderCenterView", "ScreenStackHeaderSearchBarView", "ScreenStackHeaderConfig", "SearchBar", "ScreenStackHeaderSubview", "shouldUseActivityState"], "sources": ["index.tsx"], "sourcesContent": ["import React, { ReactNode } from 'react';\nimport { Animated, View, ViewProps, ImageProps, Image } from 'react-native';\nimport {\n  ScreenProps,\n  ScreenContainerProps,\n  ScreenStackProps,\n  ScreenStackHeaderConfigProps,\n  HeaderSubviewTypes,\n  SearchBarProps,\n} from './types';\n\nexport * from './types';\nexport { default as useTransitionProgress } from './useTransitionProgress';\nexport {\n  isSearchBarAvailableForCurrentPlatform,\n  isNewBackTitleImplementation,\n  executeNativeBackPress,\n} from './utils';\n\nlet ENABLE_SCREENS = true;\n\nexport function enableScreens(shouldEnableScreens = true): void {\n  ENABLE_SCREENS = shouldEnableScreens;\n}\n\nexport function screensEnabled(): boolean {\n  return ENABLE_SCREENS;\n}\n\n// @ts-ignore function stub, freezing logic is located in index.native.tsx\n// eslint-disable-next-line @typescript-eslint/no-unused-vars\nexport function enableFreeze(shouldEnableReactFreeze = true): void {\n  // noop\n}\n\nexport class NativeScreen extends React.Component<ScreenProps> {\n  render(): JSX.Element {\n    let {\n      active,\n      activityState,\n      style,\n      enabled = ENABLE_SCREENS,\n      ...rest\n    } = this.props;\n\n    if (enabled) {\n      if (active !== undefined && activityState === undefined) {\n        activityState = active !== 0 ? 2 : 0; // change taken from index.native.tsx\n      }\n      return (\n        <View\n          // @ts-expect-error: hidden exists on web, but not in React Native\n          hidden={activityState === 0}\n          style={[style, { display: activityState !== 0 ? 'flex' : 'none' }]}\n          {...rest}\n        />\n      );\n    }\n\n    return <View {...rest} />;\n  }\n}\n\nexport const Screen = Animated.createAnimatedComponent(NativeScreen);\n\nexport const InnerScreen = View;\n\nexport const ScreenContext = React.createContext(Screen);\n\nexport const ScreenContainer: React.ComponentType<ScreenContainerProps> = View;\n\nexport const NativeScreenContainer: React.ComponentType<ScreenContainerProps> =\n  View;\n\nexport const NativeScreenNavigationContainer: React.ComponentType<ScreenContainerProps> =\n  View;\n\nexport const ScreenStack: React.ComponentType<ScreenStackProps> = View;\n\nexport const FullWindowOverlay = View as React.ComponentType<{\n  children: ReactNode;\n}>;\n\nexport const ScreenStackHeaderBackButtonImage = (\n  props: ImageProps\n): JSX.Element => (\n  <View>\n    <Image resizeMode=\"center\" fadeDuration={0} {...props} />\n  </View>\n);\n\nexport const ScreenStackHeaderRightView = (\n  props: React.PropsWithChildren<ViewProps>\n): JSX.Element => <View {...props} />;\n\nexport const ScreenStackHeaderLeftView = (\n  props: React.PropsWithChildren<ViewProps>\n): JSX.Element => <View {...props} />;\n\nexport const ScreenStackHeaderCenterView = (\n  props: React.PropsWithChildren<ViewProps>\n): JSX.Element => <View {...props} />;\n\nexport const ScreenStackHeaderSearchBarView = (\n  props: React.PropsWithChildren<Omit<SearchBarProps, 'ref'>>\n): JSX.Element => <View {...props} />;\n\nexport const ScreenStackHeaderConfig = (\n  props: React.PropsWithChildren<ScreenStackHeaderConfigProps>\n): JSX.Element => <View {...props} />;\n\n// @ts-expect-error: search bar props have no common props with View\nexport const SearchBar: React.ComponentType<SearchBarProps> = View;\n\nexport const ScreenStackHeaderSubview: React.ComponentType<\n  React.PropsWithChildren<ViewProps & { type?: HeaderSubviewTypes }>\n> = View;\n\nexport const shouldUseActivityState = true;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAqB,OAAO;AACxC,SAASC,QAAQ,EAAEC,IAAI,EAAyBC,KAAK,QAAQ,cAAc;AAU3E,cAAc,SAAS;AACvB,SAASC,OAAO,IAAIC,qBAAqB,QAAQ,yBAAyB;AAC1E,SACEC,sCAAsC,EACtCC,4BAA4B,EAC5BC,sBAAsB,QACjB,SAAS;AAEhB,IAAIC,cAAc,GAAG,IAAI;AAEzB,OAAO,SAASC,aAAaA,CAAA,EAAmC;EAAA,IAAlCC,mBAAmB,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,IAAI;EACtDH,cAAc,GAAGE,mBAAmB;AACtC;AAEA,OAAO,SAASI,cAAcA,CAAA,EAAY;EACxC,OAAON,cAAc;AACvB;;AAEA;AACA;AACA,OAAO,SAASO,YAAYA,CAAA,EAAuC;EAAA,IAAtCC,uBAAuB,GAAAL,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,IAAI;AAE3D,CAAC,CADC;;AAGF,OAAO,MAAMM,YAAY,SAASlB,KAAK,CAACmB,SAAS,CAAc;EAC7DC,MAAMA,CAAA,EAAgB;IACpB,IAAI;MACFC,MAAM;MACNC,aAAa;MACbC,KAAK;MACLC,OAAO,GAAGf,cAAc;MACxB,GAAGgB;IACL,CAAC,GAAG,IAAI,CAACC,KAAK;IAEd,IAAIF,OAAO,EAAE;MACX,IAAIH,MAAM,KAAKP,SAAS,IAAIQ,aAAa,KAAKR,SAAS,EAAE;QACvDQ,aAAa,GAAGD,MAAM,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;MACxC;;MACA,oBACErB,KAAA,CAAA2B,aAAA,CAACzB;MACC;MAAA,EAAA0B,QAAA;QACAC,MAAM,EAAEP,aAAa,KAAK,CAAE;QAC5BC,KAAK,EAAE,CAACA,KAAK,EAAE;UAAEO,OAAO,EAAER,aAAa,KAAK,CAAC,GAAG,MAAM,GAAG;QAAO,CAAC;MAAE,GAC/DG,IAAI,CACT,CAAC;IAEN;IAEA,oBAAOzB,KAAA,CAAA2B,aAAA,CAACzB,IAAI,EAAKuB,IAAO,CAAC;EAC3B;AACF;AAEA,OAAO,MAAMM,MAAM,GAAG9B,QAAQ,CAAC+B,uBAAuB,CAACd,YAAY,CAAC;AAEpE,OAAO,MAAMe,WAAW,GAAG/B,IAAI;AAE/B,OAAO,MAAMgC,aAAa,gBAAGlC,KAAK,CAACmC,aAAa,CAACJ,MAAM,CAAC;AAExD,OAAO,MAAMK,eAA0D,GAAGlC,IAAI;AAE9E,OAAO,MAAMmC,qBAAgE,GAC3EnC,IAAI;AAEN,OAAO,MAAMoC,+BAA0E,GACrFpC,IAAI;AAEN,OAAO,MAAMqC,WAAkD,GAAGrC,IAAI;AAEtE,OAAO,MAAMsC,iBAAiB,GAAGtC,IAE/B;AAEF,OAAO,MAAMuC,gCAAgC,GAC3Cf,KAAiB,iBAEjB1B,KAAA,CAAA2B,aAAA,CAACzB,IAAI,qBACHF,KAAA,CAAA2B,aAAA,CAACxB,KAAK,EAAAyB,QAAA;EAACc,UAAU,EAAC,QAAQ;EAACC,YAAY,EAAE;AAAE,GAAKjB,KAAK,CAAG,CACpD,CACP;AAED,OAAO,MAAMkB,0BAA0B,GACrClB,KAAyC,iBACzB1B,KAAA,CAAA2B,aAAA,CAACzB,IAAI,EAAKwB,KAAQ,CAAC;AAErC,OAAO,MAAMmB,yBAAyB,GACpCnB,KAAyC,iBACzB1B,KAAA,CAAA2B,aAAA,CAACzB,IAAI,EAAKwB,KAAQ,CAAC;AAErC,OAAO,MAAMoB,2BAA2B,GACtCpB,KAAyC,iBACzB1B,KAAA,CAAA2B,aAAA,CAACzB,IAAI,EAAKwB,KAAQ,CAAC;AAErC,OAAO,MAAMqB,8BAA8B,GACzCrB,KAA2D,iBAC3C1B,KAAA,CAAA2B,aAAA,CAACzB,IAAI,EAAKwB,KAAQ,CAAC;AAErC,OAAO,MAAMsB,uBAAuB,GAClCtB,KAA4D,iBAC5C1B,KAAA,CAAA2B,aAAA,CAACzB,IAAI,EAAKwB,KAAQ,CAAC;;AAErC;AACA,OAAO,MAAMuB,SAA8C,GAAG/C,IAAI;AAElE,OAAO,MAAMgD,wBAEZ,GAAGhD,IAAI;AAER,OAAO,MAAMiD,sBAAsB,GAAG,IAAI"}