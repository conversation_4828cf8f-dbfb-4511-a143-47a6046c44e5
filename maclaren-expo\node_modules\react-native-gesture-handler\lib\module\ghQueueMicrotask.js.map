{"version": 3, "sources": ["ghQueueMicrotask.ts"], "names": ["ghQueueMicrotask", "queueMicrotask", "setImmediate"], "mappings": "AAAA;AACA;AACA;AACA,OAAO,MAAMA,gBAAgB,GAC3B,OAAOC,cAAP,KAA0B,UAA1B,GAAuCA,cAAvC,GAAwDC,YADnD", "sourcesContent": ["// `queueMicrotask` was introduced to react-native in version 0.66 (https://github.com/react-native-community/releases/blob/master/CHANGELOG.md#v0660)\n// Because Gesture Handler supports versions 0.64+, we have to handle situations where someone uses older version of react native.\n// That's why if `queueMicrotask` doesn't exist, we use `setImmediate` instead, since it was used before we switched to `queueMicrotask` in version 2.11.0\nexport const ghQueueMicrotask =\n  typeof queueMicrotask === 'function' ? queueMicrotask : setImmediate;\n"]}