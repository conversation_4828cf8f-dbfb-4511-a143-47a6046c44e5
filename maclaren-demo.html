<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🏍️ MACLAREN - Demo</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #ff6b35, #f7931e);
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            color: white;
            padding: 20px;
        }
        
        .container {
            text-align: center;
            max-width: 400px;
            width: 100%;
        }
        
        .logo {
            font-size: 80px;
            margin-bottom: 20px;
            animation: bounce 2s infinite;
        }
        
        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
            40% { transform: translateY(-10px); }
            60% { transform: translateY(-5px); }
        }
        
        .title {
            font-size: 48px;
            font-weight: bold;
            margin-bottom: 10px;
            letter-spacing: 2px;
        }
        
        .subtitle {
            font-size: 18px;
            margin-bottom: 50px;
            opacity: 0.9;
        }
        
        .buttons {
            margin-bottom: 40px;
        }
        
        .btn {
            background: white;
            color: #ff6b35;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            font-size: 18px;
            font-weight: bold;
            cursor: pointer;
            margin: 10px;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0,0,0,0.3);
        }
        
        .user-type {
            display: none;
            margin-top: 30px;
        }
        
        .user-card {
            background: rgba(255,255,255,0.2);
            border-radius: 20px;
            padding: 25px;
            margin: 15px 0;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .user-card:hover {
            background: rgba(255,255,255,0.3);
            transform: scale(1.05);
        }
        
        .user-icon {
            font-size: 40px;
            margin-bottom: 10px;
        }
        
        .user-title {
            font-size: 20px;
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .user-desc {
            font-size: 14px;
            opacity: 0.8;
        }
        
        .demo-screen {
            display: none;
            background: #f8f9fa;
            color: #333;
            border-radius: 20px;
            padding: 30px;
            margin-top: 20px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
        }
        
        .demo-header {
            background: linear-gradient(135deg, #ff6b35, #f7931e);
            color: white;
            padding: 20px;
            border-radius: 15px;
            margin-bottom: 20px;
            text-align: center;
        }
        
        .feature {
            background: white;
            border-radius: 10px;
            padding: 15px;
            margin: 10px 0;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .back-btn {
            background: #e74c3c;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 15px;
            cursor: pointer;
            margin-top: 20px;
        }
        
        .footer {
            margin-top: 40px;
            opacity: 0.8;
            font-size: 14px;
        }
        
        .qr-info {
            background: rgba(255,255,255,0.1);
            border-radius: 15px;
            padding: 20px;
            margin-top: 30px;
        }
        
        .qr-title {
            font-size: 20px;
            font-weight: bold;
            margin-bottom: 15px;
        }
        
        .qr-steps {
            text-align: left;
            line-height: 1.6;
        }
    </style>
</head>
<body>
    <div class="container">
        <div id="welcome" class="screen">
            <div class="logo">🏍️</div>
            <h1 class="title">MACLAREN</h1>
            <p class="subtitle">Tu moto, tu destino</p>
            
            <div class="buttons">
                <button class="btn" onclick="showUserSelect()">🚀 Comenzar</button>
            </div>
            
            <div class="qr-info">
                <div class="qr-title">📱 Para la App Móvil:</div>
                <div class="qr-steps">
                    1. Instala <strong>Expo Go</strong> en tu teléfono<br>
                    2. Escanea el QR code de la terminal<br>
                    3. ¡Disfruta MACLAREN en tu móvil!
                </div>
            </div>
            
            <p class="footer">Conectando motociclistas con pasajeros</p>
        </div>
        
        <div id="userSelect" class="screen user-type">
            <button class="back-btn" onclick="showWelcome()">← Atrás</button>
            <h2>¿Cómo quieres usar MACLAREN?</h2>
            
            <div class="user-card" onclick="showDemo('passenger')">
                <div class="user-icon">👤</div>
                <div class="user-title">Soy Pasajero</div>
                <div class="user-desc">Necesito un viaje</div>
            </div>
            
            <div class="user-card" onclick="showDemo('driver')">
                <div class="user-icon">🏍️</div>
                <div class="user-title">Soy Conductor</div>
                <div class="user-desc">Quiero ganar dinero</div>
            </div>
        </div>
        
        <div id="passengerDemo" class="screen demo-screen">
            <div class="demo-header">
                <h2>👤 Panel de Pasajero</h2>
                <p>¡Hola! ¿A dónde quieres ir?</p>
            </div>
            
            <div class="feature">
                <h3>📍 Tu ubicación</h3>
                <p>Bogotá, Colombia</p>
                <button class="btn" style="margin-top: 10px;">🔄 Actualizar</button>
            </div>
            
            <div class="feature">
                <h3>🎯 Destinos populares</h3>
                <div style="margin-top: 10px;">
                    <div style="padding: 8px; background: #f8f9fa; margin: 5px 0; border-radius: 8px;">🏢 Centro Comercial</div>
                    <div style="padding: 8px; background: #f8f9fa; margin: 5px 0; border-radius: 8px;">🏫 Universidad</div>
                    <div style="padding: 8px; background: #f8f9fa; margin: 5px 0; border-radius: 8px;">✈️ Aeropuerto</div>
                </div>
            </div>
            
            <button class="btn" onclick="alert('¡Solicitud enviada! Buscando conductor...')">🚀 Solicitar Viaje</button>
            
            <div class="feature">
                <h3>✨ Funcionalidades</h3>
                <p>⚡ Viajes rápidos y seguros<br>
                💰 Tarifas justas<br>
                📍 Seguimiento en tiempo real<br>
                💬 Chat con conductor</p>
            </div>
            
            <button class="back-btn" onclick="showUserSelect()">← Volver</button>
        </div>
        
        <div id="driverDemo" class="screen demo-screen">
            <div class="demo-header">
                <h2>🏍️ Panel de Conductor</h2>
                <p>¡Listo para ganar dinero!</p>
            </div>
            
            <div class="feature">
                <h3>📊 Estadísticas de Hoy</h3>
                <div style="display: flex; justify-content: space-between; margin-top: 10px;">
                    <div style="text-align: center;">
                        <div style="font-size: 18px; font-weight: bold; color: #2ecc71;">$125,000</div>
                        <div style="font-size: 12px; color: #666;">Ganancias</div>
                    </div>
                    <div style="text-align: center;">
                        <div style="font-size: 18px; font-weight: bold;">8</div>
                        <div style="font-size: 12px; color: #666;">Viajes</div>
                    </div>
                    <div style="text-align: center;">
                        <div style="font-size: 18px; font-weight: bold;">⭐ 4.8</div>
                        <div style="font-size: 12px; color: #666;">Rating</div>
                    </div>
                </div>
            </div>
            
            <button class="btn" style="background: #2ecc71; color: white;" onclick="alert('¡Conectado! Ahora puedes recibir solicitudes')">🟢 Ponerse En Línea</button>
            
            <div class="feature">
                <h3>💡 Consejos para Ganar Más</h3>
                <p>🕐 Trabaja en horarios pico (7-9 AM, 5-8 PM)<br>
                📍 Mantente cerca de centros comerciales<br>
                ⭐ Un rating alto te da más viajes</p>
            </div>
            
            <div class="feature">
                <h3>🏍️ Tu Vehículo</h3>
                <p>Yamaha FZ 150<br>Placa: ABC123</p>
            </div>
            
            <button class="back-btn" onclick="showUserSelect()">← Volver</button>
        </div>
    </div>
    
    <script>
        function showWelcome() {
            document.getElementById('welcome').style.display = 'block';
            document.getElementById('userSelect').style.display = 'none';
            document.getElementById('passengerDemo').style.display = 'none';
            document.getElementById('driverDemo').style.display = 'none';
        }
        
        function showUserSelect() {
            document.getElementById('welcome').style.display = 'none';
            document.getElementById('userSelect').style.display = 'block';
            document.getElementById('passengerDemo').style.display = 'none';
            document.getElementById('driverDemo').style.display = 'none';
        }
        
        function showDemo(type) {
            document.getElementById('welcome').style.display = 'none';
            document.getElementById('userSelect').style.display = 'none';
            
            if (type === 'passenger') {
                document.getElementById('passengerDemo').style.display = 'block';
                document.getElementById('driverDemo').style.display = 'none';
            } else {
                document.getElementById('passengerDemo').style.display = 'none';
                document.getElementById('driverDemo').style.display = 'block';
            }
        }
    </script>
</body>
</html>
