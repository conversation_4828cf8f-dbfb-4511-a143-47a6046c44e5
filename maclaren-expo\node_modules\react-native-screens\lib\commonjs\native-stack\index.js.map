{"version": 3, "names": ["_createNativeStackNavigator", "_interopRequireDefault", "require", "_NativeStackView", "_useHeaderHeight", "_HeaderHeightContext", "_useAnimatedHeaderHeight", "_AnimatedHeaderHeightContext", "obj", "__esModule", "default"], "sources": ["index.tsx"], "sourcesContent": ["/**\n * Navigators\n */\nexport { default as createNativeStackNavigator } from './navigators/createNativeStackNavigator';\n\n/**\n * Views\n */\nexport { default as NativeStackView } from './views/NativeStackView';\n\n/**\n * Utilities\n */\nexport { default as useHeaderHeight } from './utils/useHeaderHeight';\nexport { default as HeaderHeightContext } from './utils/HeaderHeightContext';\n\nexport { default as useAnimatedHeaderHeight } from './utils/useAnimatedHeaderHeight';\nexport { default as AnimatedHeaderHeightContext } from './utils/AnimatedHeaderHeightContext';\n\n/**\n * Types\n */\nexport type {\n  NativeStackNavigationOptions,\n  NativeStackNavigationProp,\n  NativeStackScreenProps,\n} from './types';\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAGA,IAAAA,2BAAA,GAAAC,sBAAA,CAAAC,OAAA;AAKA,IAAAC,gBAAA,GAAAF,sBAAA,CAAAC,OAAA;AAKA,IAAAE,gBAAA,GAAAH,sBAAA,CAAAC,OAAA;AACA,IAAAG,oBAAA,GAAAJ,sBAAA,CAAAC,OAAA;AAEA,IAAAI,wBAAA,GAAAL,sBAAA,CAAAC,OAAA;AACA,IAAAK,4BAAA,GAAAN,sBAAA,CAAAC,OAAA;AAA6F,SAAAD,uBAAAO,GAAA,WAAAA,GAAA,IAAAA,GAAA,CAAAC,UAAA,GAAAD,GAAA,KAAAE,OAAA,EAAAF,GAAA"}