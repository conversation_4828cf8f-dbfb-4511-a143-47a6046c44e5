/**
 * Servicio de Notificaciones para MACLAREN
 * Maneja notificaciones push, locales y en app
 */

import PushNotification from 'react-native-push-notification';
import {Platform, Alert, Vibration} from 'react-native';

export interface NotificationData {
  title: string;
  message: string;
  data?: any;
  sound?: boolean;
  vibrate?: boolean;
  priority?: 'high' | 'normal' | 'low';
}

class NotificationServiceClass {
  private isInitialized = false;
  private notificationId = 0;

  async initialize(): Promise<boolean> {
    try {
      // Configurar notificaciones push
      PushNotification.configure({
        // Callback cuando se recibe una notificación
        onNotification: (notification) => {
          console.log('📱 Notificación recibida:', notification);
          
          // Manejar tap en notificación
          if (notification.userInteraction) {
            this.handleNotificationTap(notification);
          }
        },

        // Callback cuando se registra el token
        onRegister: (token) => {
          console.log('🔑 Token de notificación:', token);
          // Enviar token al servidor para notificaciones push
        },

        // Permisos para iOS
        permissions: {
          alert: true,
          badge: true,
          sound: true,
        },

        // Solicitar permisos al inicializar
        requestPermissions: Platform.OS === 'ios',
      });

      // Crear canal de notificación para Android
      if (Platform.OS === 'android') {
        PushNotification.createChannel(
          {
            channelId: 'maclaren-default',
            channelName: 'MACLAREN Notificaciones',
            channelDescription: 'Notificaciones de viajes y mensajes',
            playSound: true,
            soundName: 'default',
            importance: 4,
            vibrate: true,
          },
          (created) => {
            console.log(`📢 Canal de notificación ${created ? 'creado' : 'ya existe'}`);
          },
        );

        // Canal para notificaciones de alta prioridad
        PushNotification.createChannel(
          {
            channelId: 'maclaren-urgent',
            channelName: 'MACLAREN Urgente',
            channelDescription: 'Notificaciones urgentes de viajes',
            playSound: true,
            soundName: 'default',
            importance: 5,
            vibrate: true,
          },
          (created) => {
            console.log(`🚨 Canal urgente ${created ? 'creado' : 'ya existe'}`);
          },
        );
      }

      this.isInitialized = true;
      console.log('✅ Servicio de notificaciones inicializado');
      return true;
    } catch (error) {
      console.error('❌ Error inicializando notificaciones:', error);
      return false;
    }
  }

  // Mostrar notificación local
  showNotification(title: string, message: string, data?: any): void {
    if (!this.isInitialized) {
      console.warn('⚠️ Servicio de notificaciones no inicializado');
      return;
    }

    const notificationId = ++this.notificationId;

    PushNotification.localNotification({
      id: notificationId,
      title,
      message,
      playSound: true,
      soundName: 'default',
      vibrate: true,
      vibration: 300,
      channelId: 'maclaren-default',
      userInfo: data,
      actions: ['Ver'],
      invokeApp: true,
      priority: 'high',
      visibility: 'public',
    });

    console.log(`📱 Notificación mostrada: ${title}`);
  }

  // Mostrar notificación urgente
  showUrgentNotification(title: string, message: string, data?: any): void {
    if (!this.isInitialized) {
      console.warn('⚠️ Servicio de notificaciones no inicializado');
      return;
    }

    const notificationId = ++this.notificationId;

    PushNotification.localNotification({
      id: notificationId,
      title,
      message,
      playSound: true,
      soundName: 'default',
      vibrate: true,
      vibration: [500, 200, 500],
      channelId: 'maclaren-urgent',
      userInfo: data,
      actions: ['Ver', 'Cerrar'],
      invokeApp: true,
      priority: 'max',
      visibility: 'public',
      ongoing: false,
      autoCancel: true,
    });

    // Vibración adicional para notificaciones urgentes
    Vibration.vibrate([0, 500, 200, 500]);

    console.log(`🚨 Notificación urgente mostrada: ${title}`);
  }

  // Notificaciones específicas de MACLAREN
  notifyTripAccepted(driverName: string, estimatedTime?: number): void {
    const message = estimatedTime 
      ? `${driverName} aceptó tu viaje. Llegará en ${estimatedTime} minutos.`
      : `${driverName} aceptó tu viaje. Te contactará pronto.`;

    this.showUrgentNotification('🎉 ¡Viaje Aceptado!', message, {
      type: 'trip_accepted',
      driverName,
      estimatedTime,
    });
  }

  notifyNewTripRequest(pickup: string, destination: string, fare: number): void {
    this.showUrgentNotification(
      '🚗 Nueva Solicitud de Viaje',
      `${pickup} → ${destination} • $${fare.toLocaleString()}`,
      {
        type: 'trip_request',
        pickup,
        destination,
        fare,
      },
    );
  }

  notifyNewMessage(senderName: string, message: string): void {
    this.showNotification(
      `💬 Mensaje de ${senderName}`,
      message,
      {
        type: 'new_message',
        senderName,
        message,
      },
    );
  }

  notifyTripStatusChange(status: string, details?: string): void {
    const statusMessages = {
      'in_progress': '🚗 Tu viaje ha comenzado',
      'completed': '✅ Viaje completado exitosamente',
      'cancelled': '❌ Viaje cancelado',
      'driver_arriving': '🏍️ Tu conductor está llegando',
    };

    const title = statusMessages[status as keyof typeof statusMessages] || 'Estado del viaje actualizado';
    const message = details || 'Revisa la app para más detalles';

    if (status === 'driver_arriving' || status === 'completed') {
      this.showUrgentNotification(title, message, {
        type: 'trip_status',
        status,
        details,
      });
    } else {
      this.showNotification(title, message, {
        type: 'trip_status',
        status,
        details,
      });
    }
  }

  notifyDriverArriving(estimatedTime: number): void {
    this.showUrgentNotification(
      '🏍️ Tu conductor está llegando',
      `Llegará en aproximadamente ${estimatedTime} minutos. ¡Prepárate!`,
      {
        type: 'driver_arriving',
        estimatedTime,
      },
    );
  }

  notifyPaymentProcessed(amount: number): void {
    this.showNotification(
      '💳 Pago Procesado',
      `Se procesó el pago de $${amount.toLocaleString()}`,
      {
        type: 'payment',
        amount,
      },
    );
  }

  // Manejar tap en notificación
  private handleNotificationTap(notification: any): void {
    console.log('👆 Notificación tocada:', notification);
    
    const {type} = notification.data || {};
    
    switch (type) {
      case 'trip_accepted':
      case 'trip_request':
      case 'trip_status':
        // Navegar a pantalla de viaje activo
        console.log('Navegando a viaje activo...');
        break;
      
      case 'new_message':
        // Navegar a chat
        console.log('Navegando a chat...');
        break;
      
      case 'driver_arriving':
        // Mostrar alerta de conductor llegando
        Alert.alert(
          '🏍️ Conductor Llegando',
          'Tu conductor está cerca. ¡Prepárate para el viaje!',
        );
        break;
      
      default:
        console.log('Tipo de notificación no manejado:', type);
    }
  }

  // Cancelar notificación específica
  cancelNotification(notificationId: number): void {
    PushNotification.cancelLocalNotifications({id: notificationId.toString()});
    console.log(`🗑️ Notificación ${notificationId} cancelada`);
  }

  // Cancelar todas las notificaciones
  cancelAllNotifications(): void {
    PushNotification.cancelAllLocalNotifications();
    console.log('🗑️ Todas las notificaciones canceladas');
  }

  // Obtener notificaciones programadas
  getScheduledNotifications(): Promise<any[]> {
    return new Promise((resolve) => {
      PushNotification.getScheduledLocalNotifications((notifications) => {
        resolve(notifications);
      });
    });
  }

  // Programar notificación para más tarde
  scheduleNotification(
    title: string,
    message: string,
    date: Date,
    data?: any,
  ): void {
    const notificationId = ++this.notificationId;

    PushNotification.localNotificationSchedule({
      id: notificationId,
      title,
      message,
      date,
      playSound: true,
      soundName: 'default',
      vibrate: true,
      channelId: 'maclaren-default',
      userInfo: data,
    });

    console.log(`⏰ Notificación programada para ${date.toLocaleString()}`);
  }

  // Verificar permisos de notificación
  async checkPermissions(): Promise<boolean> {
    return new Promise((resolve) => {
      PushNotification.checkPermissions((permissions) => {
        const hasPermissions = permissions.alert && permissions.sound;
        console.log('🔐 Permisos de notificación:', permissions);
        resolve(hasPermissions);
      });
    });
  }

  // Solicitar permisos (principalmente para iOS)
  async requestPermissions(): Promise<boolean> {
    return new Promise((resolve) => {
      PushNotification.requestPermissions().then((permissions) => {
        const hasPermissions = permissions.alert && permissions.sound;
        console.log('📱 Permisos solicitados:', permissions);
        resolve(hasPermissions);
      });
    });
  }

  // Limpiar recursos
  cleanup(): void {
    this.cancelAllNotifications();
    this.isInitialized = false;
    this.notificationId = 0;
    console.log('🧹 Servicio de notificaciones limpiado');
  }

  // Getters
  get initialized(): boolean {
    return this.isInitialized;
  }
}

export const NotificationService = new NotificationServiceClass();
