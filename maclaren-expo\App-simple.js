/**
 * MACLAREN - Versión Simple para Testing
 */

import React, { useState } from 'react';
import { 
  View, 
  Text, 
  TouchableOpacity, 
  StyleSheet, 
  Alert,
  ScrollView 
} from 'react-native';
import { StatusBar } from 'expo-status-bar';
import { LinearGradient } from 'expo-linear-gradient';

export default function App() {
  const [currentScreen, setCurrentScreen] = useState('welcome');
  const [userType, setUserType] = useState(null);

  const WelcomeScreen = () => (
    <LinearGradient colors={['#ff6b35', '#f7931e']} style={styles.container}>
      <StatusBar style="light" />
      
      <View style={styles.content}>
        <View style={styles.logoContainer}>
          <Text style={styles.logo}>🏍️</Text>
          <Text style={styles.appName}>MACLAREN</Text>
          <Text style={styles.tagline}>Tu moto, tu destino</Text>
        </View>

        <View style={styles.buttonsContainer}>
          <TouchableOpacity
            style={styles.primaryButton}
            onPress={() => setCurrentScreen('userSelect')}>
            <Text style={styles.primaryButtonText}>🚀 Comenzar</Text>
          </TouchableOpacity>
        </View>

        <Text style={styles.footer}>Conectando motociclistas con pasajeros</Text>
      </View>
    </LinearGradient>
  );

  const UserSelectScreen = () => (
    <LinearGradient colors={['#ff6b35', '#f7931e']} style={styles.container}>
      <StatusBar style="light" />
      
      <View style={styles.content}>
        <TouchableOpacity 
          style={styles.backButton}
          onPress={() => setCurrentScreen('welcome')}>
          <Text style={styles.backButtonText}>← Atrás</Text>
        </TouchableOpacity>

        <View style={styles.logoContainer}>
          <Text style={styles.logo}>🏍️</Text>
          <Text style={styles.appName}>MACLAREN</Text>
          <Text style={styles.subtitle}>¿Cómo quieres usar la app?</Text>
        </View>

        <View style={styles.userTypeContainer}>
          <TouchableOpacity
            style={styles.userTypeButton}
            onPress={() => {
              setUserType('passenger');
              setCurrentScreen('passengerHome');
            }}>
            <Text style={styles.userTypeIcon}>👤</Text>
            <Text style={styles.userTypeTitle}>Soy Pasajero</Text>
            <Text style={styles.userTypeDesc}>Necesito un viaje</Text>
          </TouchableOpacity>
          
          <TouchableOpacity
            style={styles.userTypeButton}
            onPress={() => {
              setUserType('driver');
              setCurrentScreen('driverHome');
            }}>
            <Text style={styles.userTypeIcon}>🏍️</Text>
            <Text style={styles.userTypeTitle}>Soy Conductor</Text>
            <Text style={styles.userTypeDesc}>Quiero ganar dinero</Text>
          </TouchableOpacity>
        </View>
      </View>
    </LinearGradient>
  );

  const PassengerHomeScreen = () => (
    <View style={styles.homeContainer}>
      <StatusBar style="light" />
      
      <LinearGradient colors={['#ff6b35', '#f7931e']} style={styles.header}>
        <TouchableOpacity 
          style={styles.backButton}
          onPress={() => setCurrentScreen('userSelect')}>
          <Text style={styles.backButtonText}>← Atrás</Text>
        </TouchableOpacity>
        
        <Text style={styles.headerTitle}>👤 Panel de Pasajero</Text>
        <Text style={styles.headerSubtitle}>¡Hola! ¿A dónde quieres ir?</Text>
      </LinearGradient>

      <ScrollView style={styles.homeContent}>
        <View style={styles.card}>
          <Text style={styles.cardTitle}>📍 Tu ubicación</Text>
          <Text style={styles.cardText}>Bogotá, Colombia</Text>
          <TouchableOpacity style={styles.cardButton}>
            <Text style={styles.cardButtonText}>🔄 Actualizar</Text>
          </TouchableOpacity>
        </View>

        <View style={styles.card}>
          <Text style={styles.cardTitle}>🎯 Destinos populares</Text>
          <TouchableOpacity style={styles.destinationButton}>
            <Text style={styles.destinationText}>🏢 Centro Comercial</Text>
          </TouchableOpacity>
          <TouchableOpacity style={styles.destinationButton}>
            <Text style={styles.destinationText}>🏫 Universidad</Text>
          </TouchableOpacity>
          <TouchableOpacity style={styles.destinationButton}>
            <Text style={styles.destinationText}>✈️ Aeropuerto</Text>
          </TouchableOpacity>
        </View>

        <TouchableOpacity
          style={styles.requestButton}
          onPress={() => Alert.alert('¡Éxito!', 'Solicitud de viaje enviada. Buscando conductor...')}>
          <Text style={styles.requestButtonText}>🚀 Solicitar Viaje</Text>
        </TouchableOpacity>

        <View style={styles.infoCard}>
          <Text style={styles.infoTitle}>✨ Funcionalidades</Text>
          <Text style={styles.infoText}>⚡ Viajes rápidos y seguros</Text>
          <Text style={styles.infoText}>💰 Tarifas justas</Text>
          <Text style={styles.infoText}>📍 Seguimiento en tiempo real</Text>
          <Text style={styles.infoText}>💬 Chat con conductor</Text>
        </View>
      </ScrollView>
    </View>
  );

  const DriverHomeScreen = () => (
    <View style={styles.homeContainer}>
      <StatusBar style="light" />
      
      <LinearGradient colors={['#ff6b35', '#f7931e']} style={styles.header}>
        <TouchableOpacity 
          style={styles.backButton}
          onPress={() => setCurrentScreen('userSelect')}>
          <Text style={styles.backButtonText}>← Atrás</Text>
        </TouchableOpacity>
        
        <Text style={styles.headerTitle}>🏍️ Panel de Conductor</Text>
        <Text style={styles.headerSubtitle}>¡Listo para ganar dinero!</Text>
      </LinearGradient>

      <ScrollView style={styles.homeContent}>
        <View style={styles.card}>
          <Text style={styles.cardTitle}>📊 Estadísticas de Hoy</Text>
          <View style={styles.statsContainer}>
            <View style={styles.statItem}>
              <Text style={styles.statValue}>$125,000</Text>
              <Text style={styles.statLabel}>Ganancias</Text>
            </View>
            <View style={styles.statItem}>
              <Text style={styles.statValue}>8</Text>
              <Text style={styles.statLabel}>Viajes</Text>
            </View>
            <View style={styles.statItem}>
              <Text style={styles.statValue}>⭐ 4.8</Text>
              <Text style={styles.statLabel}>Rating</Text>
            </View>
          </View>
        </View>

        <TouchableOpacity
          style={styles.onlineButton}
          onPress={() => Alert.alert('¡Conectado!', 'Ahora puedes recibir solicitudes de viaje')}>
          <Text style={styles.onlineButtonText}>🟢 Ponerse En Línea</Text>
        </TouchableOpacity>

        <View style={styles.card}>
          <Text style={styles.cardTitle}>💡 Consejos para Ganar Más</Text>
          <Text style={styles.tipText}>🕐 Trabaja en horarios pico (7-9 AM, 5-8 PM)</Text>
          <Text style={styles.tipText}>📍 Mantente cerca de centros comerciales</Text>
          <Text style={styles.tipText}>⭐ Un rating alto te da más viajes</Text>
        </View>

        <View style={styles.infoCard}>
          <Text style={styles.infoTitle}>🏍️ Tu Vehículo</Text>
          <Text style={styles.infoText}>Yamaha FZ 150</Text>
          <Text style={styles.infoText}>Placa: ABC123</Text>
        </View>
      </ScrollView>
    </View>
  );

  // Renderizar pantalla actual
  const renderScreen = () => {
    switch (currentScreen) {
      case 'welcome':
        return <WelcomeScreen />;
      case 'userSelect':
        return <UserSelectScreen />;
      case 'passengerHome':
        return <PassengerHomeScreen />;
      case 'driverHome':
        return <DriverHomeScreen />;
      default:
        return <WelcomeScreen />;
    }
  };

  return renderScreen();
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 20,
  },
  content: {
    flex: 1,
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 60,
    width: '100%',
  },
  logoContainer: {
    alignItems: 'center',
    marginTop: 40,
  },
  logo: {
    fontSize: 80,
    marginBottom: 20,
  },
  appName: {
    fontSize: 36,
    fontWeight: 'bold',
    color: 'white',
    marginBottom: 10,
    letterSpacing: 2,
  },
  tagline: {
    fontSize: 18,
    color: 'rgba(255, 255, 255, 0.9)',
    textAlign: 'center',
    fontStyle: 'italic',
  },
  subtitle: {
    fontSize: 18,
    color: 'rgba(255, 255, 255, 0.9)',
    textAlign: 'center',
    marginTop: 10,
  },
  buttonsContainer: {
    width: '100%',
    alignItems: 'center',
  },
  primaryButton: {
    backgroundColor: 'white',
    borderRadius: 25,
    paddingVertical: 15,
    paddingHorizontal: 40,
    minWidth: 200,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 4.65,
    elevation: 8,
  },
  primaryButtonText: {
    color: '#ff6b35',
    fontSize: 18,
    fontWeight: 'bold',
    textAlign: 'center',
  },
  backButton: {
    position: 'absolute',
    top: 50,
    left: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    borderRadius: 20,
    paddingHorizontal: 15,
    paddingVertical: 8,
  },
  backButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
  },
  userTypeContainer: {
    width: '100%',
    paddingHorizontal: 20,
  },
  userTypeButton: {
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    borderRadius: 20,
    padding: 25,
    alignItems: 'center',
    marginBottom: 20,
  },
  userTypeIcon: {
    fontSize: 40,
    marginBottom: 10,
  },
  userTypeTitle: {
    color: 'white',
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 5,
  },
  userTypeDesc: {
    color: 'rgba(255, 255, 255, 0.8)',
    fontSize: 14,
  },
  footer: {
    color: 'rgba(255, 255, 255, 0.8)',
    fontSize: 14,
    textAlign: 'center',
  },
  homeContainer: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  header: {
    paddingTop: 50,
    paddingBottom: 20,
    paddingHorizontal: 20,
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: 'white',
    marginBottom: 5,
  },
  headerSubtitle: {
    fontSize: 16,
    color: 'rgba(255, 255, 255, 0.8)',
  },
  homeContent: {
    flex: 1,
    paddingHorizontal: 20,
  },
  card: {
    backgroundColor: 'white',
    borderRadius: 15,
    padding: 20,
    marginTop: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  cardTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 15,
  },
  cardText: {
    fontSize: 16,
    color: '#666',
    marginBottom: 10,
  },
  cardButton: {
    backgroundColor: '#f8f9fa',
    borderRadius: 8,
    paddingVertical: 8,
    paddingHorizontal: 15,
    alignSelf: 'flex-start',
  },
  cardButtonText: {
    color: '#ff6b35',
    fontWeight: '600',
  },
  destinationButton: {
    backgroundColor: '#f8f9fa',
    borderRadius: 10,
    padding: 12,
    marginBottom: 8,
  },
  destinationText: {
    fontSize: 16,
    color: '#333',
  },
  requestButton: {
    backgroundColor: '#ff6b35',
    borderRadius: 15,
    paddingVertical: 18,
    alignItems: 'center',
    marginTop: 20,
    shadowColor: '#ff6b35',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 4.65,
    elevation: 8,
  },
  requestButtonText: {
    color: 'white',
    fontSize: 18,
    fontWeight: 'bold',
  },
  onlineButton: {
    backgroundColor: '#2ecc71',
    borderRadius: 15,
    paddingVertical: 18,
    alignItems: 'center',
    marginTop: 20,
    shadowColor: '#2ecc71',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 4.65,
    elevation: 8,
  },
  onlineButtonText: {
    color: 'white',
    fontSize: 18,
    fontWeight: 'bold',
  },
  statsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  statItem: {
    alignItems: 'center',
  },
  statValue: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 5,
  },
  statLabel: {
    fontSize: 12,
    color: '#666',
  },
  infoCard: {
    backgroundColor: 'white',
    borderRadius: 15,
    padding: 20,
    marginTop: 20,
    marginBottom: 30,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  infoTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 15,
  },
  infoText: {
    fontSize: 14,
    color: '#666',
    marginBottom: 8,
  },
  tipText: {
    fontSize: 14,
    color: '#666',
    marginBottom: 10,
  },
});
