/**
 * Pantalla de Mapa - MACLAREN
 * Mapa con seguimiento en tiempo real
 */

import React, {useState, useEffect, useRef} from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  StatusBar,
  Alert,
  Dimensions,
} from 'react-native';
import MapView, {<PERSON><PERSON>, <PERSON><PERSON><PERSON>, PROVIDER_GOOGLE} from 'react-native-maps';
import LinearGradient from 'react-native-linear-gradient';
import * as Animatable from 'react-native-animatable';
import {StackNavigationProp} from '@react-navigation/stack';
import {RouteProp} from '@react-navigation/native';
import {LocationService} from '../../services/LocationService';
import {SocketService} from '../../services/SocketService';

const {width, height} = Dimensions.get('window');

interface MapScreenProps {
  navigation: StackNavigationProp<any>;
  route: RouteProp<any>;
}

interface Location {
  latitude: number;
  longitude: number;
}

interface TripData {
  id: string;
  pickup: Location;
  destination: Location;
  driverLocation?: Location;
  status: 'waiting' | 'pickup' | 'in_progress' | 'completed';
}

const MapScreen: React.FC<MapScreenProps> = ({navigation, route}) => {
  const [currentLocation, setCurrentLocation] = useState<Location | null>(null);
  const [tripData, setTripData] = useState<TripData | null>(null);
  const [routeCoordinates, setRouteCoordinates] = useState<Location[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [userType, setUserType] = useState<'passenger' | 'driver'>('passenger');
  
  const mapRef = useRef<MapView>(null);

  // Datos de ejemplo del viaje
  const demoTripData: TripData = {
    id: 'demo-trip',
    pickup: {
      latitude: 4.6097,
      longitude: -74.0817,
    },
    destination: {
      latitude: 4.6367,
      longitude: -74.0834,
    },
    driverLocation: {
      latitude: 4.6050,
      longitude: -74.0800,
    },
    status: 'pickup',
  };

  useEffect(() => {
    initializeMap();
    setupLocationTracking();
    
    return () => {
      // Cleanup
    };
  }, []);

  const initializeMap = async () => {
    try {
      // Obtener ubicación actual
      const location = await LocationService.getCurrentLocation();
      setCurrentLocation(location);
      
      // Configurar datos del viaje
      const routeUserType = route.params?.userType || 'passenger';
      setUserType(routeUserType);
      
      // Cargar datos del viaje (en producción vendría de props/params)
      setTripData(demoTripData);
      
      // Generar ruta de ejemplo
      generateDemoRoute();
      
      setIsLoading(false);
      
      // Centrar mapa en la ubicación apropiada
      setTimeout(() => {
        centerMapOnTrip();
      }, 1000);
      
    } catch (error) {
      console.error('Error inicializando mapa:', error);
      Alert.alert('Error', 'No se pudo cargar el mapa');
      setIsLoading(false);
    }
  };

  const setupLocationTracking = () => {
    // Simular actualizaciones de ubicación del conductor
    const locationInterval = setInterval(() => {
      if (tripData && tripData.driverLocation) {
        // Simular movimiento del conductor
        const newDriverLocation = {
          latitude: tripData.driverLocation.latitude + (Math.random() - 0.5) * 0.001,
          longitude: tripData.driverLocation.longitude + (Math.random() - 0.5) * 0.001,
        };
        
        setTripData(prev => prev ? {
          ...prev,
          driverLocation: newDriverLocation,
        } : null);
      }
    }, 5000);

    return () => clearInterval(locationInterval);
  };

  const generateDemoRoute = () => {
    // Generar puntos de ruta de ejemplo
    const route: Location[] = [
      demoTripData.pickup,
      {
        latitude: 4.6150,
        longitude: -74.0800,
      },
      {
        latitude: 4.6250,
        longitude: -74.0820,
      },
      demoTripData.destination,
    ];
    
    setRouteCoordinates(route);
  };

  const centerMapOnTrip = () => {
    if (!mapRef.current || !tripData) return;

    const coordinates = [
      tripData.pickup,
      tripData.destination,
    ];

    if (tripData.driverLocation) {
      coordinates.push(tripData.driverLocation);
    }

    mapRef.current.fitToCoordinates(coordinates, {
      edgePadding: {
        top: 100,
        right: 50,
        bottom: 100,
        left: 50,
      },
      animated: true,
    });
  };

  const handleCenterMap = () => {
    centerMapOnTrip();
  };

  const handleCallDriver = () => {
    Alert.alert('Llamar', '¿Deseas llamar al conductor?', [
      {text: 'Cancelar', style: 'cancel'},
      {text: 'Llamar', onPress: () => {
        // Aquí implementarías la llamada
        Alert.alert('Llamando...', 'Conectando con el conductor');
      }},
    ]);
  };

  const getStatusText = () => {
    if (!tripData) return 'Cargando...';
    
    switch (tripData.status) {
      case 'waiting':
        return 'Buscando conductor...';
      case 'pickup':
        return 'El conductor va hacia ti';
      case 'in_progress':
        return 'En camino al destino';
      case 'completed':
        return 'Viaje completado';
      default:
        return 'Estado desconocido';
    }
  };

  const getStatusColor = () => {
    if (!tripData) return '#999';
    
    switch (tripData.status) {
      case 'waiting':
        return '#f39c12';
      case 'pickup':
        return '#3498db';
      case 'in_progress':
        return '#2ecc71';
      case 'completed':
        return '#27ae60';
      default:
        return '#999';
    }
  };

  if (isLoading) {
    return (
      <View style={styles.loadingContainer}>
        <Text style={styles.loadingText}>Cargando mapa...</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor="#ff6b35" />
      
      {/* Mapa */}
      <MapView
        ref={mapRef}
        provider={PROVIDER_GOOGLE}
        style={styles.map}
        initialRegion={{
          latitude: currentLocation?.latitude || 4.6097,
          longitude: currentLocation?.longitude || -74.0817,
          latitudeDelta: 0.0922,
          longitudeDelta: 0.0421,
        }}
        showsUserLocation={true}
        showsMyLocationButton={false}
        showsTraffic={true}>
        
        {/* Marcador de recogida */}
        {tripData && (
          <Marker
            coordinate={tripData.pickup}
            title="Punto de Recogida"
            description="Aquí te recogeremos"
            pinColor="#2ecc71"
          />
        )}
        
        {/* Marcador de destino */}
        {tripData && (
          <Marker
            coordinate={tripData.destination}
            title="Destino"
            description="Tu destino final"
            pinColor="#e74c3c"
          />
        )}
        
        {/* Marcador del conductor */}
        {tripData && tripData.driverLocation && (
          <Marker
            coordinate={tripData.driverLocation}
            title="Conductor"
            description="Ubicación del conductor">
            <View style={styles.driverMarker}>
              <Text style={styles.driverMarkerText}>🏍️</Text>
            </View>
          </Marker>
        )}
        
        {/* Ruta */}
        {routeCoordinates.length > 0 && (
          <Polyline
            coordinates={routeCoordinates}
            strokeColor="#ff6b35"
            strokeWidth={4}
            lineDashPattern={[5, 5]}
          />
        )}
      </MapView>

      {/* Header */}
      <LinearGradient
        colors={['#ff6b35', 'transparent']}
        style={styles.header}>
        
        <TouchableOpacity 
          style={styles.backButton}
          onPress={() => navigation.goBack()}>
          <Text style={styles.backButtonText}>←</Text>
        </TouchableOpacity>
        
        <Text style={styles.headerTitle}>Seguimiento en Vivo</Text>
        
        <TouchableOpacity 
          style={styles.centerButton}
          onPress={handleCenterMap}>
          <Text style={styles.centerButtonText}>🎯</Text>
        </TouchableOpacity>
      </LinearGradient>

      {/* Panel de información */}
      <Animatable.View 
        animation="slideInUp"
        duration={800}
        style={styles.infoPanel}>
        
        <View style={styles.statusContainer}>
          <View style={[styles.statusIndicator, {backgroundColor: getStatusColor()}]} />
          <Text style={styles.statusText}>{getStatusText()}</Text>
        </View>

        {tripData && (
          <View style={styles.tripInfo}>
            <View style={styles.locationRow}>
              <Text style={styles.locationIcon}>📍</Text>
              <Text style={styles.locationText} numberOfLines={1}>
                Recogida en punto seleccionado
              </Text>
            </View>
            
            <View style={styles.locationRow}>
              <Text style={styles.locationIcon}>🎯</Text>
              <Text style={styles.locationText} numberOfLines={1}>
                Destino confirmado
              </Text>
            </View>
          </View>
        )}

        <View style={styles.actionButtons}>
          {userType === 'passenger' && (
            <TouchableOpacity 
              style={styles.callButton}
              onPress={handleCallDriver}>
              <Text style={styles.callButtonText}>📞 Llamar</Text>
            </TouchableOpacity>
          )}
          
          <TouchableOpacity 
            style={styles.chatButton}
            onPress={() => navigation.navigate('Chat', {
              tripId: tripData?.id,
              userType,
              otherUserName: userType === 'passenger' ? 'Conductor' : 'Pasajero',
            })}>
            <Text style={styles.chatButtonText}>💬 Chat</Text>
          </TouchableOpacity>
        </View>
      </Animatable.View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f8f9fa',
  },
  loadingText: {
    fontSize: 18,
    color: '#666',
  },
  map: {
    flex: 1,
  },
  header: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    flexDirection: 'row',
    alignItems: 'center',
    paddingTop: 50,
    paddingBottom: 15,
    paddingHorizontal: 20,
    zIndex: 1000,
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 15,
  },
  backButtonText: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
  },
  headerTitle: {
    flex: 1,
    fontSize: 18,
    fontWeight: 'bold',
    color: 'white',
    textAlign: 'center',
  },
  centerButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  centerButtonText: {
    fontSize: 20,
  },
  driverMarker: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#ff6b35',
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 3,
    borderColor: 'white',
  },
  driverMarkerText: {
    fontSize: 20,
  },
  infoPanel: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: 'white',
    borderTopLeftRadius: 25,
    borderTopRightRadius: 25,
    padding: 25,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: -4},
    shadowOpacity: 0.15,
    shadowRadius: 8,
    elevation: 10,
  },
  statusContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 20,
  },
  statusIndicator: {
    width: 12,
    height: 12,
    borderRadius: 6,
    marginRight: 10,
  },
  statusText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  tripInfo: {
    marginBottom: 20,
  },
  locationRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 10,
  },
  locationIcon: {
    fontSize: 16,
    marginRight: 10,
    width: 20,
  },
  locationText: {
    fontSize: 14,
    color: '#666',
    flex: 1,
  },
  actionButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  callButton: {
    backgroundColor: '#2ecc71',
    borderRadius: 12,
    paddingVertical: 15,
    paddingHorizontal: 25,
    flex: 1,
    marginRight: 10,
  },
  callButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
    textAlign: 'center',
  },
  chatButton: {
    backgroundColor: '#3498db',
    borderRadius: 12,
    paddingVertical: 15,
    paddingHorizontal: 25,
    flex: 1,
  },
  chatButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
    textAlign: 'center',
  },
});

export default MapScreen;
