# Changelog

All notable changes to this project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/)
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [v1.2.1](https://github.com/inspect-js/is-regex/compare/v1.2.0...v1.2.1) - 2024-12-11

### Commits

- [Refactor] use `call-bound` directly [`dbabfe3`](https://github.com/inspect-js/is-regex/commit/dbabfe369261f4940f8ca059e9d452fed01e6969)
- [Deps] update `call-bind`, `gopd` [`d5343a0`](https://github.com/inspect-js/is-regex/commit/d5343a0e05ac3c65243b29f740c7540480ad23e7)
- [<PERSON> <PERSON><PERSON>] update `@arethetypeswrong/cli`, `@ljharb/tsconfig` [`cc081eb`](https://github.com/inspect-js/is-regex/commit/cc081eb891b075440188c3f5240b2051090f1036)

## [v1.2.0](https://github.com/inspect-js/is-regex/compare/v1.1.4...v1.2.0) - 2024-11-29

### Fixed

- [Tests] allow tests to pass if zero traps are triggered [`#35`](https://github.com/inspect-js/is-regex/issues/35)

### Commits

- [actions] reuse common workflows [`be7bf6a`](https://github.com/inspect-js/is-regex/commit/be7bf6af175271e9db74e092d636307f6cf5e848)
- [New] add types [`39066a4`](https://github.com/inspect-js/is-regex/commit/39066a426da0f0c597b888c3c839fb2efc2bf613)
- [meta] use `npmignore` to autogenerate an npmignore file [`8938588`](https://github.com/inspect-js/is-regex/commit/89385885c2da891644e07220b4407f760c3dd629)
- [Refactor] reorganize code [`2f76f26`](https://github.com/inspect-js/is-regex/commit/2f76f26e0008a01a2f9a849dc2687cf3e3146f3e)
- [actions] split out node 10-20, and 20+ [`8c9aedf`](https://github.com/inspect-js/is-regex/commit/8c9aedf2cd9b3fd4ee045d747580265de223b4b2)
- [meta] better `eccheck` command [`6b39408`](https://github.com/inspect-js/is-regex/commit/6b39408bc4cce49ac85de70fba4a843da4b78b84)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `safe-publish-latest`, `tape` [`e38cf3c`](https://github.com/inspect-js/is-regex/commit/e38cf3c4e5b234c9a0d64fcf75e45df9b2416557)
- [actions] update codecov uploader [`487c75d`](https://github.com/inspect-js/is-regex/commit/487c75da7bbc2d977c2aed68815e7a58d269011f)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `aud`, `auto-changelog`, `core-js`, `foreach`, `tape` [`0d7da87`](https://github.com/inspect-js/is-regex/commit/0d7da87be90b637c094abfd9e68568097d391e38)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `aud`, `core-js`, `tape` [`c1c1198`](https://github.com/inspect-js/is-regex/commit/c1c1198143feaadee0370b78f7fd878e64e57a58)
- [actions] update rebase action to use reusable workflow [`213646e`](https://github.com/inspect-js/is-regex/commit/213646e1e9b5211bf621ccaaaa29f03b706b139c)
- [Dev Deps] update `@ljharb/eslint-config`, `auto-changelog`, `core-js`, `npmignore`, `tape` [`0a44e77`](https://github.com/inspect-js/is-regex/commit/0a44e77855db0ee8077f8b4351164fa500ba0d1f)
- [Refactor] use `hasown` [`d939332`](https://github.com/inspect-js/is-regex/commit/d939332c57cf46b95bc45e721fb66db64cf4743a)
- [Deps] update `call-bind`, `has-tostringtag` [`46bfdc9`](https://github.com/inspect-js/is-regex/commit/46bfdc9226df0495b5eb4e753e3f61e9a648cf44)
- [Tests] use `for-each` instead of `foreach` [`138b3f2`](https://github.com/inspect-js/is-regex/commit/138b3f2e6340eb4b6caf62981b0a1f59e960cbc9)
- [Tests] replace `aud` with `npm audit` [`37ed80a`](https://github.com/inspect-js/is-regex/commit/37ed80a3a5eeec2312f45956bc928fc7937480b8)
- [Deps] update `gopd` [`6fd4097`](https://github.com/inspect-js/is-regex/commit/6fd4097f23c3f031d0e659b29174216bed7b4f0f)
- [Dev Deps] update `core-js` [`97c1c60`](https://github.com/inspect-js/is-regex/commit/97c1c6044684f8a661055f4614ab2dd58fbfcc2b)
- [Dev Deps] add missing peer dep [`7329b8e`](https://github.com/inspect-js/is-regex/commit/7329b8edc9d9440439f190704f03740c853d15da)

## [v1.1.4](https://github.com/inspect-js/is-regex/compare/v1.1.3...v1.1.4) - 2021-08-05

### Commits

- [Dev Deps] update `auto-changelog`, `core-js`, `eslint`, `tape` [`4b17cad`](https://github.com/inspect-js/is-regex/commit/4b17cad8496b1ae621b18335fa3afe94d0c65e83)
- [Refactor] use `has-tostringtag` to behave correctly in the presence of symbol shams [`2dad4af`](https://github.com/inspect-js/is-regex/commit/2dad4afffa15f07cbbf7675b77d1f650c92652c4)

## [v1.1.3](https://github.com/inspect-js/is-regex/compare/v1.1.2...v1.1.3) - 2021-05-07

### Commits

- [actions] use `node/install` instead of `node/run`; use `codecov` action [`c681ab9`](https://github.com/inspect-js/is-regex/commit/c681ab99c07f8b3b7ae5f652b3105a30bce94f69)
- [Fix] do not use `Object.prototype.toString` when `Symbol.toStringTag` is shammed [`ca019fd`](https://github.com/inspect-js/is-regex/commit/ca019fdb828dc7d32e323213403ac9995d8604e3)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `aud`, `tape` [`605a66f`](https://github.com/inspect-js/is-regex/commit/605a66f278900f1c8ae9d1dfcec31e5f61b10ad3)
- [readme] add actions and codecov badges [`8d7c6f0`](https://github.com/inspect-js/is-regex/commit/8d7c6f0e007bd982f21b958e0abc98b8a84e2a24)
- [meta] use `prepublishOnly` script for npm 7+ [`8e50e91`](https://github.com/inspect-js/is-regex/commit/8e50e91f51aa5038745526710ef2e030527982a7)
- [Deps] update `has-symbols` [`4742c81`](https://github.com/inspect-js/is-regex/commit/4742c81260c3db9a8c9ef57110981fb6175f58e0)

## [v1.1.2](https://github.com/inspect-js/is-regex/compare/v1.1.1...v1.1.2) - 2021-02-01

### Commits

- [Tests] migrate tests to Github Actions [`cc1686e`](https://github.com/inspect-js/is-regex/commit/cc1686e25f446ca6948f43b3f180d6e55e31fb4e)
- [readme] fix repo URLs; remove travis badge [`d1d1da6`](https://github.com/inspect-js/is-regex/commit/d1d1da647bb4e91589606f12470cd27a47b3bb81)
- [meta] do not publish github action workflow files [`9f84b99`](https://github.com/inspect-js/is-regex/commit/9f84b993a995f057b4d2d097ef47b1ff9c84115d)
- [Tests] run `nyc` on all tests [`c37aab9`](https://github.com/inspect-js/is-regex/commit/c37aab9d332c4834b08ada94736c45ab1d39cd2f)
- [Robustness] use `call-bind` [`fbb61bf`](https://github.com/inspect-js/is-regex/commit/fbb61bf3e19ccc178e6ed1e0d7ab9cc7c7167393)
- [actions] add "Allow Edits" workflow [`9022b53`](https://github.com/inspect-js/is-regex/commit/9022b53cb05b0f105cd179800cf96e055b249f08)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `aud`, `auto-changelog` [`d60f28f`](https://github.com/inspect-js/is-regex/commit/d60f28f7f2fb21dade7bce302b3e0246206423d3)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `tape` [`2c35c43`](https://github.com/inspect-js/is-regex/commit/2c35c437edf3eeb37129eea2404d8f465d27620f)
- [actions] switch Automatic Rebase workflow to `pull_request_target` event [`1009e25`](https://github.com/inspect-js/is-regex/commit/1009e259d49a63753dc6e79e2b876a30c00c6de6)
- [meta] gitignore coverage output [`3b5fa9e`](https://github.com/inspect-js/is-regex/commit/3b5fa9ed2882c65ee81dff979f79f1a2751d3772)
- [actions] update workflows [`1843ef6`](https://github.com/inspect-js/is-regex/commit/1843ef65b8b8c24a44e91bc4ed5ee60dffc31c2d)

## [v1.1.1](https://github.com/inspect-js/is-regex/compare/v1.1.0...v1.1.1) - 2020-08-03

### Commits

- [Performance] Re-add lastIndex check to improve performance [`d8495cd`](https://github.com/inspect-js/is-regex/commit/d8495cd22d475ddca250818921b6088f631c1972)
- [Dev Deps] update `auto-changelog`, `eslint` [`778fa6b`](https://github.com/inspect-js/is-regex/commit/778fa6b9d2b182ee6d73993e103532855e956f85)

## [v1.1.0](https://github.com/inspect-js/is-regex/compare/v1.0.5...v1.1.0) - 2020-06-03

### Commits

- [New] use `badStringifier`‑based RegExp detection [`31eff67`](https://github.com/inspect-js/is-regex/commit/31eff673243d65c3d6c05848c0eb52f5380f1be3)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `aud`, `tape` [`fc91458`](https://github.com/inspect-js/is-regex/commit/fc914588187b8bb00d8d792c84f06a6e15d883c1)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `tape`; add `safe-publish-latest` [`d43ed83`](https://github.com/inspect-js/is-regex/commit/d43ed83db54ea727bb0b1b77a50af79d1edb8a6d)
- [Dev Deps] update `auto-changelog`, `tape`; add `aud` [`56647d1`](https://github.com/inspect-js/is-regex/commit/56647d196be34ef3c118ad67726e75169fbcb875)
- [meta] only run `aud` on prod deps [`e0865b8`](https://github.com/inspect-js/is-regex/commit/e0865b8360b0ac1b9d17b7b81ae5f339e5c9036b)

## [v1.0.5](https://github.com/inspect-js/is-regex/compare/v1.0.4...v1.0.5) - 2019-12-15

### Commits

- [Tests] use shared travis-ci configs [`af728b2`](https://github.com/inspect-js/is-regex/commit/af728b21c5cc9e41234fb4015594bffdcfff597c)
- [Tests] remove `jscs` [`1b8cfe8`](https://github.com/inspect-js/is-regex/commit/1b8cfe8cfb14820c196775f19d370276e4034791)
- [meta] add `auto-changelog` [`c3131d8`](https://github.com/inspect-js/is-regex/commit/c3131d8ab5d06ea5fa05a4bb2ad28bbfb81668ad)
- [Tests] up to `node` `v8.1`, `v7.10`, `v6.11`, `v4.8`; newer npm fails on older nodes [`660b658`](https://github.com/inspect-js/is-regex/commit/660b6585d1a9607dbdae879b70ce2f6a5684616c)
- [Tests] up to `node` `v9.3`, `v8.9`, `v6.12`; use `nvm install-latest-npm`; pin included builds to LTS [`7c25218`](https://github.com/inspect-js/is-regex/commit/7c25218d540ab17c18e4ae333677c5725806a778)
- [Tests] up to `node` `v12.10`, `v11.15`, `v10.16`, `v8.16`, `v6.17` [`fa95547`](https://github.com/inspect-js/is-regex/commit/fa955478950a5ba0a920010d5daaa29487500b30)
- [meta] remove unused Makefile and associated utilities [`9fd2a29`](https://github.com/inspect-js/is-regex/commit/9fd2a29dc57ed125f3d61e94f6254a9dd8ee0044)
- [Tests] up to `node` `v11.3`, `v10.14`, `v8.14`, `v6.15` [`7f2ac41`](https://github.com/inspect-js/is-regex/commit/7f2ac41ef5dc4d53bfe2fb1c24486c688a2537bd)
- [Tests] up to `node` `v10.0`, `v9.11`, `v8.11`, `v6.14`, `v4.9` [`6fa2b0f`](https://github.com/inspect-js/is-regex/commit/6fa2b0fe171a5b02086a06679a92d989e83a8b8e)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config` [`697e1de`](https://github.com/inspect-js/is-regex/commit/697e1de1c9e69f08e591cc0040d81fdbbde6fe4e)
- [actions] add automatic rebasing / merge commit blocking [`ad86dc9`](https://github.com/inspect-js/is-regex/commit/ad86dc97a52e4f66fbfb3b8c9c78da3963588b54)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `jscs`, `nsp`, `replace`, `semver`, `tape` [`5c99c8e`](https://github.com/inspect-js/is-regex/commit/5c99c8e384d5ce2ef434be5853c301477cf35456)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `replace`, `semver`, `tape` [`bb63686`](https://github.com/inspect-js/is-regex/commit/bb63686a9d0fc586d121549cf484da95edec3b0a)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config@`, `replace`, `semver`, `tape` [`ddf3670`](https://github.com/inspect-js/is-regex/commit/ddf36705e5f7bd29832721e4a23abf06195032c6)
- [Dev Deps] update `tape`, `nsp`, `eslint`, `@ljharb/eslint-config` [`e7b5a62`](https://github.com/inspect-js/is-regex/commit/e7b5a626eef3b9648c7d52d4620ce2e2a98a9ab8)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `covert`, `tape` [`c803db5`](https://github.com/inspect-js/is-regex/commit/c803db5cd94cf9e0a559617adbc1e4c9d22007ff)
- [Tests] switch from `nsp` to `npm audit` [`b7239be`](https://github.com/inspect-js/is-regex/commit/b7239be9da263a0f7066f79d087eaf700a9613e9)
- [Dev Deps] update `eslint`, `nsp`, `semver`, `tape` [`347ee6c`](https://github.com/inspect-js/is-regex/commit/347ee6c67ba0f56b03f21a5abe743658f6515963)
- Only apps should have lockfiles. [`3866575`](https://github.com/inspect-js/is-regex/commit/38665755ecf028061f15816059e26023890a0dc7)
- [Tests] use `npx aud` instead of `nsp` or `npm audit` with hoops [`d099a39`](https://github.com/inspect-js/is-regex/commit/d099a3943eb7e156a3e64fb8b74e11d7c83a4bec)
- [meta] add `funding` field [`741aecd`](https://github.com/inspect-js/is-regex/commit/741aecd92cd49868b3606c8cc99ce299e5f3c7d5)
- [Tests] use `eclint` instead of `editorconfig-tools` [`bc6aa75`](https://github.com/inspect-js/is-regex/commit/bc6aa7539e506788709b96f7bf3d7549850a81c3)
- [Tests] on `node` `v10.1` [`262226f`](https://github.com/inspect-js/is-regex/commit/262226f08fa34dff9a8dffd16daabb3dc6e262eb)
- [Dev Deps] update `eslint` [`31fd719`](https://github.com/inspect-js/is-regex/commit/31fd719dd59a6111ca710cdb0d19a8adadf9b8c6)
- [Deps] update `has` [`e9e25a3`](https://github.com/inspect-js/is-regex/commit/e9e25a3de7e89faaa6aadf5010477074140e8218)
- [Dev Deps] update `replace` [`aeeb968`](https://github.com/inspect-js/is-regex/commit/aeeb968bf5a4fc07f0fa6905f2c699fc563b6c32)
- [Tests] set audit level [`2a6290e`](https://github.com/inspect-js/is-regex/commit/2a6290e78b58bf14b734d7998fe53b4a84db5e44)
- [Tests] remove `nsp` [`fc74c2b`](https://github.com/inspect-js/is-regex/commit/fc74c2bb6970a7f3280abe6eff3b492d77d89c9f)

## [v1.0.4](https://github.com/inspect-js/is-regex/compare/v1.0.3...v1.0.4) - 2017-02-18

### Fixed

- [Fix] ensure that `lastIndex` is not mutated [`#3`](https://github.com/inspect-js/is-regex/issues/3)

### Commits

- Update `eslint`, `tape`, `semver`; use my personal shared `eslint` config [`c4a41c3`](https://github.com/inspect-js/is-regex/commit/c4a41c3a8203a3919b01cd0d1b577daadf30a452)
- [Tests] on all node minors; improve test matrix [`58d7508`](https://github.com/inspect-js/is-regex/commit/58d7508a36eb92bd76717486b9e78bde502ffe3e)
- [Dev Deps] update `tape`, `jscs`, `nsp`, `eslint`, `@ljharb/eslint-config`, `semver` [`7290076`](https://github.com/inspect-js/is-regex/commit/729007606e9ed162953d1f5812c37eb06c554ec2)
- Update `covert`, `jscs`, `eslint`, `semver` [`dabc729`](https://github.com/inspect-js/is-regex/commit/dabc729cfc4458264c6f7642004d41dd5c214bfd)
- Update `eslint` [`a946b05`](https://github.com/inspect-js/is-regex/commit/a946b051159396b4311c564880f96e3d00e8b8e2)
- Update `tape`, `jscs`, `eslint`, `@ljharb/eslint-config` [`1744dde`](https://github.com/inspect-js/is-regex/commit/1744dde77526841f216fa2c1c866c5a82b1638c0)
- [Refactor] when try/catch is needed, bail early if the value lacks an own `lastIndex` data property. [`288ad93`](https://github.com/inspect-js/is-regex/commit/288ad93dbfed9f6828de20de67105ee6d6504425)
- Update `editorconfig-tools`, `eslint`, `semver`, `replace` [`4d895c6`](https://github.com/inspect-js/is-regex/commit/4d895c68a0cdbb5803185928963c15147aad0404)
- Update `eslint`, `tape`, `semver` [`f387f03`](https://github.com/inspect-js/is-regex/commit/f387f03b260b56372bfca301d4e79c4067633854)
- All grade A-supported `node`/`iojs` versions now ship with an `npm` that understands `^`. [`55e480f`](https://github.com/inspect-js/is-regex/commit/55e480f407cafb6c21a6c32aef04ccaa3ba4216c)
- [Dev Deps] update `jscs`, `nsp`, `eslint`, `@ljharb/eslint-config`, `semver` [`89d9528`](https://github.com/inspect-js/is-regex/commit/89d95285b364913ebcd8ac7e0872570fe009a5d3)
- [Dev Deps] update `jscs` [`eb222a8`](https://github.com/inspect-js/is-regex/commit/eb222a8435e59909354f3700fd4880e4ce1cb13e)
- [Tests] up to `io.js` `v3.3`, `node` `v4.1` [`c65429c`](https://github.com/inspect-js/is-regex/commit/c65429cea0366508c10ad2ab773af7b83a34fc81)
- Update `nsp`, `eslint` [`c60fbd8`](https://github.com/inspect-js/is-regex/commit/c60fbd8680f7fb3508ec3c5be8ebb788672516c8)
- Update `eslint`, `semver` [`6a62116`](https://github.com/inspect-js/is-regex/commit/6a621168c63616bf004ca8b1f885b4eb8a58a3e5)
- [Tests] on `node` `v7.5`, `v4.7` [`e764651`](https://github.com/inspect-js/is-regex/commit/e764651336f5da5e239e9fe8869f3a3201c19d2b)
- Test up to `io.js` `v2.1` [`3bf326a`](https://github.com/inspect-js/is-regex/commit/3bf326a9bcd530fd16c9fc806e249a68e25ab7e3)
- Test on the latest `io.js` versions. [`693d047`](https://github.com/inspect-js/is-regex/commit/693d0477631c5d7671f6c99eca5594ffffa75771)
- [Refactor] use an early return instead of a ternary. [`31eaca2`](https://github.com/inspect-js/is-regex/commit/31eaca28b7d0aaac0599fe7a569b93b842f8ab16)
- Test on `io.js` `v2.2` [`c18c55a`](https://github.com/inspect-js/is-regex/commit/c18c55aee6358d70531f935e98851e42b698d93c)
- Run `travis-ci` tests on `iojs` and `node` v0.12; speed up builds; allow 0.8 failures. [`a1c237d`](https://github.com/inspect-js/is-regex/commit/a1c237d35f880fe0bcbc9275254611a6a2300aaf)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config` [`aa3ea0f`](https://github.com/inspect-js/is-regex/commit/aa3ea0f148af31d75f7ef8a800412729d82def04)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config` [`d97831d`](https://github.com/inspect-js/is-regex/commit/d97831d0e2ccd3d00d1f7354b7f81e2575f90953)
- [Dev Deps] Update `tape`, `eslint` [`95e6def`](https://github.com/inspect-js/is-regex/commit/95e6defe3178c45dc9df16e474e558979d5f5c05)
- Update `eslint`, `nsp` [`3844c93`](https://github.com/inspect-js/is-regex/commit/3844c935cfe7c52fae0dc74d27e884c417cb4616)
- Update `tape`, `jscs` [`0d6dac8`](https://github.com/inspect-js/is-regex/commit/0d6dac818ed251910171965932f021291919e770)
- Fix tests for faked @@toStringTag [`2ebef9f`](https://github.com/inspect-js/is-regex/commit/2ebef9f0759843e9a063de7a512b46e3e7daea7e)
- Test up to `io.js` `v3.0` [`ec1d2d4`](https://github.com/inspect-js/is-regex/commit/ec1d2d44481fa0fa11448527da8030c99fe47a12)
- [Refactor] bail earlier when the value is falsy. [`a9e333e`](https://github.com/inspect-js/is-regex/commit/a9e333e2ac8912ca05b7e31d30e4eea683c0da4b)
- [Dev Deps] update `tape` [`8cdcaae`](https://github.com/inspect-js/is-regex/commit/8cdcaae07be8c790cdb99849e6076ea7702a4c84)
- Switch from vb.teelaun.ch to versionbadg.es for the npm version badge SVG. [`281c4ef`](https://github.com/inspect-js/is-regex/commit/281c4efeb71c86dd380e741bcaee3f7dbf956151)
- Test on `io.js` `v2.4` [`4d54c68`](https://github.com/inspect-js/is-regex/commit/4d54c68a81b5332a3b76259d8aa8f514be5efd13)
- Test on `io.js` `v2.3` [`23170f5`](https://github.com/inspect-js/is-regex/commit/23170f5cae632d0377de73bd2febc53db8aebbc9)
- Test on `iojs-v1.6` [`4487ad0`](https://github.com/inspect-js/is-regex/commit/4487ad0194a5684223bfa2690da4e0a441f7132a)

## [v1.0.3](https://github.com/inspect-js/is-regex/compare/v1.0.2...v1.0.3) - 2015-01-29

### Commits

- Update npm run scripts. [`dc528dd`](https://github.com/inspect-js/is-regex/commit/dc528dd25e775089bc0a3f5a8f7ae7ffc4cdf52a)
- Add toStringTag tests. [`f48a83a`](https://github.com/inspect-js/is-regex/commit/f48a83a78720b78ab60ca586c16f6f3dbcfec825)
- If @@toStringTag is not present, use the old-school Object#toString test. [`50b0ffd`](https://github.com/inspect-js/is-regex/commit/50b0ffd9c7fdbd54aee8cde1b07e680ae84f6a0d)

## [v1.0.2](https://github.com/inspect-js/is-regex/compare/v1.0.1...v1.0.2) - 2015-01-29

### Commits

- `make release` [`a1de7ec`](https://github.com/inspect-js/is-regex/commit/a1de7eca4cecc8015fd27804669f8fc61bd16a68)
- Improve optimization by separating the try/catch, and bailing out early when not typeof "object". [`5ab7632`](https://github.com/inspect-js/is-regex/commit/5ab76322a348487fa8b16761e83f6824c3c27d11)

## [v1.0.1](https://github.com/inspect-js/is-regex/compare/v1.0.0...v1.0.1) - 2015-01-28

### Commits

- Using my standard jscs.json file [`1f1733a`](https://github.com/inspect-js/is-regex/commit/****************************************)
- Adding `npm run lint` [`51ea70f`](https://github.com/inspect-js/is-regex/commit/51ea70fa7e461d022f611c195f343ea8d0333d71)
- Use RegExp#exec to test if something is a regex, which works even with ES6 @@toStringTag. [`042c8c7`](https://github.com/inspect-js/is-regex/commit/042c8c734faade9015932b61f1e8ea4f3a93b1b3)
- Adding license and downloads badges [`366d619`](https://github.com/inspect-js/is-regex/commit/366d61965d3a4119126e78e09b2166bbcddd0c5a)
- Use SVG badges instead of PNG [`6a32e4f`](https://github.com/inspect-js/is-regex/commit/6a32e4fc87d7d3a3787b800dd033c9293aead6df)
- Update `tape`, `jscs` [`f1b9462`](https://github.com/inspect-js/is-regex/commit/f1b9462f86d1b69de07176e7f277f668757ba964)
- Update `jscs` [`1bff23f`](https://github.com/inspect-js/is-regex/commit/1bff23ff0fe88c8263e8bf04cf99e290af96d5b0)
- Update `tape`, `jscs` [`c22ea2e`](https://github.com/inspect-js/is-regex/commit/c22ea2e7967f45618deed01ff5ea483f918be216)
- Update `tape`, `jscs` [`b0479db`](https://github.com/inspect-js/is-regex/commit/b0479db99a1b1b872d1618fb0a71f0c74a78b29b)
- Use consistent quotes [`1a6e347`](https://github.com/inspect-js/is-regex/commit/1a6e34730d9270f3f20519139faa4c4e6ec2e1f5)
- Make travis builds faster. [`090a4ea`](https://github.com/inspect-js/is-regex/commit/090a4ea7c5fa709d108d596e3bc304e6ce973dec)
- Update `tape` [`7d76129`](https://github.com/inspect-js/is-regex/commit/7d7612928bdd43230fbd835db71797249ca24f35)
- Lock covert to v1.0.0. [`9a90b03`](https://github.com/inspect-js/is-regex/commit/9a90b03fb390e66f874223a34c58ba2bb109edd3)
- Updating tape [`bfbc7f5`](https://github.com/inspect-js/is-regex/commit/bfbc7f593a007acd0411152bbb55f724dc4ca935)
- Updating jscs [`13ad511`](https://github.com/inspect-js/is-regex/commit/13ad511d80cd67300c2c0c5387fc4b3b423e2768)
- Updating jscs [`cda1945`](https://github.com/inspect-js/is-regex/commit/cda1945d603dfe99e24d5a909a931d366451bc4d)
- Updating jscs [`de96c99`](https://github.com/inspect-js/is-regex/commit/de96c99d4bf5787df671de6df9138b6547a6545b)
- Running linter as part of tests [`2cb6567`](https://github.com/inspect-js/is-regex/commit/2cb656733b1ed0af14ad11fb584006d22de0c69d)
- Updating covert [`a56ae74`](https://github.com/inspect-js/is-regex/commit/a56ae74ec8d5f0473295a8b10519a18580f16624)
- Updating tape [`ffe47f7`](https://github.com/inspect-js/is-regex/commit/ffe47f7fe9cf6d16896b4bdc286bd1d0805d5c49)

## [v1.0.0](https://github.com/inspect-js/is-regex/compare/v0.0.0...v1.0.0) - 2014-05-19

### Commits

- Make sure old and unstable nodes don't break Travis [`05da747`](https://github.com/inspect-js/is-regex/commit/05da7478f960dc131ec3ad864e27e8c6b7d74a80)
- toString is a reserved var name in old Opera [`885c48c`](https://github.com/inspect-js/is-regex/commit/885c48c120f921a55f1842b0607d3e7875379821)
- Updating deps [`2ca0e79`](https://github.com/inspect-js/is-regex/commit/2ca0e79a2443ca34d85e8b2ea2e26f55855b74a7)
- Updating tape. [`9678435`](https://github.com/inspect-js/is-regex/commit/96784355611deb0c23b9064be774216d76e3e457)
- Updating covert [`c3bb898`](https://github.com/inspect-js/is-regex/commit/c3bb8985a422e3e0c81f9c43899b6c19a72c755f)
- Updating tape [`7811708`](https://github.com/inspect-js/is-regex/commit/78117089688258b8f939b397b37897b5b3e30f74)
- Testing on node 0.6 again [`dec36ae`](https://github.com/inspect-js/is-regex/commit/dec36ae58a39a3f80e832b702c3e19406363c160)
- Run code coverage as part of tests [`e6f4ebe`](https://github.com/inspect-js/is-regex/commit/e6f4ebec26894543747603f2cb360e839f2ca290)

## v0.0.0 - 2014-01-15

### Commits

- package.json [`aa60d43`](https://github.com/inspect-js/is-regex/commit/aa60d43d2c8adb9fdd47f5898e5e1e570bd238d8)
- read me [`861e944`](https://github.com/inspect-js/is-regex/commit/861e944de88e84010eaa662ea9ea9f17c90cff8c)
- Initial commit [`d0cdd71`](https://github.com/inspect-js/is-regex/commit/d0cdd71a637d8490b7ee3eaaf75c7e31d0f9242f)
- Tests. [`b533f74`](https://github.com/inspect-js/is-regex/commit/b533f741a88dff002790fb7af054b2a74e72d4da)
- Implementation. [`3c9a8c0`](https://github.com/inspect-js/is-regex/commit/3c9a8c06994003cdfffeb3620f251f4c4cae7755)
- Travis CI [`742c440`](https://github.com/inspect-js/is-regex/commit/742c4407015f9108875fd108fde137f5245e9e7a)
