{"version": 3, "names": ["React", "createContext", "undefined"], "sources": ["ReanimatedTransitionProgressContext.tsx"], "sourcesContent": ["import * as React from 'react';\n// @ts-ignore file to be used only if `react-native-reanimated` available in the project\nimport Animated from 'react-native-reanimated';\n\ntype ReanimatedTransitionProgressContextBody = {\n  progress: Animated.SharedValue<number>;\n  closing: Animated.SharedValue<number>;\n  goingForward: Animated.SharedValue<number>;\n};\n\nexport default React.createContext<\n  ReanimatedTransitionProgressContextBody | undefined\n>(undefined);\n"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B;;AASA,4BAAeA,KAAK,CAACC,aAAa,CAEhCC,SAAS,CAAC"}