{"version": 3, "names": ["React", "ReanimatedHeaderHeightContext", "useReanimatedHeaderHeight", "height", "useContext", "undefined", "Error"], "sources": ["useReanimatedHeaderHeight.tsx"], "sourcesContent": ["import * as React from 'react';\nimport ReanimatedHeaderHeightContext from './ReanimatedHeaderHeightContext';\n\nexport default function useReanimatedHeaderHeight() {\n  const height = React.useContext(ReanimatedHeaderHeightContext);\n\n  if (height === undefined) {\n    throw new Error(\n      \"Couldn't find the header height using Reanimated. Are you inside a screen in a navigator with a header and your NavigationContainer is wrapped in ReanimatedScreenProvider?\"\n    );\n  }\n\n  return height;\n}\n"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAOC,6BAA6B,MAAM,iCAAiC;AAE3E,eAAe,SAASC,yBAAyBA,CAAA,EAAG;EAClD,MAAMC,MAAM,GAAGH,KAAK,CAACI,UAAU,CAACH,6BAA6B,CAAC;EAE9D,IAAIE,MAAM,KAAKE,SAAS,EAAE;IACxB,MAAM,IAAIC,KAAK,CACb,6KACF,CAAC;EACH;EAEA,OAAOH,MAAM;AACf"}