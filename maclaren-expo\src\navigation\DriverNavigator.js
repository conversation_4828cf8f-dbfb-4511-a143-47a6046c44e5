/**
 * Navegador para Conductores - MACLAREN Expo
 */

import React from 'react';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import { Text, StyleSheet } from 'react-native';

// Pantallas temporales simples
import HomeScreen from '../screens/driver/HomeScreen';
import EarningsScreen from '../screens/driver/EarningsScreen';
import ProfileScreen from '../screens/driver/ProfileScreen';

const Tab = createBottomTabNavigator();

const DriverNavigator = ({ onLogout }) => {
  return (
    <Tab.Navigator
      screenOptions={({ route }) => ({
        headerShown: false,
        tabBarStyle: styles.tabBar,
        tabBarActiveTintColor: '#ff6b35',
        tabBarInactiveTintColor: '#999',
        tabBarLabelStyle: styles.tabBarLabel,
        tabBarIcon: ({ focused, color }) => {
          let iconText = '';
          
          switch (route.name) {
            case 'Home':
              iconText = '🏍️';
              break;
            case 'Earnings':
              iconText = '💰';
              break;
            case 'Profile':
              iconText = '👤';
              break;
          }
          
          return (
            <Text style={[styles.tabIcon, { color: focused ? '#ff6b35' : '#999' }]}>
              {iconText}
            </Text>
          );
        },
      })}>
      
      <Tab.Screen 
        name="Home" 
        component={HomeScreen}
        options={{
          tabBarLabel: 'Solicitudes',
        }}
      />
      
      <Tab.Screen 
        name="Earnings" 
        component={EarningsScreen}
        options={{
          tabBarLabel: 'Ganancias',
        }}
      />
      
      <Tab.Screen 
        name="Profile" 
        options={{
          tabBarLabel: 'Perfil',
        }}>
        {() => <ProfileScreen onLogout={onLogout} />}
      </Tab.Screen>
      
    </Tab.Navigator>
  );
};

const styles = StyleSheet.create({
  tabBar: {
    backgroundColor: 'white',
    borderTopWidth: 1,
    borderTopColor: '#e9ecef',
    paddingBottom: 5,
    paddingTop: 5,
    height: 60,
  },
  tabBarLabel: {
    fontSize: 12,
    fontWeight: '600',
    marginTop: 2,
  },
  tabIcon: {
    fontSize: 24,
    marginBottom: 2,
  },
});

export default DriverNavigator;
