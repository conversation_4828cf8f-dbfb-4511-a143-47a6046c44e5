/**
 * Navegador para Pasajeros - MACLAREN
 * Maneja la navegación entre pantallas del pasajero
 */

import React from 'react';
import {createBottomTabNavigator} from '@react-navigation/bottom-tabs';
import {createStackNavigator} from '@react-navigation/stack';
import {Text, StyleSheet} from 'react-native';

// Importar pantallas
import HomeScreen from '../screens/passenger/HomeScreen';
import TripsScreen from '../screens/passenger/TripsScreen';
import ProfileScreen from '../screens/passenger/ProfileScreen';
import ChatScreen from '../screens/shared/ChatScreen';
import MapScreen from '../screens/shared/MapScreen';

const Tab = createBottomTabNavigator();
const Stack = createStackNavigator();

// Stack Navigator para Home (incluye chat y mapa)
const HomeStack = () => {
  return (
    <Stack.Navigator screenOptions={{headerShown: false}}>
      <Stack.Screen name="HomeMain" component={HomeScreen} />
      <Stack.Screen name="Chat" component={ChatScreen} />
      <Stack.Screen name="Map" component={MapScreen} />
    </Stack.Navigator>
  );
};

// Stack Navigator para Viajes
const TripsStack = () => {
  return (
    <Stack.Navigator screenOptions={{headerShown: false}}>
      <Stack.Screen name="TripsMain" component={TripsScreen} />
      <Stack.Screen name="TripDetails" component={ChatScreen} />
    </Stack.Navigator>
  );
};

// Stack Navigator para Perfil
const ProfileStack = () => {
  return (
    <Stack.Navigator screenOptions={{headerShown: false}}>
      <Stack.Screen name="ProfileMain" component={ProfileScreen} />
    </Stack.Navigator>
  );
};

interface PassengerNavigatorProps {
  onLogout: () => void;
}

const PassengerNavigator: React.FC<PassengerNavigatorProps> = ({onLogout}) => {
  return (
    <Tab.Navigator
      screenOptions={({route}) => ({
        headerShown: false,
        tabBarStyle: styles.tabBar,
        tabBarActiveTintColor: '#ff6b35',
        tabBarInactiveTintColor: '#999',
        tabBarLabelStyle: styles.tabBarLabel,
        tabBarIcon: ({focused, color}) => {
          let iconText = '';
          
          switch (route.name) {
            case 'Home':
              iconText = '🏠';
              break;
            case 'Trips':
              iconText = '🚗';
              break;
            case 'Profile':
              iconText = '👤';
              break;
          }
          
          return (
            <Text style={[styles.tabIcon, {color: focused ? '#ff6b35' : '#999'}]}>
              {iconText}
            </Text>
          );
        },
      })}>
      
      <Tab.Screen 
        name="Home" 
        component={HomeStack}
        options={{
          tabBarLabel: 'Inicio',
        }}
      />
      
      <Tab.Screen 
        name="Trips" 
        component={TripsStack}
        options={{
          tabBarLabel: 'Mis Viajes',
        }}
      />
      
      <Tab.Screen 
        name="Profile" 
        options={{
          tabBarLabel: 'Perfil',
        }}>
        {() => <ProfileScreen onLogout={onLogout} />}
      </Tab.Screen>
      
    </Tab.Navigator>
  );
};

const styles = StyleSheet.create({
  tabBar: {
    backgroundColor: 'white',
    borderTopWidth: 1,
    borderTopColor: '#e9ecef',
    paddingBottom: 5,
    paddingTop: 5,
    height: 60,
  },
  tabBarLabel: {
    fontSize: 12,
    fontWeight: '600',
    marginTop: 2,
  },
  tabIcon: {
    fontSize: 24,
    marginBottom: 2,
  },
});

export default PassengerNavigator;
